import 'dart:math';

import 'package:flutter/material.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';
import 'package:praja_posters/src/extensions/poster_gradient_extension.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/ui_widgets/praja_poster_image.dart';

class PolygonalProfileWidget extends StatelessWidget {
  final String imageUrl;
  final PosterGradient? badgeBannerGradients;
  final PosterGradient footerGradients;
  final PosterGradient? upperFooterGradients;
  final PosterGradient? borderGradients;
  final bool isGoldenFrame;
  final double polygonHeight;
  final double polygonWidth;

  const PolygonalProfileWidget({
    super.key,
    required this.imageUrl,
    this.badgeBannerGradients,
    required this.footerGradients,
    this.upperFooterGradients,
    this.borderGradients,
    required this.isGoldenFrame,
    required this.polygonHeight,
    required this.polygonWidth,
  });

  Widget _getPolygonContainer() {
    final borderGradients = this.borderGradients;
    final upperFooterGradients = this.upperFooterGradients;
    final footerGradients = this.footerGradients;

    if (borderGradients == null) {
      return const SizedBox();
    }

    return Stack(
      clipBehavior: Clip.none,
      children: [
        SizedBox(
          height: polygonHeight + 6,
          width: polygonWidth + 6,
          child: PolygonTextureBG(
            backgroundGradient: borderGradients,
          ),
        ),
        Positioned(
          top: 3,
          left: 3,
          child: SizedBox(
            height: polygonHeight,
            width: polygonWidth,
            child: PolygonTextureBG(
              backgroundGradient:
                  (isGoldenFrame && upperFooterGradients != null)
                      ? upperFooterGradients
                      : footerGradients,
            ),
          ),
        ),
      ],
    );
  }

  Widget _getBackgroundContainer() {
    final badgeBannerGradients = this.badgeBannerGradients;
    final borderGradients = this.borderGradients;
    if (badgeBannerGradients == null || borderGradients == null) {
      return const SizedBox();
    }
    return Container(
      height: polygonHeight - 18,
      width: polygonWidth - 18,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: badgeBannerGradients.toGradient(),
        border: GradientBoxBorder(
          gradient: borderGradients.toGradient(),
          width: 2,
        ),
      ),
    );
  }

  Widget _getProfileContainer() {
    return Container(
      height: polygonHeight - 40,
      width: polygonWidth - 40,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: footerGradients.toGradient(),
        border: GradientBoxBorder(
          gradient: footerGradients.toGradient(
              transform: const GradientRotation(-0.8)),
          width: 4,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 2.0),
        child: ClipOval(
          child: PrajaPosterImage(
            imageUrl: imageUrl,
            placeholder: (_, url) => const SizedBox(),
            fadeInDuration: const Duration(milliseconds: 0),
            fit: BoxFit.contain,
            alignment: Alignment.topCenter,
          ),
        ),
      ),
    );
  }

  Widget _getBody() {
    return Stack(
      clipBehavior: Clip.none,
      alignment: AlignmentDirectional.center,
      children: [
        Transform.rotate(
          angle: -30 * (pi / 180),
          child: _getPolygonContainer(),
        ),
        Transform.rotate(
          angle: -60 * (pi / 180),
          child: _getPolygonContainer(),
        ),
        Transform.rotate(
          angle: -75 * (pi / 180),
          child: _getPolygonContainer(),
        ),
        Transform.rotate(
          angle: -105 * (pi / 180),
          child: _getPolygonContainer(),
        ),
        _getBackgroundContainer(),
        _getProfileContainer(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return _getBody();
  }
}

class PolygonTextureBG extends StatelessWidget {
  final PosterGradient backgroundGradient;
  const PolygonTextureBG({super.key, required this.backgroundGradient});

  @override
  Widget build(BuildContext context) {
    return ClipPath(
      clipper: _CustomPolygonClipper(),
      child: Container(
        height: double.infinity,
        width: double.infinity,
        decoration: BoxDecoration(
          gradient: backgroundGradient.toGradient(),
        ),
      ),
    );
  }
}

class _CustomPolygonClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.moveTo(size.width / 2, 0);
    path.lineTo(10, size.height * 0.25);
    path.lineTo(10, size.height * 0.75);
    path.lineTo(size.width / 2, size.height);
    path.lineTo(size.width - 10, size.height * 0.75);
    path.lineTo(size.width - 10, size.height * 0.25);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
