import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:praja_posters/src/app_poster_cache_manager.dart';
import 'package:praja_posters/src/extensions/cors_supported_image_url.dart';

class PrajaPosterImage extends StatelessWidget {
  final String imageUrl;
  final BoxFit? fit;
  final double? width;
  final double? height;
  final Alignment alignment;
  final ImageWidgetBuilder? imageBuilder;
  final Duration fadeInDuration;
  final PlaceholderWidgetBuilder? placeholder;
  final LoadingErrorWidgetBuilder? errorWidget;

  const PrajaPosterImage({
    super.key,
    required this.imageUrl,
    this.fit,
    this.width,
    this.height,
    this.alignment = Alignment.center,
    this.imageBuilder,
    this.fadeInDuration = const Duration(milliseconds: 500),
    this.placeholder,
    this.errorWidget,
  });

  Widget getImageWidget() {
    if (kIsWeb) {
      return Image.network(
        imageUrl.toCorsSupported(),
        fit: fit,
        width: width,
        height: height,
        alignment: alignment,
      );
    } else if (defaultTargetPlatform == TargetPlatform.android ||
        defaultTargetPlatform == TargetPlatform.iOS) {
      return CachedNetworkImage(
        imageUrl: imageUrl,
        width: width,
        height: height,
        alignment: alignment,
        fit: fit,
        cacheManager: AppPosterCacheManager.instance,
        imageBuilder: imageBuilder,
        fadeInDuration: fadeInDuration,
        placeholder: placeholder,
        errorWidget: errorWidget,
      );
    } else {
      return Image.network(
        imageUrl.toCorsSupported(),
        fit: fit,
        width: width,
        height: height,
        alignment: alignment,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return getImageWidget();
  }
}
