import 'dart:math';

import 'package:flutter/material.dart';

class PartyBgTexture extends StatelessWidget {
  const PartyBgTexture({super.key});

  @override
  Widget build(BuildContext context) {
    /*
    linear-gradient(
      165.06deg,
      rgba(255, 255, 255, 0.9) 9.14%,
      rgba(254, 254, 254, 0.873) 9.14%,
      rgba(253, 253, 253, 0.801) 12.3%,
      rgba(250, 250, 250, 0.675) 15.68%,
      rgba(247, 247, 247, 0.504) 32.06%,
      rgba(243, 243, 243, 0.288) 41.41%,
      rgba(242, 242, 242, 0.18) 49.99%,
      rgba(233, 233, 233, 0.171) 65.51%,
      rgba(206, 206, 206, 0.144) 77.77%,
      rgba(162, 162, 162, 0.099) 89.21%,
      rgba(153, 153, 153, 0.09) 90.84%)
    */
    return Opacity(
        opacity: 0.6,
        child: Container(
            decoration: const BoxDecoration(
          shape: BoxShape.circle,
          gradient: LinearGradient(
            colors: [
              Color(0xE6FFFFFF),
              Color(0xDEFEFEFE),
              Color(0xCCFDFDFD),
              Color(0xA9FAFAFA),
              Color(0x80F7F7F7),
              Color(0x49F3F3F3),
              Color(0x2DE2E2E2),
              Color(0x2CE9E9E9),
              Color(0x249C9C9C),
              Color(0x191A0A0A),
              Color(0x17191919),
            ],
            stops: [
              0.0914,
              0.0914,
              0.123,
              0.1568,
              0.3206,
              0.4141,
              0.4999,
              0.6551,
              0.7777,
              0.8921,
              0.9084,
            ],
            begin: Alignment(-0.5, -0.5),
            end: Alignment(0.5, 0.5),
            transform: GradientRotation(-pi / 4),
          ),
        )));
  }
}
