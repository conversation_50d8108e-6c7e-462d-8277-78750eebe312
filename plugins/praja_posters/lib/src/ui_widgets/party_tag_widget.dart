import 'package:flutter/material.dart';
import 'package:praja_posters/src/enums/party_icon_alignment.dart';
import 'package:praja_posters/src/extensions/poster_gradient_extension.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/ui_widgets/praja_poster_image.dart';

class PartyTagWidget extends StatelessWidget {
  final String iconUrl;
  final PosterPartyIconAlignment alignment;
  final PosterGradient gradients;

  final PartyTagClipper clipper;
  PartyTagWidget({
    super.key,
    required this.iconUrl,
    required this.alignment,
    required this.gradients,
  }) : clipper = PartyTagClipper(alignment);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        width: alignment == PosterPartyIconAlignment.left ? 88 : 96,
        height: alignment == PosterPartyIconAlignment.left ? 82 : 102,
        child: CustomPaint(
          painter: CustomPathShadowPainter(
            clipper: clipper,
            elevation: 4,
          ),
          child: ClipPath(
            clipper: clipper,
            child: Container(
              decoration: BoxDecoration(
                gradient: gradients.toGradient(),
              ),
              padding: EdgeInsets.only(
                  left: alignment == PosterPartyIconAlignment.left ? 0 : 10,
                  bottom: alignment == PosterPartyIconAlignment.left ? 10 : 10,
                  right: alignment == PosterPartyIconAlignment.left ? 10 : 0),
              child: Center(
                  child: PrajaPosterImage(
                imageUrl: iconUrl,
                fadeInDuration: Duration.zero,
                width: alignment == PosterPartyIconAlignment.left ? 48 : 60,
                height: alignment == PosterPartyIconAlignment.left ? 48 : 60,
                fit: BoxFit.fitWidth,
                alignment: Alignment.topCenter,
              )),
            ),
          ),
        ));
  }
}

class PartyTagClipper extends CustomClipper<Path> {
  final PosterPartyIconAlignment alignment;
  const PartyTagClipper(this.alignment);

  @override
  Path getClip(Size size) {
    final radius = alignment == PosterPartyIconAlignment.left
        ? size.height / 10
        : size.width / 10;

    final path = Path();
    if (alignment == PosterPartyIconAlignment.left) {
      path.moveTo(0, size.height);
      path.arcToPoint(Offset(radius, size.height - radius),
          radius: Radius.circular(radius));
      path.lineTo(size.width * 0.75, size.height - radius);
      path.lineTo(size.width, (size.height - radius) / 2);
      path.lineTo(size.width * 0.75, 0);
      path.lineTo(radius, 0);
      path.arcToPoint(Offset(0, radius),
          radius: Radius.circular(radius), clockwise: false);
      path.lineTo(0, size.height);
      path.close();
    } else {
      path.moveTo(0, 0);
      path.arcToPoint(Offset(radius, radius), radius: Radius.circular(radius));
      path.lineTo(radius, size.height * 0.75);
      path.lineTo(radius + ((size.width - radius) / 2), size.height);
      path.lineTo(size.width, size.height * 0.75);
      path.lineTo(size.width, radius);
      path.arcToPoint(Offset(size.width - radius, 0),
          radius: Radius.circular(radius), clockwise: false);
      path.lineTo(0, 0);
    }
    path.close();
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) => false;
}

const ycpIconUrl =
    'https://g-cdn.thecircleapp.in/fit-in/1024x1024/filters:quality(80)/production/photos/41/b0149ff55f4649c16b4b33f7893055dd.png';

class PartyTagDemo extends StatelessWidget {
  const PartyTagDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        PartyTagWidget(
            iconUrl: ycpIconUrl,
            alignment: PosterPartyIconAlignment.top,
            gradients: ycpPartyGradients),
        const SizedBox(height: 16),
        PartyTagWidget(
            iconUrl: ycpIconUrl,
            alignment: PosterPartyIconAlignment.left,
            gradients: ycpPartyGradients),
      ],
    );
  }
}

class CustomPathShadowPainter extends CustomPainter {
  final Color shadowColor;
  final double elevation;
  final CustomClipper<Path> clipper;

  const CustomPathShadowPainter({
    required this.clipper,
    this.shadowColor = const Color(0xFF000000),
    this.elevation = 4,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final stickerPath = clipper.getClip(size);
    canvas.drawShadow(stickerPath, shadowColor, elevation, false);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
