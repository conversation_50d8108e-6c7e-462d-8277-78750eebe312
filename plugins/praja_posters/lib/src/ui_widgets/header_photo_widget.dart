import 'package:flutter/material.dart';
import 'package:praja_posters/src/enums/poster_header_background_type.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/models/poster_layout.dart';
import 'package:praja_posters/src/ui_widgets/party_bg_texture.dart';
import 'package:praja_posters/src/ui_widgets/praja_poster_image.dart';
import 'package:praja_posters/src/extensions/poster_gradient_extension.dart';

class HeaderPhotoWidget extends StatelessWidget {
  final double radius;
  final PosterHeaderBackgroundType backgroundType;
  final PosterGradient? backgroundGradients;
  final Widget photo;

  const HeaderPhotoWidget(
      {super.key,
      required this.radius,
      required this.backgroundType,
      this.backgroundGradients,
      required this.photo});

  factory HeaderPhotoWidget.fromHeaderPhoto(PosterHeaderPhoto headerPhoto,
      {required PosterHeaderBackgroundType backgroundType,
      PosterGradient? backgroundGradients}) {
    return HeaderPhotoWidget(
      radius: headerPhoto.radius,
      backgroundType: backgroundType,
      backgroundGradients: backgroundGradients,
      photo: PrajaPosterImage(
        imageUrl: headerPhoto.photoUrl,
        imageBuilder: (context, imageProvider) => Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(
                backgroundType == PosterHeaderBackgroundType.transparent
                    ? 0
                    : headerPhoto.radius),
            image: DecorationImage(
              image: imageProvider,
              fit: BoxFit.cover,
            ),
          ),
        ),
        fadeInDuration: Duration.zero,
        placeholder: (context, url) => const Center(
          child: SizedBox(),
        ),
        errorWidget: (context, url, error) => const Icon(Icons.error),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (backgroundType == PosterHeaderBackgroundType.sticker) {
      return SizedBox.square(
          dimension: radius * 2,
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: backgroundGradients?.toGradient(),
            ),
            child: Padding(
                padding: const EdgeInsets.all(4),
                child: ClipRRect(
                    borderRadius: BorderRadius.circular(radius - 8),
                    child: Stack(children: [
                      const PartyBgTexture(),
                      photo,
                    ]))),
          ));
    } else if (backgroundType == PosterHeaderBackgroundType.transparent) {
      return ShaderMask(
        shaderCallback: (bounds) {
          return const RadialGradient(
            center: Alignment.topCenter,
            radius: 1.0,
            colors: [Colors.black, Colors.transparent],
            stops: [0.8, 1.0],
          ).createShader(bounds);
        },
        blendMode: BlendMode.dstIn,
        child: SizedBox.square(dimension: radius * 2, child: photo),
      );
    } else {
      return SizedBox.square(
          dimension: radius * 2,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(radius),
            child: Stack(children: [
              Positioned.fill(
                child: Opacity(
                  opacity: 1.0,
                  child: backgroundType == PosterHeaderBackgroundType.dark
                      ? Container(
                          decoration:
                              const BoxDecoration(gradient: _darkGradient))
                      : Container(
                          decoration:
                              const BoxDecoration(gradient: _lightGradient)),
                ),
              ),
              Positioned.fill(
                  child: ShaderMask(
                      shaderCallback: (bounds) {
                        return const RadialGradient(
                          center: Alignment.topCenter,
                          radius: 1.0,
                          colors: [Colors.black, Colors.transparent],
                          stops: [0.7, 0.95],
                        ).createShader(bounds);
                      },
                      blendMode: BlendMode.dstIn,
                      child: photo))
            ]),
          ));
    }
  }
}

const _darkGradient = RadialGradient(
    radius: 0.9671,
    colors: [Color(0x66FFFFFF), Color(0x336099C6), Color(0x660C3063)],
    stops: [0.16, 0.49, 1.0]);

const _lightGradient = RadialGradient(
  radius: 0.9671,
  colors: [
    Color.fromRGBO(255, 255, 255, 0.4),
    Color.fromRGBO(2, 102, 180, 0),
  ],
  stops: [
    0.3082,
    1.0,
  ],
);
