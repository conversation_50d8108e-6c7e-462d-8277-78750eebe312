import 'package:praja_posters/src/auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:praja_posters/src/extensions/poster_gradient_extension.dart';
import 'package:praja_posters/src/models/poster_font_config.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/models/poster_badge.dart';
import 'package:praja_posters/src/poster_utils.dart';
import 'package:praja_posters/src/utils/font_utils.dart';

class PosterFlatBadgeRibbon extends StatelessWidget {
  final String text;
  final PosterBadgeBanner outlineType;
  final PosterGradient backgroundGradient;
  final double minBadgeTextFontSize;
  final double maxBadgeTextFontSize;
  final int badgeTextColor;
  final PosterFontConfig? badgeFontConfig;

  final clipper = const RibbonClipper();

  const PosterFlatBadgeRibbon({
    super.key,
    required this.text,
    required this.outlineType,
    required this.backgroundGradient,
    this.minBadgeTextFontSize = 9,
    this.maxBadgeTextFontSize = 11,
    required this.badgeTextColor,
    this.badgeFontConfig,
  });

  @override
  Widget build(BuildContext context) {
    return ClipPath(
        clipper: clipper,
        child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
                gradient: PosterUtils.getGoldAndSilverGradients(outlineType)),
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            child: ClipPath(
              clipper: clipper,
              child: Container(
                  decoration: BoxDecoration(
                    gradient: backgroundGradient.toGradient(),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 1),
                  child: Center(
                      child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: AutoSizeText(
                            text,
                            maxLines: 1,
                            textAlign: TextAlign.center,
                            minFontSize: minBadgeTextFontSize,
                            maxFontSize: maxBadgeTextFontSize,
                            overflow: TextOverflow.ellipsis,
                            textScaler: const TextScaler.linear(1.0),
                            style: TextStyle(
                              color: Color(badgeTextColor),
                              fontSize: minBadgeTextFontSize +
                                  (maxBadgeTextFontSize -
                                          minBadgeTextFontSize) /
                                      2,
                              fontWeight: FontWeight.bold,
                              fontFamily: FontUtils.getFontFamily(
                                  fontConfig: badgeFontConfig),
                            ),
                          )))),
            )));
  }
}

class PosterFlatBadgeRibbonWithDynamicSize extends StatelessWidget {
  final String text;
  final PosterBadgeBanner outlineType;
  final PosterGradient backgroundGradient;
  final double minBadgeTextFontSize;
  final double maxBadgeTextFontSize;
  final int badgeTextColor;

  final clipper = const RibbonClipper();

  const PosterFlatBadgeRibbonWithDynamicSize({
    super.key,
    required this.text,
    required this.outlineType,
    required this.backgroundGradient,
    this.minBadgeTextFontSize = 9,
    this.maxBadgeTextFontSize = 11,
    required this.badgeTextColor,
  });

  @override
  Widget build(BuildContext context) {
    return ClipPath(
        clipper: clipper,
        child: Container(
            decoration: BoxDecoration(
                gradient: PosterUtils.getGoldAndSilverGradients(outlineType)),
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            child: ClipPath(
              clipper: clipper,
              child: Container(
                  decoration: BoxDecoration(
                    gradient: backgroundGradient.toGradient(),
                  ),
                  child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                      ),
                      child: Text(
                        text,
                        maxLines: 1,
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            color: Color(badgeTextColor),
                            fontSize: 16,
                            fontWeight: FontWeight.bold),
                      ))),
            )));
  }
}

class RibbonClipper extends CustomClipper<Path> {
  const RibbonClipper();

  @override
  Path getClip(Size size) {
    double width = size.width;
    double height = size.height;
    Path path = Path();
    path.moveTo(0, 0);
    path.lineTo(width, 0);
    path.lineTo(width - height + (height / 2), height / 2);
    path.lineTo(width, height);
    path.lineTo(0, height);
    path.lineTo(height - (height / 2), height / 2);
    path.lineTo(0, 0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

class FlatBadgeRibbonDemo extends StatelessWidget {
  const FlatBadgeRibbonDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      PosterFlatBadgeRibbon(
        text: 'సర్పంచ్ కొండపల్లి',
        outlineType: PosterBadgeBanner.silver,
        backgroundGradient: blueBgGradient,
        badgeTextColor: 4294967295,
      ),
      const SizedBox(height: 8),
      PosterFlatBadgeRibbon(
        text: 'ఆంధ్రప్రదేశ్ టూరిజం&సాంస్కృతిక శాఖ మంత్రి',
        outlineType: PosterBadgeBanner.gold,
        backgroundGradient: blueBgGradient,
        badgeTextColor: 4294967295,
      )
    ]);
  }
}

final blueBgGradient = PosterGradient(
    colorsAsInt: [0xFF05305E, 0xE83490F3, 0xFF05305E],
    stops: [0.04, 0.4, 1.0],
    directions: PosterGradientDirection(
        beginX: -1.0, beginY: 0.0, endX: 1.0, endY: 0.0));
