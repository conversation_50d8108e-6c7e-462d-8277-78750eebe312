// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poster_badge.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PosterBadge _$PosterBadgeFromJson(Map<String, dynamic> json) => PosterBadge(
      id: json['id'] as int,
      active: json['active'] as bool,
      badgeIconUrl: json['icon_url'] as String?,
      badgeRole: json['badge_text'] as String? ?? '',
      badgeRing:
          $enumDecodeNullable(_$PosterBadgeRingEnumMap, json['badge_ring']) ??
              PosterBadgeRing.noRing,
      badgeBanner: $enumDecodeNullable(
              _$PosterBadgeBannerEnumMap, json['badge_banner']) ??
          PosterBadgeBanner.none,
      description: json['description'] as String? ?? '',
    );

Map<String, dynamic> _$PosterBadgeToJson(PosterBadge instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'active': instance.active,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('icon_url', instance.badgeIconUrl);
  val['badge_text'] = instance.badgeRole;
  val['badge_banner'] = _$PosterBadgeBannerEnumMap[instance.badgeBanner]!;
  val['badge_ring'] = _$PosterBadgeRingEnumMap[instance.badgeRing]!;
  val['description'] = instance.description;
  return val;
}

const _$PosterBadgeRingEnumMap = {
  PosterBadgeRing.goldRing: 'GOLD_RING',
  PosterBadgeRing.silverRing: 'SILVER_RING',
  PosterBadgeRing.noRing: 'NO_RING',
};

const _$PosterBadgeBannerEnumMap = {
  PosterBadgeBanner.gold: 'GOLD',
  PosterBadgeBanner.silver: 'SILVER',
  PosterBadgeBanner.white: 'WHITE',
  PosterBadgeBanner.none: 'NONE',
};
