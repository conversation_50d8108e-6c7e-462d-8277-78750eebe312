// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poster_subscription_screen.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PosterSubscriptionScreen _$PosterSubscriptionScreenFromJson(
        Map<String, dynamic> json) =>
    PosterSubscriptionScreen(
      imageUrl: json['image_url'] as String,
      header: json['header'] as String?,
      subHeader: json['sub_header'] as String?,
      subscriptionHeader: json['subscription_header'] as String?,
      subscriptionSubHeader: json['subscription_sub_header'] as String?,
      subscribeButton: PosterSubscribeButton.fromJson(
          json['subscribe_button'] as Map<String, dynamic>),
      disclaimer: json['disclaimer'] as String?,
      freeButtonText: json['free_button_text'] as String,
      suggestedText: json['suggested_text'] as String?,
      orderId: json['order_id'] as String? ?? '',
      terms: PosterTerms.fromJson(json['terms'] as Map<String, dynamic>),
      shareButtonText: json['share_button_text'] as String?,
    );

Map<String, dynamic> _$PosterSubscriptionScreenToJson(
    PosterSubscriptionScreen instance) {
  final val = <String, dynamic>{
    'image_url': instance.imageUrl,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('header', instance.header);
  writeNotNull('sub_header', instance.subHeader);
  writeNotNull('subscription_header', instance.subscriptionHeader);
  writeNotNull('subscription_sub_header', instance.subscriptionSubHeader);
  val['subscribe_button'] = instance.subscribeButton.toJson();
  writeNotNull('disclaimer', instance.disclaimer);
  val['terms'] = instance.terms.toJson();
  val['free_button_text'] = instance.freeButtonText;
  writeNotNull('suggested_text', instance.suggestedText);
  writeNotNull('share_button_text', instance.shareButtonText);
  val['order_id'] = instance.orderId;
  return val;
}

PosterSubscribeButton _$PosterSubscribeButtonFromJson(
        Map<String, dynamic> json) =>
    PosterSubscribeButton(
      period: json['period'] as String,
      content: json['content'] as String,
      amount: (json['amount'] as num).toDouble(),
    );

Map<String, dynamic> _$PosterSubscribeButtonToJson(
        PosterSubscribeButton instance) =>
    <String, dynamic>{
      'period': instance.period,
      'content': instance.content,
      'amount': instance.amount,
    };

PosterTerms _$PosterTermsFromJson(Map<String, dynamic> json) => PosterTerms(
      beforeLinkText: json['before_link_text'] as String?,
      afterLinkText: json['after_link_text'] as String?,
      linkText: json['link_text'] as String,
      linkUrl: json['link_url'] as String,
    );

Map<String, dynamic> _$PosterTermsToJson(PosterTerms instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('before_link_text', instance.beforeLinkText);
  writeNotNull('after_link_text', instance.afterLinkText);
  val['link_text'] = instance.linkText;
  val['link_url'] = instance.linkUrl;
  return val;
}
