// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poster_sponsor_banner.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PosterSponsorBanner _$PosterSponsorBannerFromJson(Map<String, dynamic> json) =>
    PosterSponsorBanner(
      iconUrl: json['icon_url'] as String,
      line1: json['line_1'] as String,
      line2: json['line_2'] as String,
      gradients:
          PosterGradient.from<PERSON>son(json['gradients'] as Map<String, dynamic>),
      line2TextColor: json['line_2_text_color'] as int? ?? 4294967295,
    );

Map<String, dynamic> _$PosterSponsorBannerToJson(
        PosterSponsorBanner instance) =>
    <String, dynamic>{
      'icon_url': instance.iconUrl,
      'line_1': instance.line1,
      'line_2': instance.line2,
      'gradients': instance.gradients.toJson(),
      'line_2_text_color': instance.line2TextColor,
    };
