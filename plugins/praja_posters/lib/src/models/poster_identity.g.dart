// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poster_identity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PosterIdentity _$PosterIdentityFromJson(Map<String, dynamic> json) =>
    PosterIdentity(
      type: $enumDecode(_$PosterIdentityTypeEnumMap, json['type'],
          unknownValue: PosterIdentityType.unknown),
      partyIconUrl: json['party_icon_url'] as String?,
      isUserPositionBack: json['is_user_position_back'] as bool? ?? false,
      user: PosterUser.fromJson(json['user'] as Map<String, dynamic>),
      partyHighlightColorPrimary: json['party_highlight_color_primary'] as int?,
      partyHighlightColorSecondary:
          json['party_highlight_color_secondary'] as int?,
      sloganText: json['slogan_text'] as String? ?? '',
      sloganIconUrl: json['slogan_icon_url'] as String?,
      showBadgeRibbon: json['show_badge_ribbon'] as bool? ?? true,
      userPhotoType: json['user_photo_type'] as String? ?? '',
      isDummyPhoto: json['is_dummy_photo'] as bool? ?? false,
    );

Map<String, dynamic> _$PosterIdentityToJson(PosterIdentity instance) {
  final val = <String, dynamic>{
    'type': _$PosterIdentityTypeEnumMap[instance.type]!,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('party_icon_url', instance.partyIconUrl);
  val['is_user_position_back'] = instance.isUserPositionBack;
  val['user'] = instance.user.toJson();
  writeNotNull(
      'party_highlight_color_primary', instance.partyHighlightColorPrimary);
  writeNotNull(
      'party_highlight_color_secondary', instance.partyHighlightColorSecondary);
  val['slogan_text'] = instance.sloganText;
  writeNotNull('slogan_icon_url', instance.sloganIconUrl);
  val['show_badge_ribbon'] = instance.showBadgeRibbon;
  val['user_photo_type'] = instance.userPhotoType;
  val['is_dummy_photo'] = instance.isDummyPhoto;
  return val;
}

const _$PosterIdentityTypeEnumMap = {
  PosterIdentityType.curved: 'curved',
  PosterIdentityType.curvedWithDepth: 'curved_with_depth',
  PosterIdentityType.flatUserBack: 'flat_user_back',
  PosterIdentityType.flatUserFront: 'flat_user_front',
  PosterIdentityType.flatUserBadgeCircle: 'flat_user_badge_circle',
  PosterIdentityType.goldLetteredUserBack: 'gold_lettered_user_back',
  PosterIdentityType.goldLetteredUserFront: 'gold_lettered_user_front',
  PosterIdentityType.flatUser: 'flat_user',
  PosterIdentityType.goldLetteredUser: 'gold_lettered_user',
  PosterIdentityType.glassyUser: 'glassy_user',
  PosterIdentityType.plainIdentity: 'plain_identity',
  PosterIdentityType.plainIdentityWithPartyIcon:
      'plain_identity_with_party_icon',
  PosterIdentityType.partySloganIdentity: 'party_slogan_identity',
  PosterIdentityType.partySloganIdentityWithPartyIcon:
      'party_slogan_identity_with_party_icon',
  PosterIdentityType.linearNameAndRoleIdentity: 'linear_name_and_role_identity',
  PosterIdentityType.trapezoidalIdentity: 'trapezoidal_identity',
  PosterIdentityType.topTrapezoidalIdentity: 'top_trapezoidal_identity',
  PosterIdentityType.bottomTrapezoidalIdentity: 'bottom_trapezoidal_identity',
  PosterIdentityType.strokedBorderIdentity: 'stroked_border_identity',
  PosterIdentityType.shinyIdentity: 'shiny_identity',
  PosterIdentityType.shinyIdentityWithLowShadow:
      'shiny_identity_with_low_shadow',
  PosterIdentityType.multiColorIdentity: 'multi_color_identity',
  PosterIdentityType.semiCircularIdentity: 'semi_circular_identity',
  PosterIdentityType.premiumCorneredPartyIconShinyIdentity:
      'premium_cornered_party_icon_shiny_identity',
  PosterIdentityType.premiumCorneredPartyIconGradientIdentity:
      'premium_cornered_party_icon_gradient_identity',
  PosterIdentityType.partyTagIdentity: 'party_tag_identity',
  PosterIdentityType.polygonalProfileIdentity: 'polygonal_profile_identity',
  PosterIdentityType.basicTransparentIdentity: 'basic_transparent_identity',
  PosterIdentityType.basicNoProfilePicIdentity: 'basic_no_profile_pic_identity',
  PosterIdentityType.basicFullTransparentIdentity:
      'basic_full_transparent_identity',
  PosterIdentityType.unknown: 'unknown',
};
