// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poster_gradient.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PosterGradient _$PosterGradientFromJson(Map<String, dynamic> json) =>
    PosterGradient(
      colorsAsInt:
          (json['colors'] as List<dynamic>?)?.map((e) => e as int).toList() ??
              [],
      stops: (json['stops'] as List<dynamic>?)
          ?.map((e) => (e as num).toDouble())
          .toList(),
      directions: PosterGradientDirection.fromJson(
          json['directions'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PosterGradientToJson(PosterGradient instance) {
  final val = <String, dynamic>{
    'colors': instance.colorsAsInt,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('stops', instance.stops);
  val['directions'] = instance.directions.toJson();
  return val;
}

PosterGradientDirection _$PosterGradientDirectionFromJson(
        Map<String, dynamic> json) =>
    PosterGradientDirection(
      beginX: (json['begin_x'] as num).toDouble(),
      beginY: (json['begin_y'] as num).toDouble(),
      endX: (json['end_x'] as num).toDouble(),
      endY: (json['end_y'] as num).toDouble(),
    );

Map<String, dynamic> _$PosterGradientDirectionToJson(
        PosterGradientDirection instance) =>
    <String, dynamic>{
      'begin_x': instance.beginX,
      'begin_y': instance.beginY,
      'end_x': instance.endX,
      'end_y': instance.endY,
    };
