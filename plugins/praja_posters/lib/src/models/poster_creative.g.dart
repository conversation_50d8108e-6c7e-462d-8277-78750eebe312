// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poster_creative.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PosterCreative _$PosterCreativeFromJson(Map<String, dynamic> json) =>
    PosterCreative(
      id: json['id'] as int? ?? 0,
      url: json['v2_url'] as String,
      thumbnailUrl: json['thumbnail_url'] as String? ?? '',
      h1BackgroundType: $enumDecodeNullable(_$PosterHeaderBackgroundTypeEnumMap,
              json['h1_background_type']) ??
          PosterHeaderBackgroundType.light,
      h2BackgroundType: $enumDecodeNullable(_$PosterHeaderBackgroundTypeEnumMap,
              json['h2_background_type']) ??
          PosterHeaderBackgroundType.light,
      leaderPhotoRingColor: json['leader_photo_ring_color'] as int,
      paid: json['paid'] as bool? ?? false,
      isLocked: json['is_locked'] as bool? ?? false,
      analyticsParams: json['analytics_params'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$PosterCreativeToJson(PosterCreative instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'v2_url': instance.url,
    'thumbnail_url': instance.thumbnailUrl,
    'h1_background_type':
        _$PosterHeaderBackgroundTypeEnumMap[instance.h1BackgroundType]!,
    'h2_background_type':
        _$PosterHeaderBackgroundTypeEnumMap[instance.h2BackgroundType]!,
    'leader_photo_ring_color': instance.leaderPhotoRingColor,
    'paid': instance.paid,
    'is_locked': instance.isLocked,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('analytics_params', instance.analyticsParams);
  return val;
}

const _$PosterHeaderBackgroundTypeEnumMap = {
  PosterHeaderBackgroundType.light: 'light',
  PosterHeaderBackgroundType.dark: 'dark',
  PosterHeaderBackgroundType.sticker: 'sticker',
  PosterHeaderBackgroundType.transparent: 'transparent',
};
