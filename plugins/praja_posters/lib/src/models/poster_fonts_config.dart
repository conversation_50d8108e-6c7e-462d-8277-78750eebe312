import 'package:json_annotation/json_annotation.dart';
import 'package:praja_posters/src/models/poster_font_config.dart';

part 'poster_fonts_config.g.dart';

@JsonSerializable()
class PosterFontsConfig {
  final PosterFontConfig? name;
  final PosterFontConfig? badge;

  PosterFontsConfig({
    this.name,
    this.badge,
  });

  factory PosterFontsConfig.fromJson(Map<String, dynamic> json) =>
      _$PosterFontsConfigFromJson(json);

  Map<String, dynamic> toJson() => _$PosterFontsConfigToJson(this);

  @override
  String toString() {
    return 'PosterFontsConfig{name: $name, badge: $badge}';
  }
}
