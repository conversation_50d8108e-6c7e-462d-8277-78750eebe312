import 'package:json_annotation/json_annotation.dart';
import 'package:praja_posters/src/enums/poster_identity_type.dart';
import 'package:praja_posters/src/models/poster_user.dart';

part 'poster_identity.g.dart';

@JsonSerializable()
class PosterIdentity {
  @Json<PERSON>ey(name: 'type', unknownEnumValue: PosterIdentityType.unknown)
  final PosterIdentityType type;
  @JsonKey(name: 'party_icon_url')
  final String? partyIconUrl;
  @Json<PERSON>ey(name: 'is_user_position_back', defaultValue: false)
  final bool isUserPositionBack;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'user')
  final PosterUser user;
  @Json<PERSON>ey(name: 'party_highlight_color_primary')
  final int? partyHighlightColorPrimary;
  @JsonKey(name: 'party_highlight_color_secondary')
  final int? partyHighlightColorSecondary;
  @JsonKey(name: 'slogan_text', defaultValue: '')
  final String sloganText;
  @<PERSON>son<PERSON><PERSON>(name: 'slogan_icon_url')
  final String? sloganIconUrl;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'show_badge_ribbon', defaultValue: true)
  final bool showBadgeRibbon;
  @<PERSON>son<PERSON>ey(name: 'user_photo_type', defaultValue: '')
  final String userPhotoType;

  /// We Auto upload dummy photo for family and hero frames
  /// This will help us to know whether the user photo is dummy or not
  @JsonKey(name: 'is_dummy_photo', defaultValue: false)
  final bool isDummyPhoto;

  PosterIdentity({
    required this.type,
    this.partyIconUrl,
    required this.isUserPositionBack,
    required this.user,
    this.partyHighlightColorPrimary,
    this.partyHighlightColorSecondary,
    required this.sloganText,
    this.sloganIconUrl,
    required this.showBadgeRibbon,
    required this.userPhotoType,
    required this.isDummyPhoto,
  });

  factory PosterIdentity.fromJson(Map<String, dynamic> json) =>
      _$PosterIdentityFromJson(json);

  Map<String, dynamic> toJson() => _$PosterIdentityToJson(this);

  //copyWith method
  PosterIdentity copyWith({
    PosterIdentityType? type,
    String? partyIconUrl,
    bool? isUserPositionBack,
    PosterUser? user,
    int? partyHighlightColorPrimary,
    int? partyHighlightColorSecondary,
    String? sloganText,
    String? sloganIconUrl,
    bool? showBadgeRibbon,
    String? userPhotoType,
    bool? isDummyPhoto,
  }) {
    return PosterIdentity(
      type: type ?? this.type,
      partyIconUrl: partyIconUrl ?? this.partyIconUrl,
      isUserPositionBack: isUserPositionBack ?? this.isUserPositionBack,
      user: user ?? this.user,
      partyHighlightColorPrimary:
          partyHighlightColorPrimary ?? this.partyHighlightColorPrimary,
      partyHighlightColorSecondary:
          partyHighlightColorSecondary ?? this.partyHighlightColorSecondary,
      sloganText: sloganText ?? this.sloganText,
      sloganIconUrl: sloganIconUrl ?? this.sloganIconUrl,
      showBadgeRibbon: showBadgeRibbon ?? this.showBadgeRibbon,
      userPhotoType: userPhotoType ?? this.userPhotoType,
      isDummyPhoto: isDummyPhoto ?? this.isDummyPhoto,
    );
  }

  @override
  String toString() {
    return 'PosterIdentity(type: $type, partyIconUrl: $partyIconUrl, isUserPositionBack: $isUserPositionBack, user: $user, partyHighlightColorPrimary: $partyHighlightColorPrimary, partyHighlightColorSecondary: $partyHighlightColorSecondary, sloganText: $sloganText, sloganIconUrl: $sloganIconUrl, showBadgeRibbon: $showBadgeRibbon, userPhotoType: $userPhotoType, isDummyPhoto: $isDummyPhoto)';
  }
}
