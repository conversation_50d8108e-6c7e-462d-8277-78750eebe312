// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poster_layout.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PosterLayout _$PosterLayoutFromJson(Map<String, dynamic> json) => PosterLayout(
      id: json['id'] as int? ?? 0,
      frameId: json['frame_id'] as int? ?? 0,
      protocolId: json['protocol_id'] as String? ?? '',
      frameType: $enumDecodeNullable(_$FrameTypeEnumMap, json['frame_type'],
              unknownValue: FrameType.basic) ??
          FrameType.basic,
      layoutType:
          $enumDecode(_$PosterLayoutTypeEnumEnumMap, json['layout_type']),
      frameIdentifier: json['frame_identifier'] as String? ?? '',
      enableOuterFrame: json['enable_outer_frame'] as bool? ?? true,
      showPrajaLogo: json['show_praja_logo'] as bool? ?? true,
      isLocked: json['is_locked'] as bool? ?? false,
      isBorderedLayout: json['is_bordered_layout'] as bool? ?? false,
      goldenFrame: json['golden_frame'] as bool? ?? false,
      header1Photos: (json['header_1_photos'] as List<dynamic>?)
              ?.map(
                  (e) => PosterHeaderPhoto.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      header2Photos: (json['header_2_photos'] as List<dynamic>?)
              ?.map(
                  (e) => PosterHeaderPhoto.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      partyIcon: json['party_icon'] == null
          ? null
          : PosterPartyIcon.fromJson(
              json['party_icon'] as Map<String, dynamic>),
      identity: json['identity'] == null
          ? null
          : PosterIdentity.fromJson(json['identity'] as Map<String, dynamic>),
      shareText: json['share_text'] as String? ?? '',
      gradients: json['gradients'] == null
          ? null
          : PosterGradientsConfig.fromJson(
              json['gradients'] as Map<String, dynamic>),
      nameTextColor: json['text_color'] as int? ?? 4294967295,
      badgeTextColor: json['badge_text_color'] as int? ?? 4294967295,
      shadowColor: json['shadow_color'] as int?,
      subscriptionScreen: json['subscription_screen'] == null
          ? null
          : PosterSubscriptionScreen.fromJson(
              json['subscription_screen'] as Map<String, dynamic>),
      premiumPitch: json['premium_pitch'] == null
          ? null
          : PosterPremiumPitch.fromJson(
              json['premium_pitch'] as Map<String, dynamic>),
      fanPosterRequest: json['fan_poster_request'] == null
          ? null
          : FanPosterRequest.fromJson(
              json['fan_poster_request'] as Map<String, dynamic>),
      posterFontsConfig: json['fonts_config'] == null
          ? null
          : PosterFontsConfig.fromJson(
              json['fonts_config'] as Map<String, dynamic>),
      sponsorBanner: json['sponsorship'] == null
          ? null
          : PosterSponsorBanner.fromJson(
              json['sponsorship'] as Map<String, dynamic>),
      analyticsParams: json['analytics_params'] as Map<String, dynamic>?,
      autoAdaptTextColor: json['auto_adapt_text_color'] as bool? ?? true,
      bannerAd: json['banner_ad'] as Map<String, dynamic>?,
      lockedDeeplink: json['locked_deeplink'] as String? ?? '',
      showCameraIcon: json['show_camera_icon'] as bool? ?? true,
      layoutDeeplink: json['layout_deeplink'] as String? ?? '',
      layoutFeedbackRequest: json['layout_feedback_request'] == null
          ? null
          : LayoutFeedbackRequest.fromJson(
              json['layout_feedback_request'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PosterLayoutToJson(PosterLayout instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'frame_id': instance.frameId,
    'protocol_id': instance.protocolId,
    'frame_type': _$FrameTypeEnumMap[instance.frameType]!,
    'layout_type': _$PosterLayoutTypeEnumEnumMap[instance.layoutType]!,
    'frame_identifier': instance.frameIdentifier,
    'enable_outer_frame': instance.enableOuterFrame,
    'show_praja_logo': instance.showPrajaLogo,
    'is_locked': instance.isLocked,
    'is_bordered_layout': instance.isBorderedLayout,
    'golden_frame': instance.goldenFrame,
    'header_1_photos': instance.header1Photos.map((e) => e.toJson()).toList(),
    'header_2_photos': instance.header2Photos.map((e) => e.toJson()).toList(),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('party_icon', instance.partyIcon?.toJson());
  writeNotNull('identity', instance.identity?.toJson());
  val['share_text'] = instance.shareText;
  writeNotNull('gradients', instance.gradients?.toJson());
  val['text_color'] = instance.nameTextColor;
  val['badge_text_color'] = instance.badgeTextColor;
  writeNotNull('shadow_color', instance.shadowColor);
  writeNotNull('subscription_screen', instance.subscriptionScreen?.toJson());
  writeNotNull('fonts_config', instance.posterFontsConfig?.toJson());
  writeNotNull('sponsorship', instance.sponsorBanner?.toJson());
  writeNotNull('premium_pitch', instance.premiumPitch?.toJson());
  writeNotNull('fan_poster_request', instance.fanPosterRequest?.toJson());
  writeNotNull('analytics_params', instance.analyticsParams);
  val['auto_adapt_text_color'] = instance.autoAdaptTextColor;
  writeNotNull('banner_ad', instance.bannerAd);
  val['locked_deeplink'] = instance.lockedDeeplink;
  val['show_camera_icon'] = instance.showCameraIcon;
  val['layout_deeplink'] = instance.layoutDeeplink;
  writeNotNull(
      'layout_feedback_request', instance.layoutFeedbackRequest?.toJson());
  return val;
}

const _$FrameTypeEnumMap = {
  FrameType.premium: 'premium',
  FrameType.status: 'status',
  FrameType.basic: 'basic',
  FrameType.circleFree: 'circle_free',
  FrameType.circlePaid: 'circle_paid',
  FrameType.familyFramePremium: 'family_frame_premium',
  FrameType.heroFramePremium: 'hero_frame_premium',
};

const _$PosterLayoutTypeEnumEnumMap = {
  PosterLayoutTypeEnum.basic: 'basic',
  PosterLayoutTypeEnum.premium: 'premium',
};

PosterHeaderPhoto _$PosterHeaderPhotoFromJson(Map<String, dynamic> json) =>
    PosterHeaderPhoto(
      photoUrl: json['photo_url'] as String,
      radius: (json['radius'] as num).toDouble(),
      positionX: (json['position_x'] as num).toDouble(),
      positionY: (json['position_y'] as num).toDouble(),
    );

Map<String, dynamic> _$PosterHeaderPhotoToJson(PosterHeaderPhoto instance) =>
    <String, dynamic>{
      'photo_url': instance.photoUrl,
      'radius': instance.radius,
      'position_x': instance.positionX,
      'position_y': instance.positionY,
    };

PosterPartyIcon _$PosterPartyIconFromJson(Map<String, dynamic> json) =>
    PosterPartyIcon(
      url: json['url'] as String,
      position: $enumDecodeNullable(
              _$PosterPartyIconAlignmentEnumMap, json['position']) ??
          PosterPartyIconAlignment.top,
      gradients:
          PosterGradient.fromJson(json['gradients'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PosterPartyIconToJson(PosterPartyIcon instance) =>
    <String, dynamic>{
      'url': instance.url,
      'position': _$PosterPartyIconAlignmentEnumMap[instance.position]!,
      'gradients': instance.gradients.toJson(),
    };

const _$PosterPartyIconAlignmentEnumMap = {
  PosterPartyIconAlignment.top: 'top',
  PosterPartyIconAlignment.left: 'left',
};

PosterPremiumPitch _$PosterPremiumPitchFromJson(Map<String, dynamic> json) =>
    PosterPremiumPitch(
      title: json['title'] as String? ?? '',
      description: json['description'] as String? ?? '',
      ctaText: json['cta_text'] as String,
      submitUrl: json['submit_url'] as String? ?? '',
      type: $enumDecodeNullable(
              _$PosterPremiumPitchButtonTypeEnumMap, json['type']) ??
          PosterPremiumPitchButtonType.deeplink,
      deeplink: json['deeplink'] as String? ?? '',
    );

Map<String, dynamic> _$PosterPremiumPitchToJson(PosterPremiumPitch instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'cta_text': instance.ctaText,
      'submit_url': instance.submitUrl,
      'type': _$PosterPremiumPitchButtonTypeEnumMap[instance.type]!,
      'deeplink': instance.deeplink,
    };

const _$PosterPremiumPitchButtonTypeEnumMap = {
  PosterPremiumPitchButtonType.api: 'api',
  PosterPremiumPitchButtonType.deeplink: 'deeplink',
};

FanPosterRequest _$FanPosterRequestFromJson(Map<String, dynamic> json) =>
    FanPosterRequest(
      circleId: json['circle_id'] as int,
      ctaText: json['cta_text'] as String? ?? '',
      requestedCtaText: json['requested_cta_text'] as String? ?? '',
    );

Map<String, dynamic> _$FanPosterRequestToJson(FanPosterRequest instance) =>
    <String, dynamic>{
      'circle_id': instance.circleId,
      'cta_text': instance.ctaText,
      'requested_cta_text': instance.requestedCtaText,
    };
