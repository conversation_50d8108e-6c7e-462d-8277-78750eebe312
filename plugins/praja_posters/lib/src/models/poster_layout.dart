import 'package:json_annotation/json_annotation.dart';
import 'package:praja_posters/src/enums/frame_type.dart';
import 'package:praja_posters/src/enums/party_icon_alignment.dart';
import 'package:praja_posters/src/enums/poster_layout_type_enum.dart';
import 'package:praja_posters/src/models/layout_feedback_request.dart';
import 'package:praja_posters/src/models/poster_fonts_config.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/models/poster_gradients_config.dart';
import 'package:praja_posters/src/models/poster_identity.dart';
import 'package:praja_posters/src/models/poster_sponsor_banner.dart';
import 'package:praja_posters/src/models/poster_subscription_screen.dart';

part 'poster_layout.g.dart';

@JsonSerializable()
class PosterLayout {
  @JsonKey(name: 'id', defaultValue: 0)
  final int id;
  @Json<PERSON>ey(name: 'frame_id', defaultValue: 0)
  final int frameId;
  @JsonKey(name: 'protocol_id', defaultValue: '')
  final String protocolId;
  @JsonKey(
      name: 'frame_type',
      defaultValue: FrameType.basic,
      unknownEnumValue: FrameType.basic)
  final FrameType frameType;
  @JsonKey(name: 'layout_type')
  final PosterLayoutTypeEnum layoutType;
  @JsonKey(name: 'frame_identifier', defaultValue: '')
  final String frameIdentifier;
  @JsonKey(name: 'enable_outer_frame', defaultValue: true)
  final bool enableOuterFrame;
  @JsonKey(name: 'show_praja_logo', defaultValue: true)
  final bool showPrajaLogo;
  @JsonKey(name: 'is_locked', defaultValue: false)
  final bool isLocked;
  @JsonKey(name: 'is_bordered_layout', defaultValue: false)
  final bool isBorderedLayout;
  @JsonKey(name: 'golden_frame', defaultValue: false)
  final bool goldenFrame;
  @JsonKey(name: 'header_1_photos', defaultValue: [])
  final List<PosterHeaderPhoto> header1Photos;
  @JsonKey(name: 'header_2_photos', defaultValue: [])
  final List<PosterHeaderPhoto> header2Photos;
  @JsonKey(name: 'party_icon')
  final PosterPartyIcon? partyIcon;
  @JsonKey(name: 'identity')
  final PosterIdentity? identity;
  @JsonKey(name: 'share_text', defaultValue: '')
  final String shareText;
  @JsonKey(name: 'gradients')
  final PosterGradientsConfig? gradients;
  @JsonKey(name: 'text_color', defaultValue: 4294967295)
  final int nameTextColor;
  @JsonKey(name: 'badge_text_color', defaultValue: 4294967295)
  final int badgeTextColor;
  @JsonKey(name: 'shadow_color')
  final int? shadowColor;
  @JsonKey(name: 'subscription_screen')
  final PosterSubscriptionScreen? subscriptionScreen;
  @JsonKey(name: 'fonts_config')
  final PosterFontsConfig? posterFontsConfig;
  @JsonKey(name: 'sponsorship')
  final PosterSponsorBanner? sponsorBanner;
  @JsonKey(name: 'premium_pitch')
  final PosterPremiumPitch? premiumPitch;
  @JsonKey(name: 'fan_poster_request')
  final FanPosterRequest? fanPosterRequest;
  @JsonKey(name: 'analytics_params')
  final Map<String, dynamic>? analyticsParams;
  @JsonKey(name: 'auto_adapt_text_color', defaultValue: true)
  final bool autoAdaptTextColor;
  @JsonKey(name: 'banner_ad')
  final Map<String, dynamic>? bannerAd;
  @JsonKey(name: 'locked_deeplink', defaultValue: '')
  final String lockedDeeplink;
  @JsonKey(name: 'show_camera_icon', defaultValue: true)
  final bool showCameraIcon;
  @JsonKey(name: 'layout_deeplink', defaultValue: '')
  final String layoutDeeplink;
  @JsonKey(name: 'layout_feedback_request')
  final LayoutFeedbackRequest? layoutFeedbackRequest;

  PosterLayout({
    required this.id,
    required this.frameId,
    required this.protocolId,
    required this.frameType,
    required this.layoutType,
    required this.frameIdentifier,
    required this.enableOuterFrame,
    required this.showPrajaLogo,
    required this.isLocked,
    required this.isBorderedLayout,
    required this.goldenFrame,
    required this.header1Photos,
    required this.header2Photos,
    this.partyIcon,
    this.identity,
    required this.shareText,
    this.gradients,
    required this.nameTextColor,
    required this.badgeTextColor,
    this.shadowColor,
    this.subscriptionScreen,
    this.premiumPitch,
    this.fanPosterRequest,
    this.posterFontsConfig,
    this.sponsorBanner,
    this.analyticsParams,
    required this.autoAdaptTextColor,
    required this.bannerAd,
    required this.lockedDeeplink,
    required this.showCameraIcon,
    required this.layoutDeeplink,
    this.layoutFeedbackRequest,
  });

  factory PosterLayout.fromJson(Map<String, dynamic> json) =>
      _$PosterLayoutFromJson(json);

  Map<String, dynamic> toJson() => _$PosterLayoutToJson(this);

  //copyWith method
  PosterLayout copyWith({
    int? id,
    int? frameId,
    String? protocolId,
    FrameType? frameType,
    PosterLayoutTypeEnum? layoutType,
    String? frameIdentifier,
    bool? enableOuterFrame,
    bool? showPrajaLogo,
    bool? isLocked,
    bool? isBorderedLayout,
    bool? goldenFrame,
    List<PosterHeaderPhoto>? header1Photos,
    List<PosterHeaderPhoto>? header2Photos,
    PosterPartyIcon? partyIcon,
    PosterIdentity? identity,
    String? shareText,
    PosterGradientsConfig? gradients,
    int? nameTextColor,
    int? badgeTextColor,
    int? shadowColor,
    PosterSubscriptionScreen? subscriptionScreen,
    PosterFontsConfig? posterFontsConfig,
    PosterSponsorBanner? sponsorBanner,
    PosterPremiumPitch? premiumPitch,
    FanPosterRequest? fanPosterRequest,
    Map<String, dynamic>? analyticsParams,
    bool? autoAdaptTextColor,
    Map<String, dynamic>? bannerAd,
    String? lockedDeeplink,
    bool? showCameraIcon,
    String? layoutDeeplink,
    LayoutFeedbackRequest? layoutFeedbackRequest,
  }) {
    return PosterLayout(
      id: id ?? this.id,
      frameId: frameId ?? this.frameId,
      protocolId: protocolId ?? this.protocolId,
      frameType: frameType ?? this.frameType,
      layoutType: layoutType ?? this.layoutType,
      frameIdentifier: frameIdentifier ?? this.frameIdentifier,
      enableOuterFrame: enableOuterFrame ?? this.enableOuterFrame,
      showPrajaLogo: showPrajaLogo ?? this.showPrajaLogo,
      isLocked: isLocked ?? this.isLocked,
      isBorderedLayout: isBorderedLayout ?? this.isBorderedLayout,
      goldenFrame: goldenFrame ?? this.goldenFrame,
      header1Photos: header1Photos ?? this.header1Photos,
      header2Photos: header2Photos ?? this.header2Photos,
      partyIcon: partyIcon ?? this.partyIcon,
      identity: identity ?? this.identity,
      shareText: shareText ?? this.shareText,
      gradients: gradients ?? this.gradients,
      nameTextColor: nameTextColor ?? this.nameTextColor,
      badgeTextColor: badgeTextColor ?? this.badgeTextColor,
      shadowColor: shadowColor ?? this.shadowColor,
      subscriptionScreen: subscriptionScreen ?? this.subscriptionScreen,
      posterFontsConfig: posterFontsConfig ?? this.posterFontsConfig,
      sponsorBanner: sponsorBanner ?? this.sponsorBanner,
      premiumPitch: premiumPitch ?? this.premiumPitch,
      fanPosterRequest: fanPosterRequest ?? this.fanPosterRequest,
      analyticsParams: analyticsParams ?? this.analyticsParams,
      autoAdaptTextColor: autoAdaptTextColor ?? this.autoAdaptTextColor,
      bannerAd: bannerAd ?? this.bannerAd,
      lockedDeeplink: lockedDeeplink ?? this.lockedDeeplink,
      showCameraIcon: showCameraIcon ?? this.showCameraIcon,
      layoutDeeplink: layoutDeeplink ?? this.layoutDeeplink,
      layoutFeedbackRequest:
          layoutFeedbackRequest ?? this.layoutFeedbackRequest,
    );
  }

  @override
  String toString() {
    return 'PosterLayout{id: $id, protocolId: $protocolId, frameId: $frameId, frameType: $frameType, layoutType: $layoutType, frameIdentifier: $frameIdentifier, enableOuterFrame: $enableOuterFrame, showPrajaLogo: $showPrajaLogo, isLocked: $isLocked, isBorderedLayout: $isBorderedLayout, goldenFrame: $goldenFrame, header1Photos: $header1Photos, header2Photos: $header2Photos, partyIcon: $partyIcon, identity: $identity, shareText: $shareText, gradients: $gradients, nameTextColor: $nameTextColor, badgeTextColor: $badgeTextColor, shadowColor: $shadowColor, subscriptionScreen: $subscriptionScreen, posterFontsConfig: $posterFontsConfig, sponsorBanner: $sponsorBanner, premiumPitch: $premiumPitch, fanPosterRequest: $fanPosterRequest, analyticsParams: $analyticsParams, autoAdaptTextColor: $autoAdaptTextColor, bannerAd: $bannerAd, lockedDeeplink: $lockedDeeplink, showCameraIcon: $showCameraIcon, layoutDeeplink: $layoutDeeplink}';
  }
}

@JsonSerializable()
class PosterHeaderPhoto {
  @JsonKey(name: 'photo_url')
  final String photoUrl;
  final double radius;
  @JsonKey(name: 'position_x')
  final double positionX;
  @JsonKey(name: 'position_y')
  final double positionY;
  PosterHeaderPhoto({
    required this.photoUrl,
    required this.radius,
    required this.positionX,
    required this.positionY,
  });

  factory PosterHeaderPhoto.fromJson(Map<String, dynamic> json) =>
      _$PosterHeaderPhotoFromJson(json);

  Map<String, dynamic> toJson() => _$PosterHeaderPhotoToJson(this);

  @override
  String toString() {
    return 'PosterHeaderPhoto{photoUrl: $photoUrl, radius: $radius, positionX: $positionX, positionY: $positionY}';
  }
}

@JsonSerializable()
class PosterPartyIcon {
  final String url;
  @JsonKey(name: 'position', defaultValue: PosterPartyIconAlignment.top)
  final PosterPartyIconAlignment position;
  final PosterGradient gradients;

  PosterPartyIcon({
    required this.url,
    required this.position,
    required this.gradients,
  });

  factory PosterPartyIcon.fromJson(Map<String, dynamic> json) =>
      _$PosterPartyIconFromJson(json);

  Map<String, dynamic> toJson() => _$PosterPartyIconToJson(this);

  @override
  String toString() {
    return 'PosterPartyIcon{url: $url, position: $position, gradients: $gradients}';
  }
}

@JsonSerializable()
class PosterPremiumPitch {
  @JsonKey(name: 'title', defaultValue: '')
  final String title;
  @JsonKey(name: 'description', defaultValue: '')
  final String description;
  @JsonKey(name: 'cta_text')
  final String ctaText;
  @JsonKey(name: 'submit_url', defaultValue: '')
  final String submitUrl;
  @JsonKey(name: 'type', defaultValue: PosterPremiumPitchButtonType.deeplink)
  final PosterPremiumPitchButtonType type;
  @JsonKey(name: 'deeplink', defaultValue: '')
  final String deeplink;

  PosterPremiumPitch({
    required this.title,
    required this.description,
    required this.ctaText,
    required this.submitUrl,
    required this.type,
    required this.deeplink,
  });

  factory PosterPremiumPitch.fromJson(Map<String, dynamic> json) =>
      _$PosterPremiumPitchFromJson(json);

  Map<String, dynamic> toJson() => _$PosterPremiumPitchToJson(this);

  @override
  String toString() {
    return 'PosterPremiumPitch{title: $title, description: $description, ctaText: $ctaText, submitUrl: $submitUrl, type: $type, deeplink: $deeplink}';
  }
}

enum PosterPremiumPitchButtonType { api, deeplink }

@JsonSerializable()
class FanPosterRequest {
  @JsonKey(name: 'circle_id')
  final int circleId;
  @JsonKey(name: 'cta_text', defaultValue: '')
  final String ctaText;
  @JsonKey(name: 'requested_cta_text', defaultValue: '')
  final String requestedCtaText;

  FanPosterRequest({
    required this.circleId,
    required this.ctaText,
    required this.requestedCtaText,
  });

  factory FanPosterRequest.fromJson(Map<String, dynamic> json) =>
      _$FanPosterRequestFromJson(json);

  Map<String, dynamic> toJson() => _$FanPosterRequestToJson(this);

  @override
  String toString() {
    return 'FanPosterRequest{circleId: $circleId, ctaText: $ctaText, requestedCtaText: $requestedCtaText}';
  }
}
