import 'package:json_annotation/json_annotation.dart';
import 'package:praja_posters/src/models/poster_badge.dart';

part 'poster_user.g.dart';

@JsonSerializable()
class PosterUser {
  final int id;
  final String name;
  @J<PERSON><PERSON>ey(name: 'photo_url')
  final String photoUrl;
  final PosterBadge? badge;

  PosterUser({
    required this.id,
    required this.name,
    required this.photoUrl,
    required this.badge,
  });

  factory PosterUser.fromJson(Map<String, dynamic> json) =>
      _$PosterUserFromJson(json);

  Map<String, dynamic> toJson() => _$PosterUserToJson(this);

  //copyWith method
  PosterUser copyWith({
    int? id,
    String? name,
    String? photoUrl,
    PosterBadge? badge,
  }) {
    return PosterUser(
      id: id ?? this.id,
      name: name ?? this.name,
      photoUrl: photoUrl ?? this.photoUrl,
      badge: badge ?? this.badge,
    );
  }

  @override
  String toString() {
    return 'PosterUser{id: $id, name: $name, photoUrl: $photoUrl, badge: $badge}';
  }
}
