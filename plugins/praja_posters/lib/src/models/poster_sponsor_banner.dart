import 'package:json_annotation/json_annotation.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';

part 'poster_sponsor_banner.g.dart';

@JsonSerializable()
class PosterSponsorBanner {
  @Json<PERSON>ey(name: 'icon_url')
  final String iconUrl;
  @J<PERSON><PERSON><PERSON>(name: 'line_1')
  final String line1;
  @Json<PERSON><PERSON>(name: 'line_2')
  final String line2;
  @JsonKey(name: 'gradients')
  final PosterGradient gradients;
  @<PERSON>son<PERSON>ey(name: 'line_2_text_color', defaultValue: 4294967295)
  final int line2TextColor;

  PosterSponsorBanner({
    required this.iconUrl,
    required this.line1,
    required this.line2,
    required this.gradients,
    required this.line2TextColor,
  });

  factory PosterSponsorBanner.fromJson(Map<String, dynamic> json) =>
      _$PosterSponsorBannerFromJson(json);

  Map<String, dynamic> toJson() => _$PosterSponsorBannerToJson(this);

  @override
  String toString() {
    return 'PosterSponsorBanner{iconUrl: $iconUrl, line1: $line1, line2: $line2, gradient: $gradients, line2TextColor: $line2TextColor}';
  }
}
