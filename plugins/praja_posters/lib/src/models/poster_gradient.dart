import 'package:json_annotation/json_annotation.dart';

part 'poster_gradient.g.dart';

@JsonSerializable()
class PosterGradient {
  @JsonKey(name: 'colors', defaultValue: [])
  final List<int> colorsAsInt;
  @Json<PERSON>ey(name: 'stops')
  final List<double>? stops;
  final PosterGradientDirection directions;

  PosterGradient({
    required this.colorsAsInt,
    required this.stops,
    required this.directions,
  });

  factory PosterGradient.fromJson(Map<String, dynamic> json) =>
      _$PosterGradientFromJson(json);

  Map<String, dynamic> toJson() => _$PosterGradientToJson(this);

  @override
  String toString() {
    return 'CustomGradientV2{colors: $colorsAsInt, stops: $stops, directions: $directions}';
  }
}

@JsonSerializable()
class PosterGradientDirection {
  @JsonKey(name: 'begin_x')
  final double beginX;
  @JsonKey(name: 'begin_y')
  final double beginY;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'end_x')
  final double endX;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'end_y')
  final double endY;

  PosterGradientDirection(
      {required this.beginX,
      required this.beginY,
      required this.endX,
      required this.endY});

  factory PosterGradientDirection.fromJson(Map<String, dynamic> json) =>
      _$PosterGradientDirectionFromJson(json);

  Map<String, dynamic> toJson() => _$PosterGradientDirectionToJson(this);

  @override
  String toString() {
    return 'GradientDirectionV2{beginX: $beginX, beginY: $beginY, endX: $endX, endY: $endY}';
  }
}
