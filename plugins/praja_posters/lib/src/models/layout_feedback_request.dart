import 'package:json_annotation/json_annotation.dart';

part 'layout_feedback_request.g.dart';

@JsonSerializable()
class LayoutFeedbackRequest {
  @Json<PERSON>ey(name: 'text', defaultValue: '')
  final String text;
  @Json<PERSON>ey(name: 'accept_text', defaultValue: '')
  final String acceptText;
  @J<PERSON><PERSON>ey(name: 'reject_text', defaultValue: '')
  final String rejectText;

  LayoutFeedbackRequest({
    required this.text,
    required this.acceptText,
    required this.rejectText,
  });

  factory LayoutFeedbackRequest.fromJson(Map<String, dynamic> json) =>
      _$LayoutFeedbackRequestFromJson(json);

  Map<String, dynamic> toJson() => _$LayoutFeedbackRequestToJson(this);

  @override
  String toString() =>
      'LayoutFeedbackRequest(text: $text, acceptText: $acceptText, rejectText: $rejectText)';
}
