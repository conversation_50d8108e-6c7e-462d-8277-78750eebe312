import 'package:json_annotation/json_annotation.dart';

part 'poster_font_config.g.dart';

@JsonSerializable()
class PosterFontConfig {
  @Json<PERSON>ey(name: 'font_family')
  final String fontFamily;
  @Json<PERSON>ey(name: 'font_style', defaultValue: "normal")
  final String fontStyle;
  @Json<PERSON>ey(name: 'font_weight', defaultValue: "w700") //w700 is bold
  final String fontWeight;

  PosterFontConfig({
    required this.fontFamily,
    required this.fontStyle,
    required this.fontWeight,
  });

  factory PosterFontConfig.fromJson(Map<String, dynamic> json) =>
      _$PosterFontConfigFromJson(json);

  Map<String, dynamic> toJson() => _$PosterFontConfigToJson(this);

  @override
  String toString() {
    return 'PosterFontObject{fontFamily: $fontFamily, fontStyle: $fontStyle, fontWeight: $fontWeight}';
  }
}
