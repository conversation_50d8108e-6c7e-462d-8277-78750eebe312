// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poster_gradients_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PosterGradientsConfig _$PosterGradientsConfigFromJson(
        Map<String, dynamic> json) =>
    PosterGradientsConfig(
      backgroundGradients: PosterGradient.fromJson(
          json['background_gradients'] as Map<String, dynamic>),
      innerBackgroundGradients: json['inner_background_gradients'] == null
          ? null
          : PosterGradient.fromJson(
              json['inner_background_gradients'] as Map<String, dynamic>),
      badgeBannerGradients: json['badge_banner_gradients'] == null
          ? null
          : PosterGradient.fromJson(
              json['badge_banner_gradients'] as Map<String, dynamic>),
      footerGradients: PosterGradient.fromJson(
          json['footer_gradients'] as Map<String, dynamic>),
      badgeRibbonBackgroundGradients:
          json['badge_ribbon_background_gradients'] == null
              ? null
              : PosterGradient.fromJson(
                  json['badge_ribbon_background_gradients']
                      as Map<String, dynamic>),
      identityInnerBackgroundGradients:
          json['identity_inner_background_gradients'] == null
              ? null
              : PosterGradient.fromJson(
                  json['identity_inner_background_gradients']
                      as Map<String, dynamic>),
      identityBorderGradients: json['identity_border_gradients'] == null
          ? null
          : PosterGradient.fromJson(
              json['identity_border_gradients'] as Map<String, dynamic>),
      upperFooterGradients: json['upper_footer_gradients'] == null
          ? null
          : PosterGradient.fromJson(
              json['upper_footer_gradients'] as Map<String, dynamic>),
      partyIconBackgroundGradients: json['party_icon_background_gradients'] ==
              null
          ? null
          : PosterGradient.fromJson(
              json['party_icon_background_gradients'] as Map<String, dynamic>),
      h2BackgroundGradients: json['h2_background_gradients'] == null
          ? null
          : PosterGradient.fromJson(
              json['h2_background_gradients'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PosterGradientsConfigToJson(
    PosterGradientsConfig instance) {
  final val = <String, dynamic>{
    'background_gradients': instance.backgroundGradients.toJson(),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('inner_background_gradients',
      instance.innerBackgroundGradients?.toJson());
  writeNotNull(
      'badge_banner_gradients', instance.badgeBannerGradients?.toJson());
  val['footer_gradients'] = instance.footerGradients.toJson();
  writeNotNull('badge_ribbon_background_gradients',
      instance.badgeRibbonBackgroundGradients?.toJson());
  writeNotNull('identity_inner_background_gradients',
      instance.identityInnerBackgroundGradients?.toJson());
  writeNotNull(
      'identity_border_gradients', instance.identityBorderGradients?.toJson());
  writeNotNull(
      'upper_footer_gradients', instance.upperFooterGradients?.toJson());
  writeNotNull('party_icon_background_gradients',
      instance.partyIconBackgroundGradients?.toJson());
  writeNotNull(
      'h2_background_gradients', instance.h2BackgroundGradients?.toJson());
  return val;
}
