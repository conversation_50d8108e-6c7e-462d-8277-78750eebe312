import 'package:json_annotation/json_annotation.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
part 'poster_gradients_config.g.dart';

@JsonSerializable()
class PosterGradientsConfig {
  @J<PERSON><PERSON><PERSON>(name: 'background_gradients')
  final PosterGradient backgroundGradients;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'inner_background_gradients')
  final PosterGradient? innerBackgroundGradients;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'badge_banner_gradients')
  final PosterGradient? badgeBannerGradients;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'footer_gradients')
  final PosterGradient footerGradients;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'badge_ribbon_background_gradients')
  final PosterGradient? badgeRibbonBackgroundGradients;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'identity_inner_background_gradients')
  final PosterGradient? identityInnerBackgroundGradients;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'identity_border_gradients')
  final PosterGradient? identityBorderGradients;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'upper_footer_gradients')
  final PosterGradient? upperFooterGradients;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'party_icon_background_gradients')
  final PosterGradient? partyIconBackgroundGradients;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'h2_background_gradients')
  final PosterGradient? h2BackgroundGradients;

  PosterGradientsConfig({
    required this.backgroundGradients,
    this.innerBackgroundGradients,
    this.badgeBannerGradients,
    required this.footerGradients,
    this.badgeRibbonBackgroundGradients,
    this.identityInnerBackgroundGradients,
    this.identityBorderGradients,
    this.upperFooterGradients,
    this.partyIconBackgroundGradients,
    this.h2BackgroundGradients,
  });

  factory PosterGradientsConfig.fromJson(Map<String, dynamic> json) =>
      _$PosterGradientsConfigFromJson(json);

  Map<String, dynamic> toJson() => _$PosterGradientsConfigToJson(this);

  @override
  String toString() {
    return 'PosterGradientsConfig{backgroundGradients: $backgroundGradients, innerBackgroundGradients: $innerBackgroundGradients, badgeBannerGradients: $badgeBannerGradients, footerGradients: $footerGradients, badgeRibbonBackgroundGradients: $badgeRibbonBackgroundGradients , identityInnerBackgroundGradients: $identityInnerBackgroundGradients , identityBorderGradients: $identityBorderGradients , lowerFooterGradients: $upperFooterGradients , partyIconBackgroundGradients: $partyIconBackgroundGradients , h2BackgroundGradients: $h2BackgroundGradients}';
  }
}
