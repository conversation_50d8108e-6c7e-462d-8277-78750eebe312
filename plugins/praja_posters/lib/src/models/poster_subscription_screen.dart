import 'package:json_annotation/json_annotation.dart';

part 'poster_subscription_screen.g.dart';

@JsonSerializable()
class PosterSubscriptionScreen {
  @J<PERSON><PERSON><PERSON>(name: 'image_url')
  final String imageUrl;
  @J<PERSON><PERSON><PERSON>(name: 'header')
  final String? header;
  @J<PERSON><PERSON><PERSON>(name: 'sub_header')
  final String? subHeader;
  @Json<PERSON><PERSON>(name: 'subscription_header')
  final String? subscriptionHeader;
  @Json<PERSON>ey(name: 'subscription_sub_header')
  final String? subscriptionSubHeader;
  @Json<PERSON>ey(name: 'subscribe_button')
  final PosterSubscribeButton subscribeButton;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'disclaimer')
  final String? disclaimer;
  @Json<PERSON>ey(name: 'terms')
  final PosterTerms terms;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'free_button_text')
  final String freeButtonText;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'suggested_text')
  final String? suggestedText;
  @Json<PERSON>ey(name: "share_button_text")
  final String? shareButtonText;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'order_id', defaultValue: '')
  final String orderId;

  PosterSubscriptionScreen({
    required this.imageUrl,
    this.header,
    this.subHeader,
    this.subscriptionHeader,
    this.subscriptionSubHeader,
    required this.subscribeButton,
    this.disclaimer,
    required this.freeButtonText,
    this.suggestedText,
    required this.orderId,
    required this.terms,
    this.shareButtonText,
  });

  factory PosterSubscriptionScreen.fromJson(Map<String, dynamic> json) =>
      _$PosterSubscriptionScreenFromJson(json);

  Map<String, dynamic> toJson() => _$PosterSubscriptionScreenToJson(this);

  @override
  String toString() {
    return 'PosterSubscriptionScreen{imageUrl: $imageUrl, header: $header, subHeader: $subHeader, subscriptionHeader: $subscriptionHeader, subscriptionSubHeader: $subscriptionSubHeader, subscribeButton: $subscribeButton, disclaimer: $disclaimer, freeButtonText: $freeButtonText, suggestedText: $suggestedText, orderId: $orderId, terms: $terms}';
  }
}

@JsonSerializable()
class PosterSubscribeButton {
  final String period;
  final String content;
  final double amount;

  PosterSubscribeButton({
    required this.period,
    required this.content,
    required this.amount,
  });

  factory PosterSubscribeButton.fromJson(Map<String, dynamic> json) =>
      _$PosterSubscribeButtonFromJson(json);

  Map<String, dynamic> toJson() => _$PosterSubscribeButtonToJson(this);

  @override
  String toString() {
    return 'PosterSubscribeButton{period: $period, content: $content, amount: $amount}';
  }
}

@JsonSerializable()
class PosterTerms {
  @JsonKey(name: 'before_link_text')
  final String? beforeLinkText;
  @JsonKey(name: 'after_link_text')
  final String? afterLinkText;
  @JsonKey(name: 'link_text')
  final String linkText;
  @JsonKey(name: 'link_url')
  final String linkUrl;

  PosterTerms({
    this.beforeLinkText,
    this.afterLinkText,
    required this.linkText,
    required this.linkUrl,
  });

  factory PosterTerms.fromJson(Map<String, dynamic> json) =>
      _$PosterTermsFromJson(json);

  Map<String, dynamic> toJson() => _$PosterTermsToJson(this);

  @override
  String toString() {
    return 'PosterTerms{beforeLinkText: $beforeLinkText, afterLinkText: $afterLinkText, linkText: $linkText, linkUrl: $linkUrl}';
  }
}
