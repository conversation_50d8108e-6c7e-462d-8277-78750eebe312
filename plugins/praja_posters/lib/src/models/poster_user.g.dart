// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poster_user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PosterUser _$PosterUser<PERSON>romJson(Map<String, dynamic> json) => PosterUser(
      id: json['id'] as int,
      name: json['name'] as String,
      photoUrl: json['photo_url'] as String,
      badge: json['badge'] == null
          ? null
          : PosterBadge.fromJson(json['badge'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PosterUserToJson(PosterUser instance) {
  final val = <String, dynamic>{
    'id': instance.id,
    'name': instance.name,
    'photo_url': instance.photoUrl,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('badge', instance.badge?.toJson());
  return val;
}
