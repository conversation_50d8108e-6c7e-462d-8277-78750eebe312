import 'package:json_annotation/json_annotation.dart';
import 'package:praja_posters/src/enums/poster_header_background_type.dart';

part 'poster_creative.g.dart';

@JsonSerializable()
class PosterCreative {
  @J<PERSON><PERSON><PERSON>(name: 'id', defaultValue: 0)
  final int id;
  @J<PERSON><PERSON><PERSON>(name: 'v2_url')
  final String url;
  @<PERSON>son<PERSON>ey(name: 'thumbnail_url', defaultValue: '')
  final String thumbnailUrl;
  @<PERSON><PERSON><PERSON><PERSON>(
      name: 'h1_background_type',
      defaultValue: PosterHeaderBackgroundType.light)
  final PosterHeaderBackgroundType h1BackgroundType;
  @<PERSON><PERSON><PERSON><PERSON>(
      name: 'h2_background_type',
      defaultValue: PosterHeaderBackgroundType.light)
  final PosterHeaderBackgroundType h2BackgroundType;
  @<PERSON>son<PERSON>ey(name: 'leader_photo_ring_color')
  final int leaderPhotoRingColor;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'paid', defaultValue: false)
  final bool paid;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_locked', defaultValue: false)
  final bool isLocked;
  @Json<PERSON>ey(name: 'analytics_params')
  final Map<String, dynamic>? analyticsParams;

  PosterCreative({
    required this.id,
    required this.url,
    required this.thumbnailUrl,
    required this.h1BackgroundType,
    required this.h2BackgroundType,
    required this.leaderPhotoRingColor,
    required this.paid,
    required this.isLocked,
    this.analyticsParams,
  });

  factory PosterCreative.fromJson(Map<String, dynamic> json) =>
      _$PosterCreativeFromJson(json);

  Map<String, dynamic> toJson() => _$PosterCreativeToJson(this);

  @override
  String toString() {
    return 'PosterCreative(id: $id, url: $url, thumbnailUrl: $thumbnailUrl, h1BackgroundType: $h1BackgroundType, h2BackgroundType: $h2BackgroundType, leaderPhotoRingColor: $leaderPhotoRingColor, paid: $paid, isLocked: $isLocked, analyticsParams: $analyticsParams)';
  }
}
