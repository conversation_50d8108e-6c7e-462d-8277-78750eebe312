import 'package:flutter/material.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/extensions/poster_gradient_extension.dart';

class GoldFrame extends StatelessWidget {
  final PosterGradient backgroundGradient;
  final PosterGradient innerBackgroundGradient;
  final Widget child;
  const GoldFrame(
      {super.key,
      required this.child,
      required this.backgroundGradient,
      required this.innerBackgroundGradient});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(gradient: innerBackgroundGradient.toGradient()),
      padding: const EdgeInsets.all(10),
      child: child,
    );
  }
}
