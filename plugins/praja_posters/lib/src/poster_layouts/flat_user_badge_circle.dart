import 'package:flutter/material.dart';
import 'package:gradient_borders/box_borders/gradient_box_border.dart';
import 'package:praja_posters/src/models/poster_user.dart';
import 'package:praja_posters/src/poster_utils.dart';
import 'package:praja_posters/src/ui_widgets/praja_poster_image.dart';

class FlatUserBadgeCircle extends StatelessWidget {
  final PosterUser user;
  final bool isUserPositionBack;
  const FlatUserBadgeCircle({
    super.key,
    required this.user,
    this.isUserPositionBack = false,
  });

  Gradient? getGradients() {
    final badge = user.badge;
    if (badge == null) {
      return null;
    }
    return PosterUtils.getGoldAndSilverGradients(badge.badgeBanner);
  }

  Widget getBadgeIcon() {
    final badge = user.badge;
    final badgeIconUrl = badge?.badgeIconUrl;
    if (badge == null || badgeIconUrl == null) {
      return const SizedBox();
    }
    return PrajaPosterImage(imageUrl: badgeIconUrl);
  }

  @override
  Widget build(BuildContext context) {
    final gradients = getGradients();
    return Stack(
      children: [
        Container(
          height: isUserPositionBack ? 170 : 190,
          width: isUserPositionBack ? 170 : 190,
          padding: const EdgeInsets.all(0.5),
          decoration: gradients == null
              ? null
              : BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.transparent,
                  border: GradientBoxBorder(
                    gradient: gradients,
                    width: 6,
                  )),
          child: ClipOval(
              child: PrajaPosterImage(
            imageUrl: user.photoUrl,
            fit: BoxFit.cover,
          )),
        ),
        Positioned(
          bottom: 0,
          right: 10,
          child: Container(
              height: isUserPositionBack ? 50 : 60,
              width: isUserPositionBack ? 50 : 60,
              decoration: const BoxDecoration(shape: BoxShape.circle),
              child: getBadgeIcon()),
        )
      ],
    );
  }
}
