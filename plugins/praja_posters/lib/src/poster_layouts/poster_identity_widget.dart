import 'package:flutter/material.dart';
import 'package:praja_posters/src/enums/poster_identity_type.dart';
import 'package:praja_posters/src/models/poster_font_config.dart';
import 'package:praja_posters/src/models/poster_gradients_config.dart';
import 'package:praja_posters/src/models/poster_identity.dart';
import 'package:praja_posters/src/poster_layouts/glassy_identity.dart';
import 'package:praja_posters/src/poster_layouts/gold_lettered_identity.dart';
import 'package:praja_posters/src/poster_layouts/linear_name_role_identity.dart';
import 'package:praja_posters/src/poster_layouts/multi_color_identity.dart';
import 'package:praja_posters/src/poster_layouts/party_tag_identity.dart';
import 'package:praja_posters/src/poster_layouts/polygonal_profile_identity.dart';
import 'package:praja_posters/src/poster_layouts/premium_cornered_party_icon_gradient_identity.dart';
import 'package:praja_posters/src/poster_layouts/premium_cornered_party_icon_shiny_identity.dart';
import 'package:praja_posters/src/poster_layouts/premium_flat_identity.dart';
import 'package:praja_posters/src/poster_layouts/premium_frame_identity.dart';
import 'package:praja_posters/src/poster_layouts/premium_plain_identity.dart';
import 'package:praja_posters/src/poster_layouts/party_slogan_identity.dart';
import 'package:praja_posters/src/poster_layouts/semi_circular_identity.dart';
import 'package:praja_posters/src/poster_layouts/shiny_identity.dart';
import 'package:praja_posters/src/poster_layouts/stroked_border_identity.dart';

class PosterIdentityWidget extends StatelessWidget {
  final PosterIdentity identity;
  final PosterGradientsConfig gradients;
  final int nameTextColor;
  final int badgeTextColor;
  final bool isGoldenFrame;
  final PosterFontConfig? nameFontConfig;
  final PosterFontConfig? badgeFontConfig;

  const PosterIdentityWidget({
    super.key,
    required this.identity,
    required this.gradients,
    required this.nameTextColor,
    required this.badgeTextColor,
    required this.isGoldenFrame,
    this.nameFontConfig,
    this.badgeFontConfig,
  });

  static const double _maxFontSize = 40;
  static const double _minFontSize = 24;
  static const double _minBadgeTextFontSize = 12;
  static const double _maxBadgeTextFontSizeWithRibbon = 18;
  static const double _maxBadgeTextFontSizeWithoutRibbon = 24;
  static const int _curvedGoldFrameNameTextColor = 0xFF0B278C;

  Widget _getIdentityWidget() {
    final maxBadgeTextFontSize = identity.showBadgeRibbon
        ? _maxBadgeTextFontSizeWithRibbon
        : _maxBadgeTextFontSizeWithoutRibbon;
    switch (identity.type) {
      case PosterIdentityType.curved:
        return PremiumFrameIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          backgroundGradient: gradients.footerGradients,
          badgeGradient: gradients.badgeBannerGradients,
          badgeRibbonBackgroundGradients:
              gradients.badgeRibbonBackgroundGradients,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          minBadgeTextFontSize: _minBadgeTextFontSize,
          maxBadgeTextFontSize: maxBadgeTextFontSize,
          nameTextColor:
              isGoldenFrame ? _curvedGoldFrameNameTextColor : nameTextColor,
          badgeTextColor: badgeTextColor,
          showTextShadow: true,
          showDepthGradients: false,
          fullWidth: identity.isUserPositionBack,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
          showBadgeRibbon: identity.showBadgeRibbon,
        );
      case PosterIdentityType.curvedWithDepth:
        return PremiumFrameIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          backgroundShadowGradient: gradients.backgroundGradients,
          backgroundGradient: gradients.footerGradients,
          badgeGradient: gradients.badgeBannerGradients,
          badgeRibbonBackgroundGradients:
              gradients.badgeRibbonBackgroundGradients,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          minBadgeTextFontSize: _minBadgeTextFontSize,
          maxBadgeTextFontSize: maxBadgeTextFontSize,
          nameTextColor:
              isGoldenFrame ? _curvedGoldFrameNameTextColor : nameTextColor,
          badgeTextColor: badgeTextColor,
          showTextShadow: true,
          showDepthGradients: true,
          fullWidth: identity.isUserPositionBack,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
          showBadgeRibbon: identity.showBadgeRibbon,
        );
      case PosterIdentityType.flatUser:
        return PremiumFlatIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          badgeBannerGradients: gradients.badgeBannerGradients,
          badgeRibbonBackgroundGradients:
              gradients.badgeRibbonBackgroundGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: _minBadgeTextFontSize,
          maxBadgeTextFontSize: maxBadgeTextFontSize,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          partyIcon: identity.partyIconUrl,
          fullWidth: identity.isUserPositionBack,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
          showBadgeRibbon: identity.showBadgeRibbon,
        );
      case PosterIdentityType.flatUserBadgeCircle:
        return PremiumFlatIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          badgeBannerGradients: gradients.badgeBannerGradients,
          badgeRibbonBackgroundGradients:
              gradients.badgeRibbonBackgroundGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: _minBadgeTextFontSize,
          maxBadgeTextFontSize: maxBadgeTextFontSize,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          partyIcon: identity.partyIconUrl,
          fullWidth: identity.isUserPositionBack,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
          showBadgeRibbon: identity.showBadgeRibbon,
        );
      case PosterIdentityType.goldLetteredUser:
        return GoldLetteredIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          badgeBannerGradients: gradients.badgeBannerGradients,
          badgeRibbonBackgroundGradients:
              gradients.badgeRibbonBackgroundGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: _minBadgeTextFontSize,
          maxBadgeTextFontSize: maxBadgeTextFontSize,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          partyIcon: identity.partyIconUrl,
          fullWidth: identity.isUserPositionBack,
          badgeFontConfig: badgeFontConfig,
          nameFontConfig: nameFontConfig,
          showBadgeRibbon: identity.showBadgeRibbon,
        );
      case PosterIdentityType.glassyUser:
        return GlassyIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          badgeBannerGradients: gradients.badgeBannerGradients,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          minBadgeTextFontSize: _minBadgeTextFontSize,
          maxBadgeTextFontSize: maxBadgeTextFontSize,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          fullWidth: identity.isUserPositionBack,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
          showBadgeRibbon: identity.showBadgeRibbon,
        );
      case PosterIdentityType.plainIdentity:
        return PremiumPlainIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: 14,
          maxBadgeTextFontSize: 24,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          isUserPositionBack: identity.isUserPositionBack,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
        );
      case PosterIdentityType.plainIdentityWithPartyIcon:
        return PremiumPlainIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          partyIcon: identity.sloganIconUrl,
          footerGradients: gradients.footerGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: 14,
          maxBadgeTextFontSize: 24,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          iconBackgroundColor: identity.partyHighlightColorPrimary,
          isUserPositionBack: identity.isUserPositionBack,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
        );
      case PosterIdentityType.partySloganIdentity:
        return PartySloganIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          badgeBannerGradients: gradients.badgeBannerGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: 14,
          maxBadgeTextFontSize: 24,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          sloganText: identity.sloganText,
          borderHighlightColor: identity.partyHighlightColorPrimary,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
          showBadgeRibbon: identity.showBadgeRibbon,
        );
      case PosterIdentityType.partySloganIdentityWithPartyIcon:
        return PartySloganIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          partyIcon: identity.sloganIconUrl,
          footerGradients: gradients.footerGradients,
          badgeBannerGradients: gradients.badgeBannerGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: 14,
          maxBadgeTextFontSize: 24,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          sloganText: identity.sloganText,
          borderHighlightColor: identity.partyHighlightColorPrimary,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
          showBadgeRibbon: identity.showBadgeRibbon,
        );
      case PosterIdentityType.linearNameAndRoleIdentity:
        return LinearNameRoleIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: 14,
          maxBadgeTextFontSize: 24,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          partyIcon: identity.partyIconUrl,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
        );
      case PosterIdentityType.trapezoidalIdentity:
        return LinearNameRoleIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: 14,
          maxBadgeTextFontSize: 24,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          partyIcon: identity.partyIconUrl,
          primaryHighlightColor: identity.partyHighlightColorPrimary,
          secondaryHighlightColor: identity.partyHighlightColorSecondary,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
        );
      case PosterIdentityType.bottomTrapezoidalIdentity:
        return LinearNameRoleIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: 14,
          maxBadgeTextFontSize: 24,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          partyIcon: identity.partyIconUrl,
          secondaryHighlightColor: identity.partyHighlightColorSecondary,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
        );
      case PosterIdentityType.topTrapezoidalIdentity:
        return LinearNameRoleIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: 14,
          maxBadgeTextFontSize: 24,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          partyIcon: identity.partyIconUrl,
          primaryHighlightColor: identity.partyHighlightColorPrimary,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
        );
      case PosterIdentityType.strokedBorderIdentity:
        return StrokedBorderIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          badgeBannerGradients: gradients.badgeBannerGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: _minBadgeTextFontSize,
          maxBadgeTextFontSize: maxBadgeTextFontSize,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          partyIcon: identity.partyIconUrl,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
          showBadgeRibbon: identity.showBadgeRibbon,
        );
      case PosterIdentityType.shinyIdentityWithLowShadow:
        return ShinyIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          partyIcon: identity.partyIconUrl,
          badgeBannerGradients: gradients.badgeBannerGradients,
          identityInnerBackgroundGradients:
              gradients.identityInnerBackgroundGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: _minBadgeTextFontSize,
          maxBadgeTextFontSize: maxBadgeTextFontSize,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          primaryHighlightColor: identity.partyHighlightColorPrimary,
          secondaryHighlightColor: identity.partyHighlightColorSecondary,
          borderGradients: gradients.identityBorderGradients,
          isHighShadowVariant: false,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
          showBadgeRibbon: identity.showBadgeRibbon,
        );
      case PosterIdentityType.shinyIdentity:
        return ShinyIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          partyIcon: identity.partyIconUrl,
          badgeBannerGradients: gradients.badgeBannerGradients,
          identityInnerBackgroundGradients:
              gradients.identityInnerBackgroundGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: _minBadgeTextFontSize,
          maxBadgeTextFontSize: maxBadgeTextFontSize,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          primaryHighlightColor: identity.partyHighlightColorPrimary,
          secondaryHighlightColor: identity.partyHighlightColorSecondary,
          borderGradients: gradients.identityBorderGradients,
          isHighShadowVariant: true,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
          showBadgeRibbon: identity.showBadgeRibbon,
        );
      case PosterIdentityType.multiColorIdentity:
        return MultiColorIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          upperFooterGradients: gradients.upperFooterGradients,
          badgeBannerGradients: gradients.badgeBannerGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: _minBadgeTextFontSize,
          maxBadgeTextFontSize: maxBadgeTextFontSize,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
          showBadgeRibbon: identity.showBadgeRibbon,
          isFullWidthName: identity.isUserPositionBack,
        );
      case PosterIdentityType.semiCircularIdentity:
        return SemiCircularIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          badgeBannerGradients: gradients.badgeBannerGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: _minBadgeTextFontSize,
          maxBadgeTextFontSize: maxBadgeTextFontSize,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
          showBadgeRibbon: identity.showBadgeRibbon,
        );
      case PosterIdentityType.premiumCorneredPartyIconShinyIdentity:
        return PremiumCorneredPartyIconShinyIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          badgeBannerGradients: gradients.badgeBannerGradients,
          borderGradients: gradients.identityBorderGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: _minBadgeTextFontSize,
          maxBadgeTextFontSize: maxBadgeTextFontSize,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          partyIcon: identity.partyIconUrl,
          primaryHighlightColor: identity.partyHighlightColorPrimary,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
          showBadgeRibbon: identity.showBadgeRibbon,
          isFullWidthIdentity: identity.isUserPositionBack,
        );
      case PosterIdentityType.premiumCorneredPartyIconGradientIdentity:
        return PremiumCorneredPartyIconGradientIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          badgeBannerGradients: gradients.badgeBannerGradients,
          partyIconBackgroundGradients: gradients.partyIconBackgroundGradients,
          borderGradients: gradients.identityBorderGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: _minBadgeTextFontSize,
          maxBadgeTextFontSize: maxBadgeTextFontSize,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          partyIcon: identity.partyIconUrl,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
          showBadgeRibbon: identity.showBadgeRibbon,
          isFullWidthIdentity: identity.isUserPositionBack,
        );
      case PosterIdentityType.partyTagIdentity:
        return PartyTagIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          upperFooterGradients: gradients.upperFooterGradients,
          badgeBannerGradients: gradients.badgeBannerGradients,
          partyTagBackgroundGradients: gradients.backgroundGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: _minBadgeTextFontSize,
          maxBadgeTextFontSize: maxBadgeTextFontSize,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          partyIcon: identity.partyIconUrl,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
          showBadgeRibbon: identity.showBadgeRibbon,
        );
      case PosterIdentityType.polygonalProfileIdentity:
        return PolygonalProfileIdentity(
          name: identity.user.name,
          badge: identity.user.badge,
          footerGradients: gradients.footerGradients,
          upperFooterGradients: gradients.upperFooterGradients,
          badgeBannerGradients: gradients.badgeBannerGradients,
          borderGradients: gradients.identityBorderGradients,
          nameTextColor: nameTextColor,
          badgeTextColor: badgeTextColor,
          minBadgeTextFontSize: _minBadgeTextFontSize,
          maxBadgeTextFontSize: maxBadgeTextFontSize,
          minNameFontSize: _minFontSize,
          maxNameFontSize: _maxFontSize,
          imageUrl: identity.user.photoUrl,
          isGoldenFrame: isGoldenFrame,
          nameFontConfig: nameFontConfig,
          badgeFontConfig: badgeFontConfig,
          showBadgeRibbon: identity.showBadgeRibbon,
        );
      default:
        return const SizedBox();
    }
  }

  @override
  Widget build(BuildContext context) {
    return _getIdentityWidget();
  }
}
