import 'package:praja_posters/src/auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:praja_posters/src/constants.dart';
import 'package:praja_posters/src/models/poster_font_config.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/models/poster_badge.dart';
import 'package:praja_posters/src/ui_widgets/poster_flat_badge_ribbon.dart';
import 'package:praja_posters/src/ui_widgets/premium_frame_identity_bg.dart';
import 'package:praja_posters/src/extensions/poster_gradient_extension.dart';
import 'package:praja_posters/src/utils/font_utils.dart';

/// This Supports [PosterIdentityType.curved] and [PosterIdentityType.curvedWithDepth]
class PremiumFrameIdentity extends StatelessWidget {
  final String name;
  final PosterBadge? badge;
  final PosterGradient? backgroundShadowGradient;
  final PosterGradient backgroundGradient;
  final PosterGradient? badgeRibbonBackgroundGradients;
  final PosterGradient? badgeGradient;
  final double minNameFontSize;
  final double maxNameFontSize;
  final double minBadgeTextFontSize;
  final double maxBadgeTextFontSize;
  final int nameTextColor;
  final int badgeTextColor;
  final bool showTextShadow;
  final bool showDepthGradients;
  final bool fullWidth;
  final PosterFontConfig? nameFontConfig;
  final PosterFontConfig? badgeFontConfig;
  final bool showBadgeRibbon;

  const PremiumFrameIdentity({
    super.key,
    required this.name,
    this.badge,
    required this.backgroundGradient,
    this.backgroundShadowGradient,
    this.badgeRibbonBackgroundGradients,
    this.badgeGradient,
    this.minNameFontSize = 14,
    this.maxNameFontSize = 22,
    this.minBadgeTextFontSize = 9,
    this.maxBadgeTextFontSize = 11,
    required this.nameTextColor,
    required this.badgeTextColor,
    this.showTextShadow = true,
    this.showDepthGradients = false,
    this.fullWidth = false,
    this.nameFontConfig,
    this.badgeFontConfig,
    required this.showBadgeRibbon,
  });

  Widget _buildRibbonWidget() {
    final badge = this.badge;
    final showBadgeStrip =
        badge != null && badge.active && badge.description.isNotEmpty;
    final badgeGradient = this.badgeGradient;
    if (badgeGradient == null) {
      return const SizedBox();
    }
    if (showBadgeStrip) {
      return PosterFlatBadgeRibbon(
        text: badge.description,
        outlineType: badge.badgeBanner,
        backgroundGradient: badgeGradient,
        minBadgeTextFontSize: minBadgeTextFontSize,
        maxBadgeTextFontSize: maxBadgeTextFontSize,
        badgeTextColor: badgeTextColor,
        badgeFontConfig: badgeFontConfig,
      );
    } else {
      return const SizedBox();
    }
  }

  Widget _buildBadgeRoleWidget() {
    final badge = this.badge;
    if (badge == null || !badge.active || badge.description.isEmpty) {
      return const SizedBox();
    }
    return AutoSizeText(
      badge.description,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: minBadgeTextFontSize,
      maxFontSize: maxBadgeTextFontSize,
      textScaler: const TextScaler.linear(1.0),
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color: Color(badgeTextColor),
        fontSize: maxBadgeTextFontSize,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: badgeFontConfig),
      ),
    );
  }

  Widget _buildNameWidget() {
    final nameBrightness =
        ThemeData.estimateBrightnessForColor(Color(nameTextColor));
    return AutoSizeText(
      name,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: minNameFontSize,
      maxFontSize: maxNameFontSize,
      textScaler: const TextScaler.linear(1.0),
      style: TextStyle(
        shadows: showTextShadow
            ? [
                Shadow(
                  color: nameBrightness == Brightness.light
                      ? Colors.black
                      : Colors.white,
                  offset: const Offset(-2, 0),
                )
              ]
            : null,
        color: Color(nameTextColor),
        fontSize: !fullWidth
            ? maxNameFontSize
            : minNameFontSize + (maxNameFontSize - minNameFontSize) / 2,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: nameFontConfig),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final badge = this.badge;
    final hasBadge =
        badge != null && badge.active && badge.description.isNotEmpty;
    return AspectRatio(
      aspectRatio: 320 / 84,
      child: Stack(
        children: [
          if (showDepthGradients)
            Positioned.fill(
                child: Transform.translate(
                    offset: const Offset(0, -8),
                    child: PremiumFrameIdentityBg(
                        gradient: backgroundShadowGradient!))),
          Positioned.fill(
              child: PremiumFrameIdentityBg(gradient: backgroundGradient)),
          if (badge != null &&
              badge.description.isNotEmpty &&
              showBadgeRibbon) ...[
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                height: badgeRibbonBackgroundContainerHeight,
                decoration: BoxDecoration(
                  gradient: badgeRibbonBackgroundGradients?.toGradient(),
                  backgroundBlendMode: badgeRibbonBackgroundGradients == null
                      ? null
                      : BlendMode.luminosity,
                ),
              ),
            ),
          ],
          Positioned.fill(
            bottom: 0,
            child: Padding(
              padding: const EdgeInsets.only(left: 8, right: 8, bottom: 8),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Expanded(
                    flex: hasBadge ? 75 : 100,
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Expanded(
                          flex: fullWidth ? 100 : 56,
                          child: Container(
                            alignment: fullWidth
                                ? Alignment.bottomCenter
                                : Alignment.center,
                            padding: EdgeInsets.only(
                              bottom: fullWidth ? 16 : 0,
                              top: fullWidth ? 0 : 4,
                            ),
                            child: _buildNameWidget(),
                          ),
                        ),
                        Expanded(
                          flex: fullWidth ? 0 : 44,
                          child: const SizedBox(),
                        )
                      ],
                    ),
                  ),
                  Expanded(
                    flex: hasBadge ? 20 : 0,
                    child: showBadgeRibbon
                        ? _buildRibbonWidget()
                        : _buildBadgeRoleWidget(),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}

final blueBgGradient = PosterGradient(
    colorsAsInt: [0xFF05305E, 0xE83490F3, 0xFF05305E],
    stops: [0.04, 0.4, 1.0],
    directions: PosterGradientDirection(
        beginX: -1.0, beginY: 0.0, endX: 1.0, endY: 0.0));

class PremiumFrameIdentityDemo extends StatelessWidget {
  const PremiumFrameIdentityDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      PremiumFrameIdentity(
        name: 'సూర్యనారాయణ',
        backgroundGradient: ycpPartyGradients,
        badgeGradient: blueBgGradient,
        badge: PosterBadge(
          id: 1,
          badgeRole: 'సర్పంచ్ కొండపల్లి',
          badgeBanner: PosterBadgeBanner.silver,
          description: 'Kondappli Sarpanch',
          badgeRing: PosterBadgeRing.silverRing,
          badgeIconUrl: '',
          active: true,
        ),
        nameTextColor: 4294967295,
        badgeTextColor: 4294967295,
        showBadgeRibbon: true,
      ),
      const SizedBox(height: 16),
      PremiumFrameIdentity(
        name: 'సూర్యనారాయణ',
        backgroundGradient: ycpPartyGradients,
        badgeGradient: blueBgGradient,
        badge: PosterBadge(
          id: 1,
          badgeRole: 'సర్పంచ్ కొండపల్లి',
          badgeBanner: PosterBadgeBanner.silver,
          description: 'Kondappli Sarpanch',
          badgeRing: PosterBadgeRing.silverRing,
          badgeIconUrl: '',
          active: true,
        ),
        nameTextColor: 4294967295,
        badgeTextColor: 4294967295,
        showBadgeRibbon: true,
      ),
      const SizedBox(height: 16),
      PremiumFrameIdentity(
        name: 'డా. మొండితోక అరుణ్ కుమార్',
        backgroundGradient: ycpPartyGradients,
        badgeGradient: blueBgGradient,
        badge: PosterBadge(
          id: 1,
          badgeRole: 'ఆంధ్రప్రదేశ్ టూరిజం&సాంస్కృతిక శాఖ మంత్రి',
          badgeBanner: PosterBadgeBanner.silver,
          description: 'Kondappli Sarpanch',
          badgeRing: PosterBadgeRing.silverRing,
          badgeIconUrl: '',
          active: true,
        ),
        nameTextColor: 4294967295,
        badgeTextColor: 4294967295,
        showBadgeRibbon: true,
      ),
    ]);
  }
}
