import 'package:flutter/material.dart';
import 'package:praja_posters/src/auto_size_text/auto_size_text.dart';
import 'package:praja_posters/src/extensions/poster_gradient_extension.dart';
import 'package:praja_posters/src/models/poster_badge.dart';
import 'package:praja_posters/src/models/poster_font_config.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/ui_widgets/poster_flat_badge_ribbon.dart';
import 'package:praja_posters/src/ui_widgets/praja_poster_image.dart';
import 'package:praja_posters/src/utils/font_utils.dart';

const double _partyTagWidth = 98;

class PartyTagIdentity extends StatelessWidget {
  final String name;
  final PosterBadge? badge;
  final PosterGradient footerGradients;
  final PosterGradient? upperFooterGradients;
  final PosterGradient? badgeBannerGradients;
  final PosterGradient partyTagBackgroundGradients;
  final double minNameFontSize;
  final double maxNameFontSize;
  final double minBadgeTextFontSize;
  final double maxBadgeTextFontSize;
  final int nameTextColor;
  final int badgeTextColor;
  final String? partyIcon;
  final PosterFontConfig? nameFontConfig;
  final PosterFontConfig? badgeFontConfig;
  final bool showBadgeRibbon;

  const PartyTagIdentity({
    super.key,
    required this.name,
    this.badge,
    required this.footerGradients,
    this.upperFooterGradients,
    this.badgeBannerGradients,
    required this.partyTagBackgroundGradients,
    this.minNameFontSize = 14,
    this.maxNameFontSize = 22,
    this.minBadgeTextFontSize = 9,
    this.maxBadgeTextFontSize = 11,
    required this.nameTextColor,
    required this.badgeTextColor,
    this.partyIcon,
    this.nameFontConfig,
    this.badgeFontConfig,
    required this.showBadgeRibbon,
  });

  Widget _buildRibbonWidget() {
    final badge = this.badge;
    final showBadgeStrip =
        badge != null && badge.active && badge.description.isNotEmpty;
    final badgeBannerGradients = this.badgeBannerGradients;
    if (badgeBannerGradients == null) {
      return const SizedBox();
    }
    return showBadgeStrip
        ? PosterFlatBadgeRibbon(
            text: badge.description,
            outlineType: badge.badgeBanner,
            backgroundGradient: badgeBannerGradients,
            minBadgeTextFontSize: minBadgeTextFontSize,
            maxBadgeTextFontSize: maxBadgeTextFontSize,
            badgeTextColor: badgeTextColor,
            badgeFontConfig: badgeFontConfig,
          )
        : const SizedBox();
  }

  Widget _buildBadgeRoleWidget() {
    final badge = this.badge;
    if (badge == null || !badge.active || badge.description.isEmpty) {
      return const SizedBox();
    }
    return AutoSizeText(
      badge.description,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: minBadgeTextFontSize,
      maxFontSize: maxBadgeTextFontSize,
      textScaler: const TextScaler.linear(1.0),
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color: Color(badgeTextColor),
        fontSize: maxBadgeTextFontSize,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: badgeFontConfig),
      ),
    );
  }

  Widget _buildNameWidget() {
    final nameBrightness =
        ThemeData.estimateBrightnessForColor(Color(nameTextColor));
    return AutoSizeText(
      name,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: minNameFontSize,
      maxFontSize: maxNameFontSize,
      textScaler: const TextScaler.linear(1.0),
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        shadows: [
          Shadow(
            color: nameBrightness == Brightness.light
                ? Colors.black
                : Colors.white,
            offset: const Offset(-2, 0),
            blurRadius: 0,
          ),
        ],
        color: Color(nameTextColor),
        fontSize: maxNameFontSize,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: nameFontConfig),
      ),
    );
  }

  Widget _getPartyTag() {
    final partyIcon = this.partyIcon;
    if (partyIcon == null) {
      return const SizedBox();
    }
    return Stack(
      children: [
        Container(
          height: 106,
          width: _partyTagWidth,
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(50)),
            boxShadow: [
              BoxShadow(
                color: Color(0x3F000000),
                blurRadius: 4,
                offset: Offset(4, 0),
                spreadRadius: 0,
              )
            ],
          ),
          child: SemiCircularPartyTagBg(
            gradient: partyTagBackgroundGradients,
          ),
        ),
        Positioned(
          top: 24,
          left: 10,
          child: PrajaPosterImage(
            imageUrl: partyIcon,
            width: 70,
            height: 70,
            alignment: Alignment.topCenter,
          ),
        )
      ],
    );
  }

  Widget _getBody() {
    final upperFooterGradients = this.upperFooterGradients;
    final badge = this.badge;
    final hasBadge =
        badge != null && badge.active && badge.description.isNotEmpty;
    if (upperFooterGradients == null) {
      return const SizedBox();
    }
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          height: 106,
          decoration: BoxDecoration(
            gradient: footerGradients.toGradient(),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                flex: hasBadge ? 55 : 100,
                child: Container(
                  width: double.infinity,
                  alignment: Alignment.center,
                  padding: EdgeInsets.only(
                    left: partyIcon != null ? _partyTagWidth + 10 : 0,
                    right: 6.0,
                  ),
                  decoration: BoxDecoration(
                    gradient: upperFooterGradients.toGradient(),
                  ),
                  child: _buildNameWidget(),
                ),
              ),
              if (hasBadge) ...[
                Expanded(
                  flex: 45,
                  child: Padding(
                    padding: EdgeInsets.only(
                      left: partyIcon != null ? _partyTagWidth + 10 : 8.0,
                      right: 8.0,
                      top: 10.0,
                      bottom: 8.0,
                    ),
                    child: showBadgeRibbon
                        ? _buildRibbonWidget()
                        : _buildBadgeRoleWidget(),
                  ),
                ),
              ],
            ],
          ),
        ),
        Positioned(
          left: 0,
          top: 0,
          child: _getPartyTag(),
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return _getBody();
  }
}

class SemiCircularPartyTagBg extends StatelessWidget {
  final PosterGradient gradient;
  const SemiCircularPartyTagBg({super.key, required this.gradient});

  @override
  Widget build(BuildContext context) {
    return ClipPath(
      clipper: _SemiCircularPartyTagClipper(),
      child: Container(
        height: double.infinity,
        width: double.infinity,
        decoration: BoxDecoration(
          gradient: gradient.toGradient(),
        ),
      ),
    );
  }
}

class _SemiCircularPartyTagClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    double radius = size.height / 2;
    Path path = Path();
    path.moveTo(0, size.height);
    path.lineTo(size.width - radius, size.height);
    path.arcToPoint(
      Offset(size.width - radius, 0),
      radius: Radius.circular(radius),
      clockwise: false,
    );
    path.lineTo(0, 0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
