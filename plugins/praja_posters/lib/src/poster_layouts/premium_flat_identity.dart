import 'package:praja_posters/src/auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:praja_posters/src/constants.dart';
import 'package:praja_posters/src/models/poster_font_config.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/models/poster_badge.dart';
import 'package:praja_posters/src/ui_widgets/poster_flat_badge_ribbon.dart';
import 'package:praja_posters/src/ui_widgets/praja_poster_image.dart';
import 'package:praja_posters/src/extensions/poster_gradient_extension.dart';
import 'package:praja_posters/src/utils/font_utils.dart';

class PremiumFlatIdentity extends StatelessWidget {
  final String name;
  final PosterBadge? badge;
  final PosterGradient footerGradients;
  final PosterGradient? badgeBannerGradients;
  final PosterGradient? badgeRibbonBackgroundGradients;
  final double minNameFontSize;
  final double maxNameFontSize;
  final double minBadgeTextFontSize;
  final double maxBadgeTextFontSize;
  final int nameTextColor;
  final int badgeTextColor;
  final String? partyIcon;
  final bool fullWidth;
  final PosterFontConfig? nameFontConfig;
  final PosterFontConfig? badgeFontConfig;
  final bool showBadgeRibbon;

  const PremiumFlatIdentity({
    super.key,
    required this.name,
    this.badge,
    required this.footerGradients,
    this.badgeBannerGradients,
    this.badgeRibbonBackgroundGradients,
    this.minNameFontSize = 14,
    this.maxNameFontSize = 22,
    this.minBadgeTextFontSize = 9,
    this.maxBadgeTextFontSize = 11,
    required this.nameTextColor,
    required this.badgeTextColor,
    this.partyIcon,
    this.fullWidth = true,
    this.nameFontConfig,
    this.badgeFontConfig,
    required this.showBadgeRibbon,
  });

  Widget _buildRibbonWidget() {
    final badge = this.badge;
    final showBadgeStrip =
        badge != null && badge.active && badge.description.isNotEmpty;
    final badgeBannerGradients = this.badgeBannerGradients;
    if (badgeBannerGradients == null) {
      return const SizedBox();
    }
    return showBadgeStrip
        ? PosterFlatBadgeRibbon(
            text: badge.description,
            outlineType: badge.badgeBanner,
            backgroundGradient: badgeBannerGradients,
            minBadgeTextFontSize: minBadgeTextFontSize,
            maxBadgeTextFontSize: maxBadgeTextFontSize,
            badgeTextColor: badgeTextColor,
            badgeFontConfig: badgeFontConfig,
          )
        : const SizedBox();
  }

  Widget _buildBadgeRoleWidget() {
    final badge = this.badge;
    if (badge == null || !badge.active || badge.description.isEmpty) {
      return const SizedBox();
    }
    return AutoSizeText(
      badge.description,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: minBadgeTextFontSize,
      maxFontSize: maxBadgeTextFontSize,
      textScaler: const TextScaler.linear(1.0),
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color: Color(badgeTextColor),
        fontSize: maxBadgeTextFontSize,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: badgeFontConfig),
      ),
    );
  }

  Widget _buildNameWidget() {
    final nameBrightness =
        ThemeData.estimateBrightnessForColor(Color(nameTextColor));
    const availableNamePlateWidth = posterWidth -
        posterUserImageWidth -
        spaceBetweenNameAndPartyIcon -
        emptySizedBoxWidth -
        (2 * frameBorderWidth) -
        contentLeftPadding;
    const fullWidthNameWithPartyIcon =
        fullWidthNameSize - (partyIconWidth + spaceBetweenNameAndPartyIcon);
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: fullWidth
          ? MainAxisAlignment.center
          : partyIcon == null
              ? MainAxisAlignment.center
              : MainAxisAlignment.end,
      children: [
        if (partyIcon != null) ...[
          const Flexible(
            child: SizedBox(),
          ),
          PrajaPosterImage(
            imageUrl: partyIcon!,
            width: 50,
            height: 50,
            fit: BoxFit.fitWidth,
            alignment: Alignment.topCenter,
          ),
          const SizedBox(
            width: 16,
          ),
        ],
        Container(
          constraints: fullWidth
              ? BoxConstraints(
                  maxWidth: partyIcon != null
                      ? fullWidthNameWithPartyIcon
                      : fullWidthNameSize)
              : BoxConstraints(
                  maxWidth: partyIcon != null
                      ? availableNamePlateWidth -
                          (partyIconWidth + spaceBetweenNameAndPartyIcon)
                      : availableNamePlateWidth,
                ),
          child: AutoSizeText(
            name,
            maxLines: 1,
            textAlign: TextAlign.center,
            minFontSize: minNameFontSize,
            maxFontSize: maxNameFontSize,
            textScaler: const TextScaler.linear(1.0),
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              shadows: [
                Shadow(
                  color: nameBrightness == Brightness.light
                      ? Colors.black
                      : Colors.white,
                  offset: const Offset(-2, 0),
                  blurRadius: 0,
                ),
              ],
              color: Color(nameTextColor),
              fontSize: !fullWidth
                  ? maxNameFontSize
                  : minNameFontSize + (maxNameFontSize - minNameFontSize) / 2,
              fontWeight: FontWeight.bold,
              fontFamily: FontUtils.getFontFamily(fontConfig: nameFontConfig),
            ),
          ),
        ),
        if (partyIcon != null) ...[
          SizedBox(
            width: fullWidth ? 0 : 16,
          ),
        ],
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final badge = this.badge;
    final showBadgeStrip =
        badge != null && badge.active && badge.description.isNotEmpty;
    return SizedBox(
      height: showBadgeStrip ? 108 : 90,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Positioned.fill(
            child: PremiumFlatIdentityBg(
              backgroundGradient: footerGradients,
            ),
          ),
          if (showBadgeStrip && showBadgeRibbon) ...[
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                height: badgeRibbonBackgroundContainerHeight,
                decoration: BoxDecoration(
                  gradient: badgeRibbonBackgroundGradients?.toGradient(),
                  backgroundBlendMode: badgeRibbonBackgroundGradients == null
                      ? null
                      : BlendMode.luminosity,
                ),
                transform: Matrix4.rotationZ(0),
              ),
            ),
          ],
          Positioned.fill(
            bottom: 0,
            child: Padding(
              padding: EdgeInsets.only(
                bottom: showBadgeStrip ? 6 : 0,
                left: 12,
                right: 12,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: showBadgeStrip
                    ? MainAxisAlignment.end
                    : MainAxisAlignment.center,
                children: [
                  Expanded(
                    flex: showBadgeStrip ? 50 : 100,
                    child: Row(
                      children: [
                        Expanded(
                            child: Padding(
                          padding: EdgeInsets.only(
                            bottom: showBadgeStrip ? 4 : 0,
                          ),
                          child: _buildNameWidget(),
                        )),
                        SizedBox(
                          width: fullWidth ? 0 : posterUserImageWidth,
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    flex: showBadgeStrip ? 25 : 0,
                    child: Padding(
                      padding: EdgeInsets.only(left: partyIcon != null ? 4 : 0),
                      child: showBadgeRibbon
                          ? _buildRibbonWidget()
                          : _buildBadgeRoleWidget(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class PremiumFlatIdentityBg extends StatelessWidget {
  final PosterGradient backgroundGradient;
  const PremiumFlatIdentityBg({
    super.key,
    required this.backgroundGradient,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
        height: double.infinity,
        width: double.infinity,
        decoration: BoxDecoration(
          gradient: backgroundGradient.toGradient(),
        ));
  }
}
