import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:praja_posters/src/auto_size_text/auto_size_text.dart';
import 'package:praja_posters/src/models/poster_font_config.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';
import 'package:praja_posters/src/models/poster_badge.dart';
import 'package:praja_posters/src/ui_widgets/poster_flat_badge_ribbon.dart';
import 'package:praja_posters/src/utils/font_utils.dart';

class GlassyIdentity extends StatelessWidget {
  final String name;
  final PosterBadge? badge;
  final PosterGradient footerGradients;
  final PosterGradient? badgeBannerGradients;
  final double minNameFontSize;
  final double maxNameFontSize;
  final double minBadgeTextFontSize;
  final double maxBadgeTextFontSize;
  final int nameTextColor;
  final int badgeTextColor;
  final bool fullWidth;
  final PosterFontConfig? nameFontConfig;
  final PosterFontConfig? badgeFontConfig;
  final bool showBadgeRibbon;

  const GlassyIdentity({
    super.key,
    required this.name,
    this.badge,
    required this.footerGradients,
    this.badgeBannerGradients,
    this.minNameFontSize = 14,
    this.maxNameFontSize = 30,
    this.minBadgeTextFontSize = 9,
    this.maxBadgeTextFontSize = 11,
    required this.nameTextColor,
    required this.badgeTextColor,
    this.fullWidth = false,
    this.nameFontConfig,
    this.badgeFontConfig,
    required this.showBadgeRibbon,
  });

  Widget _buildRibbonWidget(BuildContext context) {
    final badge = this.badge;
    final showBadgeStrip =
        badge != null && badge.active && badge.description.isNotEmpty;
    final badgeBannerGradients = this.badgeBannerGradients;
    if (badgeBannerGradients == null) {
      return const SizedBox();
    }
    return showBadgeStrip
        ? Column(children: [
            const SizedBox(height: 6),
            PosterFlatBadgeRibbon(
              text: badge.description,
              outlineType: badge.badgeBanner,
              backgroundGradient: badgeBannerGradients,
              minBadgeTextFontSize: minBadgeTextFontSize,
              maxBadgeTextFontSize: maxBadgeTextFontSize,
              badgeTextColor: badgeTextColor,
              badgeFontConfig: badgeFontConfig,
            )
          ])
        : const SizedBox();
  }

  Widget _buildBadgeRoleWidget() {
    final badge = this.badge;
    if (badge == null || !badge.active || badge.description.isEmpty) {
      return const SizedBox();
    }
    return Container(
      padding: const EdgeInsets.only(top: 6),
      child: AutoSizeText(
        badge.description,
        maxLines: 1,
        textAlign: TextAlign.center,
        minFontSize: minBadgeTextFontSize,
        maxFontSize: maxBadgeTextFontSize,
        textScaler: const TextScaler.linear(1.0),
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          color: Color(badgeTextColor),
          fontSize: maxBadgeTextFontSize,
          fontWeight: FontWeight.bold,
          fontFamily: FontUtils.getFontFamily(fontConfig: badgeFontConfig),
        ),
      ),
    );
  }

  Widget _buildNameWidget(BuildContext context) {
    return AutoSizeText(
      name,
      maxLines: 1,
      textAlign: TextAlign.center,
      minFontSize: minNameFontSize,
      maxFontSize: maxNameFontSize,
      textScaler: const TextScaler.linear(1.0),
      style: TextStyle(
        shadows: const [
          Shadow(
            color: Colors.white,
            offset: Offset(-2, 0),
            blurRadius: 0,
          ),
        ],
        color: Colors.black,
        fontSize: !fullWidth
            ? maxNameFontSize
            : minNameFontSize + (maxNameFontSize - minNameFontSize) / 2,
        fontWeight: FontWeight.bold,
        fontFamily: FontUtils.getFontFamily(fontConfig: nameFontConfig),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final badge = this.badge;
    final showBadgeStrip =
        badge != null && badge.active && badge.description.isNotEmpty;
    return SizedBox(
      height: showBadgeStrip ? 104 : 80,
      width: 610,
      child: Stack(
        children: [
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withOpacity(0.25),
                        Colors.white.withOpacity(0.6),
                        Colors.white.withOpacity(0.7),
                        Colors.white.withOpacity(0.6),
                        Colors.white.withOpacity(0.9),
                      ],
                      stops: const [0.0, 0.25, 0.5, 0.75, 1.0],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                    border: Border.all(
                      color: Colors.white,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Image.asset(
                    "assets/images/glassy.png",
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
          ),
          Positioned.fill(
            child: Padding(
              padding: const EdgeInsets.only(left: 12.0, right: 12.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    flex: showBadgeStrip ? 60 : 100,
                    child: Row(
                      children: [
                        Expanded(
                            flex: fullWidth ? 100 : 60,
                            child: _buildNameWidget(context)),
                        Expanded(
                          flex: fullWidth ? 0 : 40,
                          child: const SizedBox(),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    flex: showBadgeStrip ? 40 : 0,
                    child: showBadgeRibbon
                        ? _buildRibbonWidget(context)
                        : _buildBadgeRoleWidget(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
