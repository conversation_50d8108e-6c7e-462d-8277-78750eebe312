import 'package:google_fonts/google_fonts.dart';
import 'package:praja_posters/src/extensions/poster_font_extension.dart';
import 'package:praja_posters/src/models/poster_font_config.dart';

class FontUtils {
  static String? getFontFamily({required PosterFontConfig? fontConfig}) {
    if (fontConfig == null) {
      return null;
    }
    final fontFamily = fontConfig.fontFamily;
    final fontStyle = fontConfig.fontStyle;
    final fontWeight = fontConfig.fontWeight;
    return GoogleFonts.getFont(fontFamily,
            fontStyle: fontConfig.toFontStyle(fontStyle: fontStyle),
            fontWeight: fontConfig.toFontWeight(fontWeight: fontWeight))
        .fontFamily;
  }
}
