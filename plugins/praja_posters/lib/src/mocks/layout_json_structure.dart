//Poster 3.0 -> Nov Version
Map<String, dynamic> layoutStructure = {
  "layout_type": "premium",
  "golden_frame": true,
  "shadow_color": null,
  "enable_outer_frame": true, //New set false to remove outer frame
  "v1": null,
  "share_text": " ",
  "gradients": {
    "background_gradients": {
      "colors": [4278226502, 4280466360, 4278347444],
      "stops": null,
      "directions": {
        "begin_x": 1.0,
        "begin_y": -1.0,
        "end_x": 1.0,
        "end_y": 1.0
      }
    },
    "inner_background_gradients": {},
    "badge_banner_gradients": {
      "colors": [4278205290, 4278347444, 4278347444, 4278205290],
      "stops": [0.0, 0.3368, 0.6826, 1.0],
      "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
    },
    "footer_gradients": {
      "colors": [4278226502, 4280466360, 4278347444],
      "stops": null,
      "directions": {"begin_x": 1.0, "begin_y": 0, "end_x": -1.0, "end_y": 0}
    },
    "upper_footer_gradients": {
      "colors": [4278205290, 4278347444, 4278347444, 4278205290],
      "stops": [0.0, 0.3368, 0.6826, 1.0],
      "directions": {"begin_x": -1.0, "begin_y": 0, "end_x": 1.0, "end_y": 0}
    }, //New
    "badge_ribbon_background_gradients": {
      "colors": [2164025858, 2163337504, 2164260863, 2164025858, 2163337504],
      "stops": [-0.0389, 0.235, 0.4109, 0.7924, 1.0421],
      "directions": {
        "begin_x": -1.0,
        "begin_y": 0.0,
        "end_x": 1.0,
        "end_y": -0.0
      }
    },
    "identity_border_gradients": {
      "colors": [
        0xFF9B7B0C,
        0xFFD1A818,
        0xFFF3D978,
        0xFFFFF4C3,
        0xFFFFFFFF,
        0xFFFFF4C3,
        0xFFF3D979,
        0xFFD1A818,
        0xFF9B7B0C
      ],
      "stops": [
        0.0141,
        0.1252,
        0.3134,
        0.4424,
        0.5285,
        0.6468,
        0.7705,
        0.9587,
        1.0465
      ],
      "directions": {
        "begin_x": 1.0,
        "begin_y": 0.0,
        "end_x": -1.0,
        "end_y": -0.0
      }
    }, //New
    "identity_inner_background_gradients": {}, //New
    "party_icon_background_gradients": {}, //New
  },
  "text_color": 0xffffffff,
  "badge_text_color": 0xffffffff,
  "identity": {
    "user": {
      "id": 690291,
      "name": "సూర్య",
      "photo_url":
          "https://cdn.thecircleapp.in/production/admin-media/40/e74376a54a37549d2df99608c2f9374d.png",
      "badge": {
        "id": 30359,
        "active": true,
        "icon_url":
            "https://g-cdn.thecircleapp.in/fit-in/100x100/filters:quality(80)/production/admin-media/15/3d299fa15975c1fb5be6692e7c24b0a1.png",
        "badge_banner": "SILVER",
        "badge_icon":
            "https://g-cdn.thecircleapp.in/fit-in/100x100/filters:quality(80)/production/admin-media/15/3d299fa15975c1fb5be6692e7c24b0a1.png",
        "badge_ring": "SILVER_RING",
        "badge_text": "జిల్లా అధ్యక్షులు",
        "description": "ఉమ్మడి గుంటూరు జిల్లా అధ్యక్షులు",
        "is_celebrated": true,
        "grade_level": 3,
        "role_id": 55
      }
    },
    "type": "polygonal_profile_identity",
    "is_user_position_back": false,
    "party_icon_url": null,
    "party_highlight_color_primary": 0xffffffff, //new
    "party_highlight_color_secondary": 0xffffffff, //new
    "slogan_text": "జై జగన్", //new
    "slogan_icon_url": "image Url", //new
  },
  "party_icon": {
    "url":
        "https://g-cdn.thecircleapp.in/production/admin-media/10/2c5342f966f026693e80b598492d7524.png",
    "position": "left",
    "gradients": {
      "directions": {
        "begin_x": 0.0,
        "begin_y": -1.0,
        "end_x": 0.0,
        "end_y": 1.0
      },
      "colors": [4278226502, 4280466360, 4278347444],
      "stops": null
    },
  },
  "header_1_photos": [
    {
      "radius": 75,
      "position_x": 32,
      "position_y": 32,
      "photo_url":
          "https://cdn.thecircleapp.in/production/admin-media/4/9c462919aac3ebea40073221d8a1bb7e.png"
    },
    {
      "radius": 75,
      "position_x": 448,
      "position_y": 32,
      "photo_url":
          "https://cdn.thecircleapp.in/production/admin-media/4/435dfdbccafc37185329e076ecf8f981.png"
    }
  ],
  "header_2_photos": [
    {
      "radius": 40.18,
      "position_x": 185.24,
      "position_y": 47.83,
      "photo_url":
          "https://cdn.thecircleapp.in/production/admin-media/4/94996f55dac0a3065e063f3781560c76.png"
    },
    {
      "radius": 40.18,
      "position_x": 269.24,
      "position_y": 47.83,
      "photo_url":
          "https://cdn.thecircleapp.in/production/admin-media/4/802ab6ff7fc4c7f0af5df468787fe391.png"
    },
    {
      "radius": 40.18,
      "position_x": 353.24,
      "position_y": 47.83,
      "photo_url":
          "https://cdn.thecircleapp.in/production/admin-media/4/e438f73c695f486ad086d1ee042580dd.png"
    },
    {
      "radius": 40.18,
      "position_x": 209.24,
      "position_y": 130.83,
      "photo_url":
          "https://cdn.thecircleapp.in/production/admin-media/4/601058bd8b2779f7ae8b2f8ca2dc7bf0.png"
    },
    {
      "radius": 40.18,
      "position_x": 293.24,
      "position_y": 130.83,
      "photo_url":
          "https://cdn.thecircleapp.in/production/admin-media/4/902fe1c8db1cd46b9a6533b358a07879.png"
    },
    {
      "radius": 40.18,
      "position_x": 377.24,
      "position_y": 130.83,
      "photo_url":
          "https://cdn.thecircleapp.in/production/admin-media/4/24a4c8299451e2d126bc68683edec8e5.png"
    }
  ],
  "subscription_screen": null,
  "is_locked": true,
  "is_bordered_layout": false,
  "frame_identifier": "gold_curved_identity",
  "analytics_params": {
    "layout_type": "premium",
    "is_locked": true,
    "golden_frame": true,
    "header_1_count": 2,
    "header_2_count": 6,
    "identity_type": "curved_with_depth",
    "is_user_position_back": false,
    "party_icon_position": "left"
  }
};
