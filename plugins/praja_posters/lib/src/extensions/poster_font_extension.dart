import 'dart:ui';

import 'package:praja_posters/src/models/poster_font_config.dart';

extension PosterFontConfigX on PosterFontConfig {
  FontWeight? toFontWeight({required String fontWeight}) {
    switch (fontWeight) {
      case "w100":
        return FontWeight.w100;
      case "w200":
        return FontWeight.w200;
      case "w300":
        return FontWeight.w300;
      case "w400":
        return FontWeight.w400;
      case "w500":
        return FontWeight.w500;
      case "w600":
        return FontWeight.w600;
      case "w700":
        return FontWeight.w700;
      case "w800":
        return FontWeight.w800;
      case "w900":
        return FontWeight.w900;
      default:
        return FontWeight.w700;
    }
  }

  FontStyle? toFontStyle({required String fontStyle}) {
    switch (fontStyle) {
      case "italic":
        return FontStyle.italic;
      case "normal":
        return FontStyle.normal;
      default:
        return FontStyle.normal;
    }
  }
}
