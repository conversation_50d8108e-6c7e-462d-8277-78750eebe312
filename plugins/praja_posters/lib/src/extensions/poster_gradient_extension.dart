import 'dart:convert';

import 'package:flutter/rendering.dart';
import 'package:praja_posters/src/models/poster_gradient.dart';

extension PosterGradientX on PosterGradient {
  Gradient toGradient({GradientRotation? transform}) {
    return LinearGradient(
      colors: colorsAsInt.map((e) => Color(e)).toList(),
      begin: Alignment(directions.beginX, directions.beginY),
      end: Alignment(directions.endX, directions.endY),
      stops: stops,
      transform: transform,
    );
  }
}

// For mocking / demo widgets
const ycpPartyGradientsJson = '''
{
  "colors": [
      4278347444,
      4280466360,
      4278226502
  ],
  "stops": [
      0.1,
      0.4,
      1.0
  ],
  "directions": {
      "begin_x": -1.0,
      "begin_y": 0.5,
      "end_x": 1.0,
      "end_y": -0.5
  }
}
''';

final ycpPartyGradients =
    PosterGradient.fromJson(json.decode(ycpPartyGradientsJson));
