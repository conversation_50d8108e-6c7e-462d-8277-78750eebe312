name: Release 🏷️ - Create Tagged Release

on: workflow_dispatch

env:
  GH_TOKEN: ${{ github.token }}

jobs:
  create-release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Get Version
        id: version
        run: |
          echo "VERSION=$(ruby ./scripts/print_version.rb ./pubspec.yaml)" >> $GITHUB_OUTPUT
      - name: Get last version's tag
        id: lastversiontag
        run: |
          echo "LAST_VERSION_TAG=$(gh release list --exclude-drafts --exclude-pre-releases -L 1 | grep 'Latest' | awk '{print $(NF-1)}')" >> $GITHUB_OUTPUT
      - name: Create release
        env:
          LAST_VERSION_TAG: ${{ steps.lastversiontag.outputs.LAST_VERSION_TAG }}
          VERSION: ${{ steps.version.outputs.VERSION }}
        run: |
          # delete tag if exists just to be safe
          git tag -d "v-$VERSION" || true
          git tag "v-$VERSION"
          git push origin "v-$VERSION"
          gh release create "v-$VERSION" --generate-notes --verify-tag --latest --notes-start-tag $LAST_VERSION_TAG
      - name: Delete branch
        if: github.ref != 'refs/heads/master'
        run: |
          git push origin -d $GITHUB_REF # delete the release branch after release is created

