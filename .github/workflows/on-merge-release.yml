# Automatically raise a PR with the changes merged into release branch to master branch
name: Auto 🤖 - On PR Merged to release branch
on:
  pull_request:
    types:
      - closed
    branches:
      - 'release/**'
env:
  BASE_BRANCH: ${{ github.base_ref }} # release branch
  PR_NUMBER: ${{ github.event.pull_request.number }}
  PR_TITLE: ${{ github.event.pull_request.title }}
  TZ: +05:30 # Required for the Ruby script to pick the right date in version
  GH_TOKEN: ${{ github.token }}

jobs:
  update-version:
    if: github.event.pull_request.merged == true
    secrets: inherit
    uses: ./.github/workflows/update-version.yml
    with:
      commit_message: "[Bot] Bump version after #${{ github.event.pull_request.number }}"
      branch: ${{ github.base_ref }}
      flags: '--only-patch'

  backmerge-via-cherry-pick:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: master

      - name: Cherry Pick
        env:
          COMMIT_TO_BACKMERGE: ${{ github.sha }}
        run: |
          git switch -c "auto-backmerge-cp/$PR_NUMBER"
          git fetch origin $COMMIT_TO_BACKMERGE
          git -c user.email=<EMAIL> -c user.name=praja-ci cherry-pick $COMMIT_TO_BACKMERGE # might fail if conflicts are present
          git push origin HEAD
          gh pr create --title "Backmerge #$PR_NUMBER: $PR_TITLE" --body "Backmerge from $BASE_BRANCH to master" --base master --head $(git branch --show-current)
          gh pr merge --squash

  backmerge-via-base-branch:
    if: ${{ always() && needs.backmerge-via-cherry-pick.result == 'failure' }}
    needs: backmerge-via-cherry-pick
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.base_ref }}

      - name: Create PR from a backmerge branch
        continue-on-error: true
        run: |
          git switch -c "auto-backmerge-bb/$PR_NUMBER"
          git push origin HEAD
          gh pr create --title "Backmerge #$PR_NUMBER: $PR_TITLE" --body "Backmerge from $BASE_BRANCH to master" --base master --head $(git branch --show-current)
          gh pr merge --squash

  internal-release:
    needs: update-version
    secrets: inherit
    uses: ./.github/workflows/internal-release.yml
    with:
      ref: ${{ github.base_ref }}
      send_release_notes: true
