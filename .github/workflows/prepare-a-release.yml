name: Release 🚀 - Cut branch

on: workflow_dispatch

env:
  GH_TOKEN: ${{ github.token }}

jobs:
  create-release-branch:
    runs-on: self-hosted
    outputs:
      version: ${{ steps.version.outputs.VERSION }}
    steps:
      - name: Checkout master
        uses: actions/checkout@v4
        with:
          ref: master
      - name: Get Version
        id: version
        run: |
          echo "VERSION=$(ruby ./scripts/print_version.rb ./pubspec.yaml --exclude-patch)" >> $GITHUB_OUTPUT
      - name: Create a release branch
        env:
          VERSION: ${{ steps.version.outputs.VERSION }}
        run: |
          git switch -c "release/v-$VERSION"
          git push origin HEAD
  internal-release:
    needs: create-release-branch
    uses: ./.github/workflows/internal-release.yml
    secrets: inherit
    with:
      ref: "release/v-${{ needs.create-release-branch.outputs.version }}"
