name: Release 🚀 - Android App Sharing Link
on:
  workflow_dispatch

jobs:
  private-link-release:
    runs-on: self-hosted
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Flutter
        run: |
          fvm install 3.19.6
          fvm use 3.19.6
          echo "Flutter Version: $(fvm flutter --version)"
      - name: Setup Fastlane for Android
        working-directory: android
        run: |
          bundle install
      - name: Setup Release Keys for Android
        working-directory: android
        run: |
          echo "$PLAY_STORE_UPLOAD_KEY" | base64 --decode > app/upload-keystore.jks
          echo "storeFile=upload-keystore.jks" >> key.properties
          echo "keyAlias=$KEYSTORE_KEY_ALIAS" >> key.properties
          echo "storePassword=$KEYSTORE_STORE_PASSWORD" >> key.properties
          echo "keyPassword=$KEYSTORE_KEY_PASSWORD" >> key.properties
        env:
          PLAY_STORE_UPLOAD_KEY: ${{ secrets.PLAY_STORE_UPLOAD_KEY }}
          KEYSTORE_KEY_ALIAS: ${{ secrets.KEYSTORE_KEY_ALIAS }}
          KEYSTORE_KEY_PASSWORD: ${{ secrets.KEYSTORE_KEY_PASSWORD }}
          KEYSTORE_STORE_PASSWORD: ${{ secrets.KEYSTORE_STORE_PASSWORD }}
      - name: Android App Sharing Link Release via Fastlane
        working-directory: android
        env:
          SLACK_API_TOKEN: ${{ secrets.SLACK_API_TOKEN }}
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
        run: |
          bundle exec fastlane private_link_release ref:${{ github.ref_name }} actor:${{ github.actor }}
