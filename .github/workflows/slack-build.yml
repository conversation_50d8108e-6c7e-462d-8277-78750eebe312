name: Slack ✉️ - Dev Build

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment'
        required: true
        type: choice
        default: 'production'
        options:
          - production
          - pre-prod
          - local
      local_ip:
        description: "Local IP"
        required: false
        default: ''
        type: string
      local_ror_ngrok_url:
        description: "Local ROR ngrok url"
        required: false
        default: ''
        type: string
      local_messaging_ngrok_url:
        description: "Local Messaging ngrok url"
        required: false
        default: ''
        type: string

env:
  GH_TOKEN: ${{ github.token }}

jobs:
  slack-build:
    runs-on: self-hosted
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Flutter
        run: |
          fvm install 3.19.6
          fvm use 3.19.6
          echo "Flutter Version: $(fvm flutter --version)"
      - name: Setup Fastlane for Android
        working-directory: android
        run: |
          bundle install
      - name: Build and Upload to Slack
        working-directory: android
        run: |
          bundle exec fastlane upload_build_to_slack env:${{inputs.environment}} local_ip:${{inputs.local_ip}} local_ror_ngrok_url:${{inputs.local_ror_ngrok_url}} local_messaging_ngrok_url:${{inputs.local_messaging_ngrok_url}} ref:${{ github.ref_name }} actor:${{ github.actor }} sha:${{ github.sha }}
        env:
          CI_TO_MEDIA_SERVICE_TOKEN: ${{ secrets.CI_TO_MEDIA_SERVICE_TOKEN }}
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
