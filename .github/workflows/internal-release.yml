name: Reusable 📦 - Internal Release
on:
  workflow_call:
    inputs:
      ref:
        required: true
        type: string
      send_release_notes:
        type: boolean
        default: true
env:
  GH_TOKEN: ${{ github.token }}

jobs:
  internal-release-android:
    concurrency: internal-release-android-${{ inputs.ref }} # cancels any pending internal release on the same branch
    runs-on: self-hosted
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}
      - name: Setup Flutter
        run: |
          fvm install 3.19.6
          fvm use 3.19.6
          echo "Flutter Version: $(fvm flutter --version)"
      - name: Setup Fastlane for Android
        working-directory: android
        run: |
          bundle install
      - name: Setup Release Keys for Android
        working-directory: android
        run: |
          echo "$PLAY_STORE_UPLOAD_KEY" | base64 --decode > app/upload-keystore.jks
          echo "storeFile=upload-keystore.jks" >> key.properties
          echo "keyAlias=$KEYSTORE_KEY_ALIAS" >> key.properties
          echo "storePassword=$KEYSTORE_STORE_PASSWORD" >> key.properties
          echo "keyPassword=$KEYSTORE_KEY_PASSWORD" >> key.properties
        env:
          PLAY_STORE_UPLOAD_KEY: ${{ secrets.PLAY_STORE_UPLOAD_KEY }}
          KEYSTORE_KEY_ALIAS: ${{ secrets.KEYSTORE_KEY_ALIAS }}
          KEYSTORE_KEY_PASSWORD: ${{ secrets.KEYSTORE_KEY_PASSWORD }}
          KEYSTORE_STORE_PASSWORD: ${{ secrets.KEYSTORE_STORE_PASSWORD }}
      - name: Android Internal Release via Fastlane
        working-directory: android
        env:
          SLACK_API_TOKEN: ${{ secrets.SLACK_API_TOKEN }}
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
        run: |
          bundle exec fastlane internal_release ref:${{ github.ref_name }} actor:${{ github.actor }}
  internal-release-ios:
    concurrency: internal-release-ios-${{ inputs.ref }} # cancels any pending internal release on the same branch
    runs-on: self-hosted
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}
      - name: Setup Flutter
        run: |
          fvm install 3.19.6
          fvm use 3.19.6
          echo "Flutter Version: $(fvm flutter --version)"
      - name: Setup Fastlane for iOS
        working-directory: ios
        run: |
          bundle install
          bundle exec pod repo update
      - name: iOS Beta Release via Fastlane
        working-directory: ios
        env:
          SLACK_API_TOKEN: ${{ secrets.SLACK_API_TOKEN }}
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          APP_STORE_CONNECT_PRIVATE_KEY: ${{ secrets.APP_STORE_CONNECT_PRIVATE_KEY }}
        run: |
          bundle exec fastlane beta ref:${{ github.ref_name }} actor:${{ github.actor }}
  send-release-notes:
    runs-on: ubuntu-latest
    if: ${{ inputs.send_release_notes }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}
      - name: Get last version's tag
        id: lastversiontag
        run: |
          echo "LAST_VERSION_TAG=$(gh release list --exclude-drafts --exclude-pre-releases -L 1 | grep 'Latest' | awk '{print $(NF-1)}')" >> $GITHUB_OUTPUT
      - name: Generate release notes
        id: releasenotes
        env:
          LAST_VERSION_TAG: ${{ steps.lastversiontag.outputs.LAST_VERSION_TAG }}
        run: |
          CURRENT_VERSION=$(ruby ./scripts/print_version.rb ./pubspec.yaml)
          # delete tag if exists just to be safe
          git tag -d "v-$CURRENT_VERSION" || true
          git tag "v-$CURRENT_VERSION"
          git push origin "v-$CURRENT_VERSION"
          gh release create "v-$CURRENT_VERSION" --generate-notes --draft --notes-start-tag $LAST_VERSION_TAG
          echo "$(gh release view v-$CURRENT_VERSION --json body --jq .body)" >> release-notes.md
          gh release delete "v-$CURRENT_VERSION" --cleanup-tag
      - name: Send Release Notes to Slack
        id: slack
        uses: adrey/slack-file-upload-action@1.0.5
        with:
          token: ${{ secrets.SLACK_API_TOKEN }}
          path: release-notes.md
          channel: app-builds
