name: Auto 🤖 - On PR merged to master

on:
  pull_request:
    types:
      - closed
    branches:
      - master

env:
  TZ: +05:30 # Required for the Ruby script to pick the right date in version

jobs:
  update-version:
    if: github.event.pull_request.merged == true
    secrets: inherit
    uses: ./.github/workflows/update-version.yml
    with:
      commit_message: "[Bot] Bump version after #${{ github.event.pull_request.number }}"
      branch: ${{ github.base_ref }}

  verify:
    if: github.event.pull_request.merged == true
    runs-on: self-hosted
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    - name: Setup Flutter
      run: |
        fvm install 3.19.6
        fvm use 3.19.6
        echo "Flutter Version: $(fvm flutter --version)"
    - name: Setup Fastlane for Android
      working-directory: android
      run: |
        bundle install
    - name: Android Verify
      working-directory: android
      env:
        SLACK_API_TOKEN: ${{ secrets.SLACK_API_TOKEN }}
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
      run: |
        bundle exec fastlane verify pr_number:${{github.event.pull_request.number}} actor:${{github.actor}}
    - name: Setup Fastlane for iOS
      working-directory: ios
      run: |
        bundle install
        bundle exec pod repo update
    - name: iOS Verify
      working-directory: ios
      env:
        SLACK_API_TOKEN: ${{ secrets.SLACK_API_TOKEN }}
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
      run: |
        bundle exec fastlane verify pr_number:${{github.event.pull_request.number}} actor:${{github.actor}}
