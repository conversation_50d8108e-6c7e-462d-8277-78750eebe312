name: Auto 🤖 - Hardcoded Strings Check
on:
  pull_request:
    branches: [master, release/*]

env:
  GITHUB_BASE_REF: ${{github.base_ref}}

jobs:
  pr-hardcoded-strings-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Fetch Base Branch
        run: git fetch origin $GITHUB_BASE_REF:$GITHUB_BASE_REF

      - name: Diff
        id: diff
        shell: bash
        run: |
          # "catch exit status 1" grep wrapper
          c1grep() { grep "$@" || test $? = 1; }

          changedDartFiles=$(git diff --name-only $GITHUB_BASE_REF | c1grep '\.dart$')
          if [ -z "$changedDartFiles" ]; then
            echo "::set-output name=DART_FILES_CHANGED::false"
          else
            echo "::set-output name=DART_FILES_CHANGED::true"
          fi
      - name: Setup Flutter SDK
        if: steps.diff.outputs.DART_FILES_CHANGED == 'true'
        uses: flutter-actions/setup-flutter@v3
        with:
          channel: stable
          version: 3.19.6
      - name: Setup SSH
        if: steps.diff.outputs.DART_FILES_CHANGED == 'true'
        uses: MrSquaare/ssh-setup-action@v3
        with:
          host: github.com
          private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Install dependencies
        if: steps.diff.outputs.DART_FILES_CHANGED == 'true'
        run: flutter pub get

      - name: Check for hardcoded strings
        if: steps.diff.outputs.DART_FILES_CHANGED == 'true'
        shell: bash
        run: |
          ruby scripts/hardcoded_strings_check_in_diff.rb
