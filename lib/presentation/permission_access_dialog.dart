import 'package:flutter/material.dart';

//Class for prompting to get contact access permission
class PermissionAccessDialog extends StatelessWidget {
  final Widget title, description, allowbutton, disAllowbutton;

  const PermissionAccessDialog(
      {super.key,
      required this.title,
      required this.description,
      required this.allowbutton,
      required this.disAllowbutton});

  //Method to create the view
  dialogContent(BuildContext context) {
    return Stack(
      children: <Widget>[
        Container(
          width: 300,
          padding: const EdgeInsets.only(
            top: Consts.padding,
            bottom: Consts.padding,
            left: Consts.padding,
            right: Consts.padding,
          ),
          margin: const EdgeInsets.only(top: Consts.avatarRadius),
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(Consts.padding),
            boxShadow: const [
              BoxShadow(
                color: Colors.black26,
                blurRadius: 10.0,
                offset: Offset(0.0, 10.0),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min, // To make the card compact
            children: <Widget>[
              title,
              const SizedBox(height: 16.0),
              description,
              const SizedBox(height: 24.0),
              allowbutton,
              disAllowbutton,
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(Consts.padding),
        ),
        elevation: 0.0,
        backgroundColor: Colors.transparent,
        child: Align(
          alignment: Alignment.topCenter,
          child: SingleChildScrollView(
              physics: const NeverScrollableScrollPhysics(),
              child: dialogContent(context)),
        ));
  }
}

class Consts {
  Consts._();

  static const double padding = 16.0;
  static const double avatarRadius = 120.0;
}
