/// Flutter icons CircleIcons
/// Copyright (C) 2019 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  CircleIcons
///      fonts:
///       - asset: fonts/CircleIcons.ttf
///
///
/// * Ty<PERSON><PERSON>, (c) <PERSON> 2012
///         Author:    <PERSON>
///         License:   SIL (http://scripts.sil.org/OFL)
///         Homepage:  http://typicons.com/
///
import 'package:flutter/widgets.dart';

class AppIcons {
  AppIcons._();

  static const _kFontFam = 'CircleIcons';
  static const _kOrgFontFam = 'OrgPageIcons';
  static const _kAdminFam = 'AdminOptionsIcon';
  static const _kPosterFam = 'PosterIcons';
  static const _kIconsFam = 'InfoIcons';
  static const _kContactsFam = 'FollowContactIcons';

  static const IconData circle_logo =
      const IconData(0xe900, fontFamily: _kFontFam);
  static const IconData home_outline =
      const IconData(0xe903, fontFamily: _kFontFam);
  static const IconData facebook =
      const IconData(0xe901, fontFamily: _kFontFam);
  static const IconData whatsapp =
      const IconData(0xe902, fontFamily: _kFontFam);
  static const IconData whatsappSolid =
      const IconData(0xe905, fontFamily: _kFontFam);
  static const IconData forward = const IconData(0xe906, fontFamily: _kFontFam);
  static const IconData poll_live =
      const IconData(0xe91d, fontFamily: _kFontFam);
  static const IconData poll_end =
      const IconData(0xe904, fontFamily: _kFontFam);
  static const IconData enter = const IconData(0xea13, fontFamily: _kFontFam);
  static const IconData exit = const IconData(0xea14, fontFamily: _kFontFam);
  static const IconData mention = const IconData(0xe935, fontFamily: _kFontFam);
  static const IconData bubbles = const IconData(0xe96f, fontFamily: _kFontFam);
  static const IconData earth = const IconData(0xe9ca, fontFamily: _kFontFam);
  static const IconData edit = const IconData(0xe907, fontFamily: _kFontFam);
  static const IconData commentBubble = const IconData(
    0xe96e,
    fontFamily: _kFontFam,
  );
  static const IconData groupTypeIcon =
      IconData(0xe800, fontFamily: _kOrgFontFam, fontPackage: null);
  static const IconData joinIcon =
      IconData(0xe802, fontFamily: _kOrgFontFam, fontPackage: null);
  static const IconData unfollowIcon =
      IconData(0xe805, fontFamily: _kOrgFontFam, fontPackage: null);

  static const IconData admin_options_icon =
      IconData(0xe800, fontFamily: _kAdminFam);

  static const IconData framePhotoUploadCameraIcon =
      IconData(0xe800, fontFamily: _kPosterFam, fontPackage: null);

  //InfoIcons

  static const IconData project_share =
      IconData(0xe800, fontFamily: _kIconsFam, fontPackage: null);
  static const IconData twitter_icon =
      IconData(0xe801, fontFamily: _kIconsFam, fontPackage: null);
  static const IconData whatsapp_icon =
      IconData(0xe802, fontFamily: _kIconsFam, fontPackage: null);
  static const IconData project_whatsapp =
      IconData(0xe803, fontFamily: _kIconsFam, fontPackage: null);
  static const IconData share_icon =
      IconData(0xe804, fontFamily: _kIconsFam, fontPackage: null);
  static const IconData view_all_positions =
      IconData(0xe805, fontFamily: _kIconsFam, fontPackage: null);
  static const IconData facebook_icon =
      IconData(0xe806, fontFamily: _kIconsFam, fontPackage: null);
  static const IconData back_icon =
      IconData(0xe807, fontFamily: _kIconsFam, fontPackage: null);

  //Follow Contact Icons
  static const IconData follow_icon =
      IconData(0xe800, fontFamily: _kContactsFam, fontPackage: null);
  static const IconData not_selected_icon =
      IconData(0xe801, fontFamily: _kContactsFam, fontPackage: null);
  static const IconData follow_all_contacts_icon =
      IconData(0xe802, fontFamily: _kContactsFam, fontPackage: null);
}
