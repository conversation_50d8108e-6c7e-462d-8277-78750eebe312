import 'package:flutter/material.dart';

class UpDownAnimatedSwitcher extends StatelessWidget {
  /// true -> up, ON; false -> down, OFF
  final bool value;
  final Widget child;

  const UpDownAnimatedSwitcher({
    super.key,
    required this.value,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
        duration: const Duration(milliseconds: 200),
        transitionBuilder: (child, animation) {
          final childKey = child.key as ValueKey;
          final offsetAnimation = Tween<Offset>(
            begin: childKey.value == 'up_down_animated_switcher_off'
                ? const Offset(0.0, 1.0)
                : const Offset(0.0, -1.0),
            end: Offset.zero,
          ).animate(animation);
          final opacityAnimation = Tween<double>(
            begin: 0.0,
            end: 1.0,
          ).animate(animation);
          return SlideTransition(
            position: offsetAnimation,
            child: FadeTransition(opacity: opacityAnimation, child: child),
          );
        },
        child: KeyedSubtree(
          key: Value<PERSON><PERSON>(value
              ? 'up_down_animated_switcher_on'
              : 'up_down_animated_switcher_off'),
          child: child,
        ));
  }
}
