import 'dart:math';

import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/features/intl/intl.dart';
import 'package:praja/models/circle_identity.dart';
import 'package:praja/services/hive_module.dart';
import 'package:praja/services/prefs/hive_prefs.dart';
import 'package:shimmer/shimmer.dart';

import 'circle_display_picture.dart';
import 'circle_official_badge.dart';

/// Introduces the circle to the user if
/// the circle is verified and not already introduced.
/// Otherwise, shows the child.
/// Wrap the page's scaffold with this widget where you want
/// to introduce the circle / channel
class CircleIntro extends StatelessWidget {
  final CircleIdentity circleIdentity;
  final VoidCallback? onComplete;
  final bool isChannel;
  final Widget child;
  const CircleIntro(this.circleIdentity,
      {super.key,
      required this.child,
      this.onComplete,
      this.isChannel = false});

  @override
  Widget build(BuildContext context) {
    final viewModel = context.circleIntroViewModel(circleIdentity.id);

    return Stack(children: [
      child,
      LiveDataBuilder<bool>(
          liveData: viewModel.isCircleIntroComplete,
          builder: (_, isIntroComplete) {
            return WillPopScope(
                onWillPop: () async {
                  if (circleIdentity.verified && !isIntroComplete) {
                    return false;
                  }
                  return true;
                },
                child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 500),
                    switchInCurve: Curves.easeOutQuad,
                    switchOutCurve: Curves.easeInQuad,
                    transitionBuilder: (child, animation) {
                      return ScaleTransition(
                          scale: animation.drive(Tween(begin: 0.90, end: 1.0)),
                          child:
                              FadeTransition(opacity: animation, child: child));
                    },
                    child: (circleIdentity.verified && !isIntroComplete)
                        ? CircleIntroUI(circleIdentity,
                            key: Key('circle_intro_ui_${circleIdentity.id}'),
                            onComplete: viewModel.onCircleIntroComplete,
                            isChannel: isChannel)
                        : const SizedBox(key: Key('no-intro'))));
          }),
    ]);
  }
}

@injectable
class CircleIntroViewModel extends ViewModel {
  final HivePrefs appPrefs;

  final MutableLiveData<bool> _isCircleIntroComplete =
      MutableLiveData<bool>(true);
  LiveData<bool> get isCircleIntroComplete => _isCircleIntroComplete;
  late int circleId;

  CircleIntroViewModel(@Named(appPrefsConstant) this.appPrefs);

  bool _isInitialized = false;
  void _init(int circleId) async {
    if (_isInitialized) return;
    _isInitialized = true;
    this.circleId = circleId;

    final isCircleIntroComplete = await appPrefs
        .getBool("circle_intro_complete_$circleId", defaultValue: false);
    _isCircleIntroComplete.value = isCircleIntroComplete;
  }

  void onCircleIntroComplete() {
    appPrefs.putBool("circle_intro_complete_$circleId", true);
    _isCircleIntroComplete.value = true;
  }
}

extension on BuildContext {
  CircleIntroViewModel circleIntroViewModel(int circleId) =>
      getViewModel<CircleIntroViewModel>(key: "$circleId").._init(circleId);
}

class CircleIntroUI extends StatefulWidget {
  final CircleIdentity circleIdentity;
  final VoidCallback? onComplete;
  final bool isChannel;
  const CircleIntroUI(this.circleIdentity,
      {super.key, this.onComplete, this.isChannel = false});

  @override
  State<CircleIntroUI> createState() => _CircleIntroUIState();
}

const int _initialDelayMillis = 900;
const int _badgeEntryDurationMillis = 350;
const int _shakeDurationMillis = 300;
const int _membersCountDelayDurationMillis = 850;
const int _memberCountEntryDurationMillis = 350;
const int _memberCountShimmerDurationMillis = 3200;

class _CircleIntroUIState extends State<CircleIntroUI>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  late Animation<double> _badgeOpacityAnimation;
  late Animation<double> _badgeScaleAnimation;
  late Animation<double> _badgeRotationAnimation;
  late Animation<double> _badgeTranslationAnimation;

  late Animation<double> _shakeAnimation;

  late Animation<double> _memberCountOpacityAnimation;
  late Animation<double> _memberCountTranslationAnimation;

  bool _enableMembersCountShimmer = false;

  int get _totalDurationMillis =>
      _initialDelayMillis +
      _badgeEntryDurationMillis +
      _shakeDurationMillis +
      _membersCountDelayDurationMillis +
      _memberCountEntryDurationMillis +
      _memberCountShimmerDurationMillis;

  late double memberCountEnd;
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
        duration: Duration(milliseconds: _totalDurationMillis), vsync: this);
    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        widget.onComplete?.call();
      }
    });

    final badgeStart = _initialDelayMillis / _totalDurationMillis;
    final badgeEnd =
        (badgeStart + _badgeEntryDurationMillis / _totalDurationMillis);
    _badgeOpacityAnimation =
        Tween<double>(begin: 0, end: 1).animate(CurvedAnimation(
      parent: _controller,
      curve: Interval(badgeStart, badgeEnd, curve: Curves.easeInCirc),
    ));

    _badgeScaleAnimation =
        Tween<double>(begin: 1.5, end: 1).animate(CurvedAnimation(
      parent: _controller,
      curve: Interval(badgeStart, badgeEnd, curve: Curves.easeInCirc),
    ));

    _badgeRotationAnimation =
        Tween<double>(begin: -pi / 72, end: 0).animate(CurvedAnimation(
      parent: _controller,
      curve: Interval(badgeStart, badgeEnd, curve: Curves.easeInCirc),
    ));

    _badgeTranslationAnimation =
        Tween<double>(begin: -16, end: 0).animate(CurvedAnimation(
      parent: _controller,
      curve: Interval(badgeStart, badgeEnd, curve: Curves.easeInCirc),
    ));

    final shakeStart = badgeEnd;
    final shakeEnd = shakeStart + _shakeDurationMillis / _totalDurationMillis;
    _shakeAnimation = Tween<double>(begin: 0, end: 1).animate(CurvedAnimation(
      parent: _controller,
      curve: Interval(shakeStart, shakeEnd, curve: Curves.linear),
    ));

    final memberCountStart =
        (shakeEnd + _membersCountDelayDurationMillis / _totalDurationMillis);
    memberCountEnd = memberCountStart +
        _memberCountEntryDurationMillis / _totalDurationMillis;
    _memberCountOpacityAnimation =
        Tween<double>(begin: 0, end: 1).animate(CurvedAnimation(
      parent: _controller,
      curve:
          Interval(memberCountStart, memberCountEnd, curve: Curves.easeOutQuad),
    ));

    _memberCountTranslationAnimation =
        Tween<double>(begin: 4, end: 0).animate(CurvedAnimation(
      parent: _controller,
      curve:
          Interval(memberCountStart, memberCountEnd, curve: Curves.easeOutQuad),
    ));
  }

  void onReady() async {
    if (!mounted || _enableMembersCountShimmer) return;

    _controller.forward();
    await Future.delayed(Duration(
        milliseconds: (_totalDurationMillis * memberCountEnd).toInt()));
    if (!mounted) return;
    setState(() {
      _enableMembersCountShimmer = true;
    });
  }

  @override
  dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      onReady();
    });
    return Scaffold(
        body: Container(
      color: Colors.white,
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        children: [
          AnimatedBuilder(
            animation: _shakeAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(
                    (1 - _shakeAnimation.value) *
                        4 *
                        cos(_shakeAnimation.value * 2 * pi * 5),
                    0),
                child: child,
              );
            },
            child: Center(
                child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                CircleDisplayPicture.fromCircleIdentity(widget.circleIdentity,
                    size: MediaQuery.of(context).size.width / 2.5,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 16,
                      )
                    ]),
                const SizedBox(height: 24),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: Text(
                    widget.circleIdentity.name,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.w700,
                        color: Colors.black87),
                  ),
                ),
                const SizedBox(height: 8),
                AnimatedBuilder(
                  animation: _badgeOpacityAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _badgeOpacityAnimation.value,
                      child: Transform.scale(
                          scale: _badgeScaleAnimation.value,
                          child: Transform(
                            transform:
                                Matrix4.skewY(_badgeRotationAnimation.value),
                            alignment: Alignment.centerLeft,
                            child: Transform.translate(
                                offset:
                                    Offset(_badgeTranslationAnimation.value, 0),
                                child: child),
                          )),
                    );
                  },
                  child: widget.isChannel
                      ? CircleOfficialBadge.channel(height: 40)
                      : CircleOfficialBadge.circle(height: 40),
                ),
                const SizedBox(height: 36),
                AnimatedBuilder(
                  animation: _memberCountOpacityAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _memberCountOpacityAnimation.value,
                      child: Transform.translate(
                        offset:
                            Offset(0, _memberCountTranslationAnimation.value),
                        child: child,
                      ),
                    );
                  },
                  child: Shimmer.fromColors(
                    baseColor: Colors.grey.shade800,
                    highlightColor: Colors.grey.shade400,
                    enabled: _enableMembersCountShimmer,
                    period: const Duration(
                        milliseconds: _memberCountShimmerDurationMillis),
                    child: Column(children: [
                      Text(
                        "${(widget.circleIdentity.membersCount).toCommaSeperatedFormat()} సభ్యులు",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.normal,
                          color: Colors.grey.shade800,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (widget.circleIdentity.biggest)
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: Text(
                            widget.isChannel
                                ? "ఇంటర్నెట్లోనే అతిపెద్ద ${widget.circleIdentity.name} ఛానల్"
                                : "ఇంటర్నెట్లోనే అతిపెద్ద ${widget.circleIdentity.name} సర్కిల్",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey.shade800,
                            ),
                          ),
                        ),
                    ]),
                  ),
                ),
                const SizedBox(height: 144),
              ],
            )),
          ),
        ],
      ),
    ));
  }
}
