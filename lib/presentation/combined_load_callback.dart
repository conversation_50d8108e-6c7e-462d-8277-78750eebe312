import 'dart:ui';

/// Invokes onLoaded when all entities are loaded.
class CombinedLoadCallback {
  final Set<String> entityNames;
  final VoidCallback? onLoaded;

  final Set<String> _loadedEntityNames = {};

  CombinedLoadCallback({
    required this.entityNames,
    this.onLoaded,
  });

  void onEntityLoaded(String entityName) {
    _loadedEntityNames.add(entityName);
    if (_loadedEntityNames.length == entityNames.length) {
      onLoaded?.call();
    }
  }
}
