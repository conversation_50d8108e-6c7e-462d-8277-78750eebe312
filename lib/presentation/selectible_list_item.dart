import 'package:flutter/material.dart';
import 'package:praja/presentation/praja_icons.dart';
import 'package:praja/styles.dart';

class SelectibleListItem extends StatelessWidget {
  final Widget child;
  final bool selected;
  final EdgeInsets padding;

  final VoidCallback onTap;

  const SelectibleListItem({
    super.key,
    required this.child,
    required this.selected,
    required this.onTap,
    this.padding = EdgeInsets.zero,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: padding,
        color: selected ? Styles.circleIndigo.shade50 : Colors.transparent,
        child: Row(children: [
          Expanded(child: child),
          AnimatedOpacity(
              duration: const Duration(milliseconds: 200),
              opacity: selected ? 1 : 0,
              child: AnimatedScale(
                  duration: const Duration(milliseconds: 200),
                  scale: selected ? 1 : 0.5,
                  curve: Curves.easeInOut,
                  child: AnimatedRotation(
                      turns: selected ? 0 : 0.25,
                      duration: const Duration(milliseconds: 200),
                      curve: Curves.easeInOut,
                      child: Container(
                          width: 24,
                          height: 24,
                          margin: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Padding(
                              padding: EdgeInsets.all(4),
                              child: Icon(PrajaIcons.tick,
                                  color: Colors.white, size: 16))))))
        ]),
      ),
    );
  }
}
