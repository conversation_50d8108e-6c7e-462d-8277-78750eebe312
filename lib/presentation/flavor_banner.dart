import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:praja/enums/app_environment.dart';

// ignore: must_be_immutable
class FlavorBanner extends StatelessWidget {
  final Widget child;
  final String? bannerName;
  final Color? color;
  BannerConfig? bannerConfig;

  FlavorBanner({
    super.key,
    required this.child,
    this.bannerName,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    if (AppEnvironment.current() == AppEnvironment.production &&
        !kProfileMode) {
      return child;
    }

    bannerConfig ??= _getDefaultBanner();

    return Stack(
      children: <Widget>[child, _buildBanner(context)],
    );
  }

  BannerConfig _getDefaultBanner() {
    final env = AppEnvironment.current();
    return BannerConfig(
      bannerName: bannerName ??
          (env == AppEnvironment.production
              ? 'PROFILE'
              : env.toString().split('.').last.toUpperCase()),
      bannerColor: color ?? Colors.green,
    );
  }

  Widget _buildBanner(BuildContext context) {
    return bannerConfig == null
        ? const SizedBox()
        : SizedBox(
            width: 50,
            height: 50,
            child: CustomPaint(
              painter: BannerPainter(
                message: bannerConfig!.bannerName,
                textDirection: Directionality.of(context),
                layoutDirection: Directionality.of(context),
                location: BannerLocation.topStart,
                color: bannerConfig!.bannerColor,
              ),
            ),
          );
  }
}

class BannerConfig {
  final String bannerName;
  final Color bannerColor;

  BannerConfig({
    required this.bannerName,
    required this.bannerColor,
  });
}
