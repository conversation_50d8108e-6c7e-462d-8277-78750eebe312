import 'package:flutter/material.dart';
import 'package:praja/styles.dart';

class OutlineHighlight extends StatefulWidget {
  final Widget child;
  final bool enabled;

  final double outlineWidth;
  final int count;
  final Color color;
  final VoidCallback? onComplete;

  const OutlineHighlight(
      {super.key,
      required this.child,
      this.outlineWidth = 3.0,
      this.count = 2,
      this.color = Styles.circleIndigo,
      this.enabled = false,
      this.onComplete});

  @override
  State<StatefulWidget> createState() {
    return OutlineHighlightState();
  }
}

class OutlineHighlightState extends State<OutlineHighlight>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  initState() {
    super.initState();
    _controller = AnimationController(
        duration: const Duration(milliseconds: 350), vsync: this);
    _animation = CurvedAnimation(parent: _controller, curve: Curves.easeInOut);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (widget.enabled) {
      _animate();
    }
  }

  @override
  didUpdateWidget(OutlineHighlight oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.enabled != widget.enabled) {
      if (widget.enabled) {
        _animate();
      } else {
        _controller.stop();
        _controller.value = 0;
      }
    }
  }

  Future<void> _animate() async {
    _controller.reset();
    for (int i = 0; i < widget.count; i++) {
      await _controller.forward();
      await _controller.reverse();
    }
    widget.onComplete?.call();
  }

  @override
  dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      if (widget.enabled)
        Positioned.fill(
            child: AnimatedBuilder(
                animation: _animation,
                builder: (context, child) {
                  return Opacity(
                    opacity: (_animation.value) * 0.6,
                    child: child,
                  );
                },
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                        color: widget.color, width: widget.outlineWidth),
                  ),
                ))),
      widget.child,
    ]);
  }
}
