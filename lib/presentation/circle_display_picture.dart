import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:praja/extensions/locale_extensions.dart';
import 'package:praja/models/circle.dart';
import 'package:praja/models/circle_identity.dart';
import 'package:praja/services/app_cache_manager.dart';
import 'package:praja/styles.dart';
import 'package:shimmer/shimmer.dart';

const fallbackCharacter = 'C'; // C for Circle

class CircleDisplayPicture extends StatelessWidget {
  final String name;
  final String? imageUrl;
  final double size;
  final Color color;
  final VoidCallback? onLoaded;
  final List<BoxShadow>? boxShadow;

  const CircleDisplayPicture({
    super.key,
    required this.name,
    this.imageUrl,
    this.size = 48,
    this.color = Styles.circleIndigo,
    this.onLoaded,
    this.boxShadow,
  });

  factory CircleDisplayPicture.fromCircleIdentity(CircleIdentity circleIdentity,
      {double size = 48, VoidCallback? onLoaded, List<BoxShadow>? boxShadow}) {
    return CircleDisplayPicture(
      name: circleIdentity.name,
      imageUrl: circleIdentity.profilePhoto?.url,
      size: size,
      onLoaded: onLoaded,
      boxShadow: boxShadow,
    );
  }

  factory CircleDisplayPicture.fromCircle(Circle circle,
      {double size = 48,
      VoidCallback? onLoaded,
      Color color = const Color(0xFFF1F2FA)}) {
    return CircleDisplayPicture(
      name: circle.name,
      imageUrl: circle.profilePhoto?.url,
      size: size,
      color: color,
      onLoaded: onLoaded,
    );
  }

  Widget _noImageFallback(BuildContext context) {
    String firstCharacter = name.firstCharacter(fallbackCharacter);
    return Container(
        width: size,
        height: size,
        color: color,
        child: Center(
          child: Padding(
            padding: EdgeInsets.only(
                top: firstCharacter.localeByUnicodeUsage.languageCode == 'te'
                    ? size * 0.05
                    : 0),
            child: Text(
              name.firstCharacter(fallbackCharacter),
              textAlign: TextAlign.center,
              style: TextStyle(
                color: color.computeLuminance() > 0.179
                    ? Colors.grey.shade900
                    : Colors.white,
                fontSize: size > 100 ? size * 0.4 : (size / 2) * 0.75,
              ),
            ),
          ),
        ));
  }

  Widget _imageFallback(BuildContext context) {
    return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(size / 4),
        ));
  }

  Widget _image(BuildContext context, String imageUrl) {
    return CachedNetworkImage(
        imageUrl: imageUrl,
        cacheManager: AppCacheManager.instance,
        imageBuilder: (context, imageProvider) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            onLoaded?.call();
          });
          return Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: imageProvider,
                fit: BoxFit.cover,
              ),
            ),
          );
        },
        fadeInDuration: Duration.zero,
        fadeOutDuration: Duration.zero,
        placeholder: (context, url) => _imageFallback(context),
        errorWidget: (context, url, error) => _imageFallback(context));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        width: size,
        height: size,
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(size / 4),
          boxShadow: boxShadow,
        ),
        child: imageUrl != null
            ? _image(context, imageUrl!)
            : _noImageFallback(context));
  }

  static Widget shimmer({double size = 48}) {
    return Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            borderRadius: BorderRadius.circular(size / 4),
          ),
        ));
  }
}

extension _FirstCharacterExtension on String {
  String firstCharacter(String fallback) {
    if (characters.isEmpty) {
      return fallback;
    } else {
      return characters.first;
    }
  }
}
