import 'dart:async';

import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/core/ui/global_view_model_scope.dart';
import 'package:praja/features/direct_messaging/service/messaging_service.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/notifications/service/notification.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/presentation/praja_icons.dart';
import 'package:praja/services/app_initializer.dart';
import 'package:praja/services/app_unread_badge.dart';
import 'package:praja/utils/logger.dart';
import 'package:rxdart/rxdart.dart';

@injectable
class NavViewModel extends ViewModel {
  NavViewModel(this._notificationService, this._messageService,
      this._appInitializer, this._appUnreadBadge) {
    _unreadNotificationsCountSubscription = _notificationService
        .unreadCountStream
        .debounceTime(const Duration(milliseconds: 500))
        .logNonFatalsIfAppError(
            "Error while listening to notification unread count in nav_bar")
        .listen((count) {
      _unreadNotificationsCount.value = count;
    });
    _appInitListener = (data) {
      if (data['dm_enabled'] ?? true) {
        useChatNavItems();
      } else {
        useDefaultNavItems();
      }
      switchTo(currentNavItemName);
    };

    _appInitializer.addListener(_appInitListener);

    _selectedIndex.observe(_onSelectedIndexChanged);
    _unreadChatsState.observe(_onUnreadChatsStateChanged);
    _unreadNotificationsCount.observe(_onUnreadNotificationCountChanged);
  }

  final AppUnreadBadge _appUnreadBadge;
  final NotificationService _notificationService;
  final MessagingService _messageService;
  final AppInitializer _appInitializer;
  late AppInitListener _appInitListener;
  NavItemName currentNavItemName = NavItemName.trends;

  StreamSubscription<int>? _unreadNotificationsCountSubscription;
  StreamSubscription<int>? _unreadPrimaryChatsCountSubscription;
  StreamSubscription<int>? _unreadSecondaryChatsCountSubscription;

  final MutableLiveData<int> _selectedIndex = MutableLiveData<int>(0);
  LiveData<int> get selectedIndex => _selectedIndex;

  final MutableLiveData<List<NavItem>> _navItems =
      MutableLiveData<List<NavItem>>(const []);
  LiveData<List<NavItem>> get navItems => _navItems;

  final MutableEventQueue<NavBarEvent> _eventQueue = MutableEventQueue();
  EventQueue<NavBarEvent> get eventQueue => _eventQueue;

  final MutableLiveData<int> _unreadNotificationsCount =
      MutableLiveData<int>(0);
  LiveData<int> get unreadNotificationsCount => _unreadNotificationsCount;

  final MutableLiveData<UnreadChatsState> _unreadChatsState =
      MutableLiveData<UnreadChatsState>(UnreadChatsState.initial());
  LiveData<UnreadChatsState> get unreadChatsState => _unreadChatsState;

  final MutableLiveData<bool> _showTrendsUnreadDot =
      MutableLiveData<bool>(false);
  LiveData<bool> get showTrendsUnreadDot => _showTrendsUnreadDot;

  NavItemName selectedNavItemName() {
    return _navItems.value[_selectedIndex.value].name;
  }

  void updateTrendsUnreadDot(bool show) {
    _showTrendsUnreadDot.value = show;
  }

  void onLogout() {
    _selectedIndex.value = 0;
  }

  void onTabSelected(int index) {
    _selectedIndex.value = index;
  }

  void useDefaultNavItems() {
    final oldNavItems = _navItems.value;
    final oldSelectedIndex = _selectedIndex.value;
    _navItems.value = defaultNavItems;
    _unreadPrimaryChatsCountSubscription?.cancel();
    _unreadSecondaryChatsCountSubscription?.cancel();

    if (oldNavItems.isEmpty) {
      return;
    }

    final newIndex = _navItems.value.indexOf(oldNavItems[oldSelectedIndex]);
    if (newIndex >= 0) {
      _selectedIndex.value = newIndex;
    }
  }

  void useChatNavItems() {
    final oldNavItems = _navItems.value;
    final oldSelectedIndex = _selectedIndex.value;
    _navItems.value = chatNavItems;

    _messageService.tryConnect();
    _unreadPrimaryChatsCountSubscription?.cancel();
    _unreadPrimaryChatsCountSubscription = _messageService
        .getUnreadConversationsCount(isPrimary: true)
        .debounceTime(const Duration(milliseconds: 500))
        .logNonFatalsIfAppError(
            "Error while listening to primary unread count in nav_bar")
        .listen((count) {
      logInfo("Primary unread count: $count");
      _unreadChatsState.value =
          _unreadChatsState.value.copyWith(primaryUnreadCount: count);
    });
    _unreadSecondaryChatsCountSubscription?.cancel();
    _unreadSecondaryChatsCountSubscription = _messageService
        .getUnreadConversationsCount(isPrimary: false)
        .debounceTime(const Duration(milliseconds: 500))
        .logNonFatalsIfAppError(
            "Error while listening to secondary unread count in nav_bar")
        .listen((count) {
      logInfo("Secondary unread count: $count");
      _unreadChatsState.value = _unreadChatsState.value
          .copyWith(secondaryUnreadChatsPresent: count > 0);
    });

    if (oldNavItems.isEmpty) {
      return;
    }

    final newIndex = _navItems.value.indexOf(oldNavItems[oldSelectedIndex]);
    if (newIndex >= 0) {
      _selectedIndex.value = newIndex;
    }
  }

  Map<String, dynamic> postersFeedDeeplinkParams = const {};

  void switchTo(NavItemName name,
      {Map<String, dynamic> navTabParams = const {}}) {
    final index = _navItems.value.indexWhere((element) => element.name == name);
    if (index >= 0) {
      if (name == NavItemName.posters) {
        _eventQueue.push(PostersFeedDeeplinkEvent(navTabParams));
        postersFeedDeeplinkParams = navTabParams;
      }
      _selectedIndex.value = index;
    } else {
      currentNavItemName = name;
    }
  }

  void onTappedOn(int index) {
    if (index == _selectedIndex.value) {
      _eventQueue.push(
          NavBarEvent.selectedTabTapped(index, _navItems.value[index].name));
    } else {
      _selectedIndex.value = index;
      _sendTabClickAnalytics(_navItems.value[index].name);
    }
  }

  void _sendTabClickAnalytics(NavItemName name) {
    switch (name) {
      case NavItemName.trends:
        AppAnalytics.logEvent(name: 'trending_menu_click');
        break;
      case NavItemName.posters:
        AppAnalytics.logEvent(name: 'posters_menu_click');
        break;
      case NavItemName.circles:
        AppAnalytics.logEvent(name: 'circle_menu_click');
        break;
      case NavItemName.chat:
        AppAnalytics.logEvent(name: 'chat_menu_click');
        break;
      case NavItemName.notifications:
        AppAnalytics.logEvent(name: 'notifications_menu_click');
        break;
    }
  }

  void _onSelectedIndexChanged(int index) {
    if (index < 0 || index >= _navItems.value.length) {
      return;
    }
    final prevNavItemName = currentNavItemName;
    currentNavItemName = _navItems.value[index].name;

    if (prevNavItemName != currentNavItemName) {
      _eventQueue.push(
          TabChangedEvent(prev: prevNavItemName, current: currentNavItemName));
    }
  }

  void _onUnreadNotificationCountChanged(int _) {
    _appUnreadBadge.setCount(_unreadNotificationsCount.value +
        _unreadChatsState.value.primaryUnreadCount);
  }

  void _onUnreadChatsStateChanged(UnreadChatsState state) {
    _appUnreadBadge.setCount(_unreadNotificationsCount.value +
        _unreadChatsState.value.primaryUnreadCount);
  }

  @override
  void onDispose() {
    _unreadNotificationsCountSubscription?.cancel();
    _unreadPrimaryChatsCountSubscription?.cancel();
    _unreadSecondaryChatsCountSubscription?.cancel();
    _appInitializer.removeListener(_appInitListener);
    _unreadChatsState.removeObserver(_onUnreadChatsStateChanged);
    _unreadNotificationsCount.removeObserver(_onUnreadNotificationCountChanged);
    _selectedIndex.removeObserver(_onSelectedIndexChanged);
    super.onDispose();
  }
}

extension NavBarX on BuildContext {
  NavViewModel get globalNavViewModel =>
      globalViewModelProvider.get<NavViewModel>();
}

class NavItem {
  final NavItemName name;
  final IconData icon;
  final StringKey localizedNameKey;

  const NavItem(this.name, this.icon, this.localizedNameKey);

  @override
  String toString() {
    return 'NavItem{name: $name, icon: $icon, localizedNameKey: $localizedNameKey}';
  }
}

const trendsNavItem =
    NavItem(NavItemName.trends, PrajaIcons.trend_1, StringKey.navTrendsLabel);
const groupsNavItem =
    NavItem(NavItemName.circles, PrajaIcons.groups, StringKey.navCirclesLabel);
const notificationsNavItem = NavItem(NavItemName.notifications,
    PrajaIcons.notification, StringKey.navNotificationsLabel);
const chatNavItem =
    NavItem(NavItemName.chat, PrajaIcons.message, StringKey.navChatsLabel);
const postersNavItem =
    NavItem(NavItemName.posters, PrajaIcons.posters, StringKey.navPostersLabel);

const defaultNavItems = [
  trendsNavItem,
  postersNavItem,
  groupsNavItem,
  notificationsNavItem,
];

const chatNavItems = [
  trendsNavItem,
  postersNavItem,
  groupsNavItem,
  notificationsNavItem,
  chatNavItem,
];

enum NavItemName {
  trends,
  posters,
  circles,
  notifications,
  chat,
}

abstract class NavBarEvent {
  const NavBarEvent();
  factory NavBarEvent.selectedTabTapped(int index, NavItemName name) =>
      SelectedTabTappedEvent(index, name);
}

class SelectedTabTappedEvent extends NavBarEvent {
  final int index;
  final NavItemName name;

  const SelectedTabTappedEvent(this.index, this.name);
}

class TabChangedEvent extends NavBarEvent {
  final NavItemName prev;
  final NavItemName current;

  const TabChangedEvent({required this.prev, required this.current});
}

class PostersFeedDeeplinkEvent extends NavBarEvent {
  final Map<String, dynamic> params;

  const PostersFeedDeeplinkEvent(this.params);
}

class UnreadChatsState {
  final int primaryUnreadCount;
  final bool secondaryUnreadChatsPresent;

  const UnreadChatsState(
      {required this.primaryUnreadCount,
      required this.secondaryUnreadChatsPresent});

  factory UnreadChatsState.initial() {
    return const UnreadChatsState(
        primaryUnreadCount: 0, secondaryUnreadChatsPresent: false);
  }

  UnreadChatsState copyWith(
      {int? primaryUnreadCount, bool? secondaryUnreadChatsPresent}) {
    return UnreadChatsState(
        primaryUnreadCount: primaryUnreadCount ?? this.primaryUnreadCount,
        secondaryUnreadChatsPresent:
            secondaryUnreadChatsPresent ?? this.secondaryUnreadChatsPresent);
  }

  @override
  String toString() {
    return 'UnreadChatsState{primaryUnreadCount: $primaryUnreadCount, secondaryUnreadChatsPresent: $secondaryUnreadChatsPresent}';
  }
}
