import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:praja/services/app_cache_manager.dart';

import '../models/v2/photo.dart';

/// Generic avatar widget that can be used for both users
/// and circles and may be other entities in the future.
class Avatar extends StatelessWidget {
  final String name;
  final Color color;
  final double size;
  final String fallbackCharacter;
  final Photo? photo;
  final VoidCallback? onLoaded;

  const Avatar({
    super.key,
    required this.name,
    required this.color,
    this.fallbackCharacter = 'U', // for user
    this.size = 48,
    this.photo,
    this.onLoaded,
  });

  @override
  Widget build(BuildContext context) {
    if (photo != null) {
      return PhotoAvatar(
        photo: photo!,
        size: size,
        onLoaded: onLoaded,
      );
    } else {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        onLoaded?.call();
      });
      return NameAvatar(
        name: name,
        color: color,
        size: size,
        fallbackCharacter: fallback<PERSON>haracter,
      );
    }
  }
}

/// Used as a fallback when a photo is not available.
class NameAvatar extends StatelessWidget {
  final String name;
  final Color color;
  final double size;
  final String fallbackCharacter;

  const NameAvatar({
    super.key,
    required this.name,
    required this.color,
    this.fallbackCharacter = 'U', // for user
    this.size = 48,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(size / 2),
        ),
        child: Center(
          child: Text(
            name.firstCharacter(fallbackCharacter),
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: (size / 2) * 0.75,
            ),
          ),
        ));
  }
}

extension _FirstCharacterExtension on String {
  String firstCharacter(String fallback) {
    if (characters.isEmpty) {
      return fallback;
    } else {
      return characters.first;
    }
  }
}

class PhotoAvatar extends StatelessWidget {
  final Photo photo;
  final double size;
  final VoidCallback? onLoaded;

  // default cache with high stale duration
  static final CacheManager _cacheManager = AppCacheManager.instance;

  const PhotoAvatar({
    super.key,
    required this.photo,
    this.size = 48,
    this.onLoaded,
  });

  Widget _fallback(BuildContext context) {
    return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColorDark,
          borderRadius: BorderRadius.circular(size / 2),
        ));
  }

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
        imageUrl: photo.url,
        cacheManager: _cacheManager,
        imageBuilder: (context, imageProvider) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            onLoaded?.call();
          });
          return Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(size / 2),
              image: DecorationImage(
                image: imageProvider,
                fit: BoxFit.cover,
              ),
            ),
          );
        },
        fadeInDuration: Duration.zero,
        fadeOutDuration: Duration.zero,
        placeholder: (context, url) => _fallback(context),
        errorWidget: (context, url, error) => _fallback(context));
  }
}
