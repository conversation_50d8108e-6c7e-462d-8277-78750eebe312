import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/features/circle/service/circle_service.dart';
import 'package:praja/models/circle_identity.dart';

@injectable
class CircleIdentityViewModel extends ViewModel {
  final _circleIdentity = MutableLiveData<CircleIdentity?>(null);

  LiveData<CircleIdentity?> get circleIdentity => _circleIdentity;

  final CircleServiceV2 _circleService;

  CircleIdentityViewModel(this._circleService);

  bool _isInitialised = false;
  Future<void> init(int circleId) async {
    if (_isInitialised) return;

    _isInitialised = true;
    final circle = await _circleService.getIdentity(circleId);
    _circleIdentity.value = circle;
  }
}

extension CircleIdentityViewModelX on BuildContext {
  CircleIdentityViewModel circleIdentityViewModel(int circleId) =>
      getViewModel<CircleIdentityViewModel>(key: circleId.toString())
        ..init(circleId);
}
