import 'package:flutter/material.dart';

import 'up_down_animated_switcher.dart';

class UpDownIconToggle extends StatelessWidget {
  /// true -> up, ON; false -> down, OFF
  final bool value;
  final VoidCallback? onPressed;
  final Widget onIcon;
  final Widget offIcon;
  final EdgeInsets padding;
  final FocusNode? focusNode;
  final double? splashRadius;
  final bool autofocus;

  const UpDownIconToggle({
    super.key,
    required this.value,
    required this.onPressed,
    required this.onIcon,
    required this.offIcon,
    this.splashRadius,
    this.padding = const EdgeInsets.all(8.0),
    this.focusNode,
    this.autofocus = false,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
        button: true,
        enabled: onPressed != null,
        child: InkResponse(
            onTap: onPressed,
            focusNode: focusNode,
            autofocus: autofocus,
            radius: splashRadius,
            child: UpDownAnimatedSwitcher(
              value: value,
              child: Padding(padding: padding, child: value ? onIcon : offIcon),
            )));
  }
}
