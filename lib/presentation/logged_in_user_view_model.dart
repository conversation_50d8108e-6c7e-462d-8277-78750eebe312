import 'dart:async';

import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/features/user/models/app_user.dart';
import 'package:praja/services/user_store.dart';

@injectable
class LoggedInUserViewModel extends ViewModel {
  LoggedInUserViewModel(this._userStore) {
    _userSubscription = _userStore.userStream.listen((user) {
      _user.value = user;
    });
    _userStore.getAppUser().then((user) {
      _user.value = user;
    });
  }

  final UserStore _userStore;
  StreamSubscription<AppUser?>? _userSubscription;

  final MutableLiveData<AppUser?> _user = MutableLiveData<AppUser?>(null);
  LiveData<AppUser?> get user => _user;

  @override
  void onDispose() {
    _userSubscription?.cancel();
    super.onDispose();
  }
}
