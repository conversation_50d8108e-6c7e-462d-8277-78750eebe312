import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:photo_view/photo_view.dart';
import 'package:praja/services/app_cache_manager.dart';

class ImagesViewer extends StatefulWidget {
  const ImagesViewer({super.key, required this.images, this.initialIndex = 0});
  final List<ImageItem> images;
  final int initialIndex;

  static Future<void> show(BuildContext context,
      {required List<ImageItem> images, int initialIndex = 0}) {
    return Navigator.of(context).push(MaterialPageRoute(
        builder: (ctx) => ImagesViewer(
              images: images,
              initialIndex: initialIndex,
            )));
  }

  @override
  State<ImagesViewer> createState() {
    return _ImagesViewerState();
  }
}

class _ImagesViewerState extends State<ImagesViewer> {
  int currentIndex = 0;
  bool zoomedIn = false;
  late PageController pageController;

  @override
  initState() {
    super.initState();
    currentIndex = widget.initialIndex;
    pageController = PageController(initialPage: currentIndex);
  }

  ImageProvider _imageProvider(int index) {
    final imageItem = widget.images[index];
    if (imageItem is RemoteImageItem) {
      return CachedNetworkImageProvider(
        imageItem.url,
        cacheManager: AppCacheManager.instance,
      );
    } else if (imageItem is LocalImageItem) {
      return FileImage(File(imageItem.path));
    } else {
      throw ArgumentError('Unknown image item type');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.black,
        body: Stack(children: [
          PageView.builder(
              physics: zoomedIn
                  ? const NeverScrollableScrollPhysics()
                  : const BouncingScrollPhysics(),
              itemCount: widget.images.length,
              controller: pageController,
              onPageChanged: (index) {
                setState(() {
                  currentIndex = index;
                });
              },
              itemBuilder: (ctx, index) {
                return PhotoView(
                  backgroundDecoration:
                      const BoxDecoration(color: Colors.transparent),
                  minScale: PhotoViewComputedScale.contained,
                  scaleStateCycle: (state) {
                    if (state == PhotoViewScaleState.initial) {
                      return PhotoViewScaleState.covering;
                    } else {
                      return PhotoViewScaleState.initial;
                    }
                  },
                  maxScale: PhotoViewComputedScale.contained * 2,
                  imageProvider: _imageProvider(index),
                  scaleStateChangedCallback: (state) {
                    setState(() {
                      zoomedIn = state != PhotoViewScaleState.initial;
                    });
                  },
                );
              }),
          Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: AppBar(
                elevation: 0,
                backgroundColor: Colors.transparent,
                systemOverlayStyle: const SystemUiOverlayStyle(
                  statusBarColor: Colors.transparent,
                  statusBarBrightness: Brightness.dark,
                  statusBarIconBrightness: Brightness.light,
                  systemNavigationBarColor: Colors.black,
                ),
                leading: GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      width: 40,
                      height: 40,
                      margin: const EdgeInsets.only(left: 8),
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        size: 24,
                        color: Colors.white,
                      ),
                    )),
                centerTitle: true,
                title: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      '${currentIndex + 1} / ${widget.images.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    )),
              )),
        ]));
  }
}

abstract class ImageItem {
  const ImageItem();

  factory ImageItem.remote(
      {required String url, required int width, required int height}) {
    return RemoteImageItem(url: url, width: width, height: height);
  }

  factory ImageItem.local(
      {required String path, required int width, required int height}) {
    return LocalImageItem(path: path, width: width, height: height);
  }
}

class RemoteImageItem extends ImageItem {
  const RemoteImageItem(
      {required this.url, required this.width, required this.height});

  final String url;
  final int width;
  final int height;

  @override
  String toString() {
    return 'RemoteImageItem{url: $url, width: $width, height: $height}';
  }
}

class LocalImageItem extends ImageItem {
  const LocalImageItem(
      {required this.path, required this.width, required this.height});

  final String path;
  final int width;
  final int height;

  @override
  String toString() {
    return 'AssetImageItem{path: $path, width: $width, height: $height}';
  }
}
