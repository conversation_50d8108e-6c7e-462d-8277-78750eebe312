import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/models/user_identity.dart';
import 'package:praja/services/user/user_service.dart';

@injectable
class UserIdentityViewModel extends ViewModel {
  final _user = MutableLiveData<UserIdentity?>(null);

  LiveData<UserIdentity?> get user => _user;

  final UserService _userService;

  UserIdentityViewModel(this._userService);

  bool _isInitialized = false;
  Future<void> init(userId) async {
    if (_isInitialized) return;

    _isInitialized = true;
    final user = await _userService.getIdentity(userId);
    _user.value = user;
  }
}

extension UserIdentityViewModelX on BuildContext {
  UserIdentityViewModel userIdentityViewModel(int userId) =>
      getViewModel<UserIdentityViewModel>(key: userId.toString())..init(userId);
}
