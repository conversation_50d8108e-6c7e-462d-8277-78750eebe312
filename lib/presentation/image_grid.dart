import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:praja/features/direct_messaging/ui/chat_ui_constants.dart';
import 'package:praja/services/app_cache_manager.dart';

class ImageGrid extends StatelessWidget {
  const ImageGrid(
      {super.key,
      required this.imageUrls,
      this.borderRadius = ChatUiConstants.bubbleBorderRadius - 4,
      this.onImageTap});

  final List<String> imageUrls;
  final double borderRadius;
  final Function(String)? onImageTap;

  Widget _singleImage(String imageUrl) {
    return GestureDetector(
        onTap: () => onImageTap?.call(imageUrl),
        child: Container(
            width: double.infinity,
            height: double.infinity,
            clipBehavior: Clip.antiAlias,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(borderRadius),
            ),
            child: CachedNetworkImage(
                fit: BoxFit.cover,
                imageUrl: imageUrl,
                cacheManager: AppCacheManager.instance)));
  }

  Widget _twoImageColumn() {
    return Column(
      children: [
        Expanded(flex: 1, child: _singleImage(imageUrls[0])),
        const SizedBox(height: 2),
        Expanded(flex: 1, child: _singleImage(imageUrls[1])),
      ],
    );
  }

  Widget _threeImageGrid() {
    return Column(
      children: [
        Expanded(flex: 1, child: _singleImage(imageUrls[0])),
        const SizedBox(height: 2),
        Expanded(
            child: Row(
          children: [
            Expanded(flex: 1, child: _singleImage(imageUrls[1])),
            const SizedBox(width: 2),
            Expanded(flex: 1, child: _singleImage(imageUrls[2])),
          ],
        )),
      ],
    );
  }

  Widget _fourImageGrid() {
    return Column(
      children: [
        Expanded(
            child: Row(
          children: [
            Expanded(flex: 1, child: _singleImage(imageUrls[0])),
            const SizedBox(width: 2),
            Expanded(flex: 1, child: _singleImage(imageUrls[1])),
          ],
        )),
        const SizedBox(height: 2),
        Expanded(
            flex: 1,
            child: Row(
              children: [
                Expanded(flex: 1, child: _singleImage(imageUrls[2])),
                const SizedBox(width: 2),
                Expanded(flex: 1, child: _singleImage(imageUrls[3])),
              ],
            )),
      ],
    );
  }

  Widget _images() {
    if (imageUrls.length == 1) {
      return _singleImage(imageUrls[0]);
    } else if (imageUrls.length == 2) {
      return _twoImageColumn();
    } else if (imageUrls.length == 3) {
      return _threeImageGrid();
    } else {
      return _fourImageGrid();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 1,
      child: _images(),
    );
  }
}
