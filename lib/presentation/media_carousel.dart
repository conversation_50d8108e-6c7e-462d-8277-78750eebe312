import 'dart:io';
import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:praja/models/cloud_media_file.dart';
import 'package:praja/models/v2/local_image.dart';
import 'package:praja/models/v2/photo.dart';
import 'package:praja/services/app_cache_manager.dart';

typedef ItemTapCallback = void Function(int index);

/// For showing a horizontally scrolling list of images (or videos in the future)
/// Created to be used for posts
class MediaCarousel extends StatelessWidget {
  static const _gap = 8.0;

  /// The widest aspect ratio that we will allow is 2:1
  /// This is wider than what's aesthetically pleasing (4:3) but
  /// A lot of our users posts group shots and try to fit in as
  /// many people as possible, so we decided to keep 2:1 as our widest
  /// These images appear short on the screen, so there is incentive
  /// for the user to post a less wide / taller image
  static const _widestAspectRatio = 2 / 1;

  /// The narrowest aspect ratio that we will allow is 3:4
  /// beyond this, posts might look too tall and lesser posts
  /// might be consumed as a result.
  static const _narrowestAspectRatio = 3 / 4;

  /// The width of the carousel
  final double width;
  final List<MediaCarouselItem> items;
  final EdgeInsets padding;
  final ItemTapCallback? onItemTap;
  final IndexedWidgetBuilder? topRightOptionBuilder;
  final IndexedWidgetBuilder? bottomRightOptionBuilder;

  const MediaCarousel(
      {super.key,
      required this.items,
      required this.width,
      this.onItemTap,
      this.topRightOptionBuilder,
      this.bottomRightOptionBuilder,
      this.padding = EdgeInsets.zero});

  double _clampedAspectRatio(MediaCarouselItem item) {
    if (item.width == 0 || item.height == 0) {
      return 1;
    }
    final aspectRatio = item.width / item.height;
    return aspectRatio.clamp(_narrowestAspectRatio, _widestAspectRatio);
  }

  double _calculateHeight(MediaCarouselItem item, bool forMultiItem) {
    final clampedAspectRatio = _clampedAspectRatio(item);

    final availableWidth =
        width - padding.horizontal - (_gap * (forMultiItem ? 2 : 0));

    if (!forMultiItem) {
      // for a single image, within the limits of the aspect ratio
      // we can show the image at full width
      return availableWidth / clampedAspectRatio;
    } else if (clampedAspectRatio > 1) {
      // wide image, width is the limiting factor
      return (availableWidth / clampedAspectRatio);
    } else {
      // tall image, height is the limiting factor
      // assuming square limits; aesthetic choice
      return availableWidth;
    }
  }

  Widget _media(MediaCarouselItem item) {
    switch (item) {
      case RemoteMediaCarouselItem():
        return CachedNetworkImage(
          imageUrl: item.url,
          fit: BoxFit.cover,
          progressIndicatorBuilder: (context, _, downloadProgress) {
            return Container(
              color: Colors.grey[300],
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    height: 20,
                    width: 20,
                    child: downloadProgress.progress != null
                        ? CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                                Theme.of(context).primaryColor),
                            value: downloadProgress.progress,
                          )
                        : const CircularProgressIndicator(),
                  ),
                ],
              ),
            );
          },
          errorWidget: (context, url, error) {
            return const Center(child: Icon(Icons.error));
          },
          cacheManager: AppCacheManager.instance,
        );
      case LocalMediaCarouselItem():
        return Image.file(File(item.path), fit: BoxFit.cover);
    }
  }

  Widget _item(BuildContext context,
      {required MediaCarouselItem item,
      required double height,
      required bool forMultiItem}) {
    final aspectRatio = _clampedAspectRatio(item);
    final availableWidth =
        width - padding.horizontal - (_gap * (forMultiItem ? 2 : 0));
    final itemWidth = min(height * aspectRatio, availableWidth);

    return Stack(
      children: [
        Container(
          clipBehavior: Clip.antiAlias,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 2,
                offset: const Offset(0, 0),
              ),
            ],
          ),
          child: SizedBox(
            width: itemWidth,
            height: height,
            child: _media(item),
          ),
        ),
        if (topRightOptionBuilder != null)
          Positioned(
            top: 0,
            right: 0,
            child: topRightOptionBuilder!(context, items.indexOf(item)),
          ),
        if (bottomRightOptionBuilder != null)
          Positioned(
            bottom: 0,
            right: 0,
            child: bottomRightOptionBuilder!(context, items.indexOf(item)),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (items.isEmpty) {
      return const SizedBox();
    }
    final height = _calculateHeight(items.first, items.length > 1);

    return SizedBox(
      height: height + padding.vertical,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: padding,
        itemCount: items.length,
        cacheExtent: 200,
        itemBuilder: (context, index) {
          final item = items[index];
          return InkWell(
              onTap: () {
                onItemTap?.call(index);
              },
              child: _item(context,
                  item: item, height: height, forMultiItem: items.length > 1));
        },
        separatorBuilder: (context, index) {
          return const SizedBox(width: _gap);
        },
      ),
    );
  }
}

sealed class MediaCarouselItem {
  final int width;
  final int height;

  const MediaCarouselItem(this.width, this.height);

  factory MediaCarouselItem.fromPhoto(Photo photo) {
    final aspectRatio = photo.aspectRatio;
    int width;
    int height;
    if (aspectRatio == 0) {
      width = 100;
      height = 100;
    } else {
      width = 100;
      height = (width / aspectRatio).round();
    }
    return RemoteMediaCarouselItem(
        photo.placeholderUrl ?? photo.url, width, height);
  }

  factory MediaCarouselItem.fromCloudImageFile(CloudImageFile file) {
    int width;
    int height;
    if (file.width == null || file.height == null) {
      width = 100;
      height = 100;
    } else {
      width = file.width!;
      height = file.height!;
    }
    return RemoteMediaCarouselItem(file.cdnUrl, width, height);
  }

  factory MediaCarouselItem.fromLocalImage(LocalImage image) {
    int width;
    int height;
    if (image.width == null || image.height == null) {
      width = 100;
      height = 100;
    } else {
      width = image.width!.toInt();
      height = image.height!.toInt();
    }
    return LocalMediaCarouselItem(image.path, width, height);
  }
}

class RemoteMediaCarouselItem extends MediaCarouselItem {
  final String url;

  const RemoteMediaCarouselItem(this.url, int width, int height)
      : super(width, height);
}

class LocalMediaCarouselItem extends MediaCarouselItem {
  final String path;

  const LocalMediaCarouselItem(this.path, int width, int height)
      : super(width, height);
}
