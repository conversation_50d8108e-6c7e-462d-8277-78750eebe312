import 'package:flutter/material.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/features/localization/string_key.dart';

import 'nav_bar_view_model.dart';

const double navBarHeight = 56;

class GlobalNavBar extends StatelessWidget {
  final bool isRoot;
  const GlobalNavBar({super.key, this.isRoot = true});

  @override
  Widget build(BuildContext context) {
    final viewModel = context.globalNavViewModel;
    return Hero(
        tag: 'global-nav',
        child: LiveDataBuilder<List<NavItem>>(
          liveData: viewModel.navItems,
          builder: (context, navItems) {
            return LiveDataBuilder(
                liveData: viewModel.selectedIndex,
                builder: (context, selectedIndex) {
                  return NavBar(
                    navItems: navItems,
                    isRoot: isRoot,
                    selectedTabIndex: selectedIndex,
                    elevation: isRoot ? 4 : 0,
                    onTap: (index) {
                      viewModel.onTappedOn(index);
                      if (isRoot) return;

                      // go to root if the nav bar is not in root
                      final navigator = Navigator.of(context);
                      if (navigator.canPop()) {
                        Navigator.of(context)
                            .popUntil((route) => route.isFirst);
                      }
                    },
                  );
                });
          },
        ));
  }
}

class NavBar extends StatelessWidget {
  final List<NavItem> navItems;
  final int selectedTabIndex;
  final Function(int) onTap;
  final double elevation;
  final bool isRoot;
  const NavBar(
      {required this.navItems,
      required this.onTap,
      required this.selectedTabIndex,
      this.elevation = 4,
      required this.isRoot,
      super.key});

  Widget _getNavItemWidget(BuildContext context, int index) {
    final viewModel = context.globalNavViewModel;
    final navItem = navItems[index];
    if (navItem.name == NavItemName.chat) {
      return Expanded(
          child: LiveDataBuilder<UnreadChatsState>(
              liveData: viewModel.unreadChatsState,
              builder: (context, state) {
                return NavBarButton(
                    iconData: navItem.icon,
                    label: context.getString(navItem.localizedNameKey),
                    isSelected: index == selectedTabIndex,
                    onTap: () {
                      onTap(index);
                    },
                    badgeCount: state.primaryUnreadCount,
                    showUnreadDot: state.secondaryUnreadChatsPresent);
              }));
    } else if (navItem.name == NavItemName.notifications) {
      return Expanded(
          child: LiveDataBuilder<int>(
              liveData: viewModel.unreadNotificationsCount,
              builder: (context, unreadNotificationsCount) {
                return NavBarButton(
                    iconData: navItem.icon,
                    label: context.getString(navItem.localizedNameKey),
                    isSelected: index == selectedTabIndex,
                    onTap: () {
                      onTap(index);
                    },
                    badgeCount: unreadNotificationsCount);
              }));
    } else if (navItem.name == NavItemName.trends) {
      return Expanded(
        child: LiveDataBuilder<bool>(
          liveData: viewModel.showTrendsUnreadDot,
          builder: (context, showTrendsUnreadDot) {
            return NavBarButton(
              iconData: navItem.icon,
              label: context.getString(navItem.localizedNameKey),
              isSelected: index == selectedTabIndex,
              onTap: () {
                onTap(index);
              },
              showUnreadDot: showTrendsUnreadDot &&
                  (!isRoot || (isRoot && index != selectedTabIndex)),
            );
          },
        ),
      );
    }
    return Expanded(
        child: NavBarButton(
            iconData: navItem.icon,
            label: context.getString(navItem.localizedNameKey),
            isSelected: index == selectedTabIndex,
            onTap: () {
              onTap(index);
            }));
  }

  BoxDecoration _getDecoration({required bool isDarkMode}) {
    if (elevation == 0) {
      return BoxDecoration(color: isDarkMode ? Colors.black : Colors.white);
    }
    return BoxDecoration(
        color: isDarkMode ? const Color(0xFF292929) : Colors.white,
        borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(16), topRight: Radius.circular(16)),
        boxShadow: isDarkMode
            ? null
            : [BoxShadow(color: Colors.black12, blurRadius: elevation)]);
  }

  @override
  Widget build(BuildContext context) {
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    return AnimatedContainer(
      padding: EdgeInsets.only(bottom: bottomPadding),
      duration: const Duration(milliseconds: 300),
      height: navItems.isEmpty ? 0 : navBarHeight + bottomPadding,
      width: double.infinity,
      clipBehavior: Clip.antiAlias,
      decoration: navItems.isEmpty
          ? const BoxDecoration(color: Colors.white)
          : _getDecoration(
              isDarkMode: Theme.of(context).brightness == Brightness.dark),
      child: SizedBox(
          height: navBarHeight,
          child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                for (var index = 0; index < navItems.length; index++)
                  _getNavItemWidget(context, index)
              ])),
    );
  }
}

class NavBarButton extends StatelessWidget {
  final String label;
  final IconData iconData;
  final bool isSelected;
  final int badgeCount;
  final Function() onTap;
  final bool showUnreadDot;

  const NavBarButton(
      {required this.label,
      required this.iconData,
      required this.isSelected,
      required this.onTap,
      this.badgeCount = 0,
      this.showUnreadDot = false,
      super.key});

  @override
  Widget build(BuildContext context) {
    final bool isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final badgeText = badgeCount < 100 ? badgeCount.toString() : '99+';
    return SizedBox.expand(
      child: Material(
          color: Colors.transparent,
          child: InkWell(
            customBorder: const CircleBorder(eccentricity: 0.5),
            onTap: onTap,
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Stack(
                    clipBehavior: Clip.none,
                    alignment: Alignment.center,
                    children: [
                      Icon(iconData,
                          color: isSelected
                              ? isDarkMode
                                  ? Colors.white
                                  : Theme.of(context).primaryColor
                              : const Color(0xFFA8A8A8),
                          size: 28),
                      if (badgeCount > 0)
                        Positioned(
                          top: -2,
                          right: -4,
                          child: Container(
                            height: 16,
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.error,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            constraints: const BoxConstraints(
                              minWidth: 16,
                            ),
                            child: Center(
                                widthFactor: 1,
                                child: Text(
                                  badgeText,
                                  textScaler: const TextScaler.linear(1.0),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 11,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  textAlign: TextAlign.center,
                                )),
                          ),
                        ),
                      if (showUnreadDot && badgeCount <= 0)
                        Positioned(
                          top: 4,
                          right: 4,
                          child: Container(
                            height: 5,
                            width: 5,
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.error,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                    ]),
                const SizedBox(height: 2),
                Text(label,
                    maxLines: 1,
                    style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: isSelected
                            ? isDarkMode
                                ? Colors.white
                                : Theme.of(context).primaryColor
                            : const Color(0xFF989898),
                        fontSize: 12))
              ],
            ),
          )),
    );
  }
}
