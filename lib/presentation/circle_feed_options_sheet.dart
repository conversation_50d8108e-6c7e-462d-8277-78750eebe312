import 'package:flutter/material.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/circle.dart';
import 'package:praja/models/feed_option.dart';
import 'package:praja/presentation/praja_icons.dart';

extension CircleFeedOptionSheetX on BuildContext {
  void _circleFeedSelectionSheetLogEvent(
      String source, int circleId, String status) {
    AppAnalytics.logEvent(
      name: "circle_feed_selection_sheet",
      parameters: {
        "source": source,
        "circle_id": circleId,
        "status": status,
      },
    );
  }

  void _circleFeedSelectedLogEvent(String feedType, int circleId) {
    AppAnalytics.logEvent(
      name: "circle_feed_selected",
      parameters: {
        "feed_type": feedType,
        "circle_id": circleId,
      },
    );
  }

  void showCircleFeedOptions(Circle circle,
      Function(FeedOption selectedFeedOption)? selectedOptionCallBack) {
    _circleFeedSelectionSheetLogEvent('feed_button', circle.id, 'opened');

    showModalBottomSheet(
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(10.0),
            topRight: Radius.circular(10.0),
          ),
        ),
        context: this,
        builder: (context) {
          return PopScope(
            canPop: true,
            onPopInvoked: (bool popped) {
              if (popped) {
                _circleFeedSelectionSheetLogEvent(
                    'back_button', circle.id, 'closed');
              }
            },
            child: Wrap(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(
                          top: 30.0, left: 30, bottom: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Icon(
                            PrajaIcons.posters,
                            size: 24,
                            color: Theme.of(context).primaryColor,
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Text(
                            context.getString(StringKey.selectFeedLabel),
                            style: const TextStyle(
                                height: 1.5,
                                fontSize: 14,
                                fontWeight: FontWeight.w600),
                          ),
                        ],
                      ),
                    ),
                    const Divider(
                      thickness: 0.5,
                      height: 0,
                    ),
                    ListView.separated(
                        shrinkWrap: true,
                        itemCount: circle.feedOptions.length,
                        separatorBuilder: (context, index) {
                          return const Divider(
                            thickness: 0.5,
                            height: 0,
                          );
                        },
                        itemBuilder: (context, index) {
                          return Padding(
                            padding: const EdgeInsets.only(left: 30.0),
                            child: TextButton(
                                onPressed: () {
                                  _circleFeedSelectedLogEvent(
                                      circle.feedOptions[index].displayName,
                                      circle.id);
                                  Navigator.pop(context);
                                  _circleFeedSelectionSheetLogEvent(
                                      'feed_option_selected',
                                      circle.id,
                                      'closed');
                                  selectedOptionCallBack
                                      ?.call(circle.feedOptions[index]);
                                },
                                child: SizedBox(
                                  width: double.infinity,
                                  child: Text(
                                    circle.feedOptions[index].displayName,
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontSize: 15,
                                    ),
                                  ),
                                )),
                          );
                        }),
                  ],
                )
              ],
            ),
          );
        });
  }
}
