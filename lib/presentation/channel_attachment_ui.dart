import 'package:flutter/material.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/features/direct_messaging/database/entities/db_conversation.dart';
import 'package:praja/features/direct_messaging/models/attachment.dart';
import 'package:praja/features/direct_messaging/presentation/chat_page/chat_page.dart';
import 'package:praja/features/direct_messaging/ui/channel_preview_ui.dart';
import 'package:praja/features/direct_messaging/ui/chat_ui_constants.dart';
import 'package:praja/features/direct_messaging/ui/draft_attachments_ui.dart';
import 'package:praja/models/circle_identity.dart';
import 'package:praja/presentation/circle_identity_view_model.dart';

class ChannelAttachmentUI extends StatelessWidget {
  final ChannelAttachmentData attachmentData;
  final double borderRadius;
  final bool elevated;
  final VoidCallback? onTap;

  const ChannelAttachmentUI(
      {super.key,
      required this.attachmentData,
      this.borderRadius = ChatUiConstants.bubbleBorderRadius,
      this.elevated = false,
      this.onTap});

  @override
  Widget build(BuildContext context) {
    final viewModel =
        context.circleIdentityViewModel(int.parse(attachmentData.circleId));
    return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(borderRadius),
          border: Border.all(color: Colors.grey.shade300),
          boxShadow: elevated
              ? [
                  BoxShadow(
                    color: Colors.grey.shade300,
                    blurRadius: 0.5,
                    offset: const Offset(0, 1),
                  )
                ]
              : null,
        ),
        child: LiveDataBuilder<CircleIdentity?>(
            liveData: viewModel.circleIdentity,
            builder: (ctx, circleIdentity) {
              if (circleIdentity == null) {
                return ChannelPreviewUI.shimmer();
              } else {
                return ChannelPreviewUI(
                  circleIdentity: circleIdentity,
                  onTap: () {
                    onTap?.call();
                    Navigator.of(context).push(MaterialPageRoute(
                        builder: (ctx) => ChatPage.withCircle(
                            circleIdentity.id, ConversationType.channel)));
                  },
                );
              }
            }));
  }
}

class ChannelDraftAttachmentUI extends StatelessWidget {
  final ChannelAttachmentData attachmentData;
  final VoidCallback? onRemove;

  const ChannelDraftAttachmentUI(
      {super.key, required this.attachmentData, this.onRemove});

  @override
  Widget build(BuildContext context) {
    final viewModel =
        context.circleIdentityViewModel(int.parse(attachmentData.circleId));
    return Container(
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade300,
              blurRadius: 8,
              offset: const Offset(0, 2),
            )
          ],
        ),
        child: Removable(
            animationDuration: Duration.zero, // zero, skip animation
            onRemove: onRemove,
            child: LiveDataBuilder<CircleIdentity?>(
              liveData: viewModel.circleIdentity,
              builder: (ctx, circleIdentity) {
                if (circleIdentity == null) {
                  return ChannelPreviewUI.shimmer(showCTA: false);
                } else {
                  return ChannelPreviewUI(
                    circleIdentity: circleIdentity,
                    showCTA: false,
                  );
                }
              },
            )));
  }
}
