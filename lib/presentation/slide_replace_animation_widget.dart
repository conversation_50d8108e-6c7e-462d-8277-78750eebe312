import 'package:flutter/material.dart';

class SlideReplaceAnimationWidget extends StatefulWidget {
  final Widget before;
  final Widget? after;
  final VoidCallback? onAnimationComplete;

  const SlideReplaceAnimationWidget(
      {required this.before, this.after, this.onAnimationComplete, super.key});

  @override
  State<StatefulWidget> createState() {
    return _SlideReplaceAnimationState();
  }
}

class _SlideReplaceAnimationState extends State<SlideReplaceAnimationWidget>
    with SingleTickerProviderStateMixin {
  bool clip = false;
  late AnimationController _controller;

  late Animation<double> _lineAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
        duration: const Duration(milliseconds: 800), vsync: this);
    _controller.repeat(reverse: true);

    _lineAnimation = Tween<double>(begin: -1.0, end: 1.0).animate(_controller);
  }

  @override
  void didUpdateWidget(covariant SlideReplaceAnimationWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.after != null && widget.after == null) {
      setState(() {
        clip = false;
        _controller.repeat(reverse: true);
        _lineAnimation =
            Tween<double>(begin: -1.0, end: 1.0).animate(_controller);
      });
    } else if (oldWidget.after == null && widget.after != null) {
      _playEffect();
    }
  }

  void _playEffect() async {
    _controller.stop();
    await _controller.animateTo(0.0);
    if (mounted) {
      setState(() {
        clip = true;
      });
    }
    if (mounted) {
      await _controller.animateTo(1.0,
          duration: const Duration(milliseconds: 1200));
    }
    widget.onAnimationComplete?.call();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      Opacity(opacity: clip ? 0 : 1, child: widget.before),
      if (clip && widget.after != null)
        Positioned.fill(
          child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return ClipRect(
                    clipper: TopClipper(ratio: _controller.value),
                    child: child);
              },
              child: widget.after),
        ),
      if (clip)
        Positioned.fill(
          child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return ClipRect(
                    clipper: BottomClipper(ratio: 1 - _controller.value),
                    child: child);
              },
              child: widget.before),
        ),
      Positioned.fill(
          child: AnimatedBuilder(
              animation: _lineAnimation,
              builder: (BuildContext context, Widget? child) {
                return Align(
                    alignment: Alignment(0.0, _lineAnimation.value),
                    child: child);
              },
              child: Container(
                  width: double.infinity,
                  height: 4,
                  decoration: BoxDecoration(color: Colors.white, boxShadow: [
                    BoxShadow(
                        color: Colors.white.withOpacity(0.4),
                        offset: const Offset(0, 4),
                        spreadRadius: 8,
                        blurRadius: 2)
                  ])))),
    ]);
  }
}

class TopClipper extends CustomClipper<Rect> {
  final double ratio; // ratio should be between 0.0 and 1.0

  TopClipper({required this.ratio});

  @override
  Rect getClip(Size size) {
    // Clip from the top based on the ratio
    double clipHeight = size.height * ratio;
    return Rect.fromLTWH(0, 0, size.width, clipHeight);
  }

  @override
  bool shouldReclip(covariant TopClipper oldClipper) {
    return oldClipper.ratio != ratio;
  }
}

class BottomClipper extends CustomClipper<Rect> {
  final double ratio; // ratio should be between 0.0 and 1.0

  BottomClipper({required this.ratio});

  @override
  Rect getClip(Size size) {
    // Clip from the bottom based on the ratio
    double clipHeight = size.height * (1 - ratio);
    return Rect.fromLTWH(0, clipHeight, size.width, size.height * ratio);
  }

  @override
  bool shouldReclip(covariant BottomClipper oldClipper) {
    return oldClipper.ratio != ratio;
  }
}
