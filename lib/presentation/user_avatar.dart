import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart' hide Badge;
import 'package:flutter_svg/svg.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/features/user/models/app_user.dart';
import 'package:praja/models/badge.dart';
import 'package:praja/models/user_identity.dart';
import 'package:praja/presentation/avatar.dart';
import 'package:praja/presentation/user_identity_view_model.dart';
import 'package:praja/services/app_cache_manager.dart';
import 'package:praja/utils/color_utils.dart';
import 'package:praja/utils/widgets/bounceWrapper.dart';
import 'package:shimmer/shimmer.dart';

import '../models/v2/photo.dart';
import 'combined_load_callback.dart';

const double ringWidthSizeRatio = 0.055;
// icon size is 78x100
const double smallIconWidthSizeRatio = 0.38; // for size < 100
const double smallIconHeightSizeRatio = 0.487;
const double bigIconWidthSizeRatio = 0.257; // for size >= 100
const double bigIconHeightSizeRatio = 0.33;
const double badgeAngleDegrees = 30;

class UserAvatar extends StatelessWidget {
  final String name;
  final Color color;
  final Photo? photo;
  final Badge? badge;
  final double size;
  final bool showIcon;
  final double strokeWidth;
  final bool animateBadgeIcon;
  final Color strokeColor;
  final VoidCallback? onLoaded;
  final Widget? noPhotoFallback;
  final bool alwaysFullSize;

  static const entityPhoto = 'photo';
  static const entityBadgeIcon = 'badgeIcon';

  final CombinedLoadCallback _combinedLoadCallback;

  UserAvatar({
    super.key,
    required this.name,
    required this.color,
    this.photo,
    this.badge,
    this.onLoaded,
    this.size = 48,
    this.showIcon = true,
    this.strokeWidth = 0,
    this.strokeColor = Colors.white,
    this.animateBadgeIcon = false,
    this.alwaysFullSize = false,
    this.noPhotoFallback,
  }) : _combinedLoadCallback = CombinedLoadCallback(
          entityNames: {entityPhoto, entityBadgeIcon},
          onLoaded: onLoaded,
        );

  factory UserAvatar.fromIdentity(
    UserIdentity userIdentity, {
    double size = 48,
    double strokeWidth = 0,
    Color strokeColor = Colors.white,
    bool showBadge = true,
    bool animateBadgeIcon = false,
    bool alwaysFullSize = false,
    VoidCallback? onLoaded,
    Widget? noPhotoFallback,
  }) {
    return UserAvatar(
      name: userIdentity.name,
      color: userIdentity.avatarColor,
      photo: userIdentity.photo,
      badge: showBadge ? userIdentity.badge : null,
      size: size,
      alwaysFullSize: alwaysFullSize,
      strokeWidth: strokeWidth,
      strokeColor: strokeColor,
      animateBadgeIcon: animateBadgeIcon,
      onLoaded: onLoaded,
      noPhotoFallback: noPhotoFallback,
    );
  }

  factory UserAvatar.fromAppUser(
    AppUser appUser, {
    double size = 48,
    double strokeWidth = 0,
    Color strokeColor = Colors.white,
    bool showBadge = true,
    bool animateBadgeIcon = false,
    bool alwaysFullSize = false,
    VoidCallback? onLoaded,
    Widget? noPhotoFallback,
  }) {
    return UserAvatar(
      name: appUser.name,
      color: HexColor.fromHex(appUser.avatarColor),
      photo: appUser.photo,
      badge: showBadge ? appUser.badge : null,
      size: size,
      alwaysFullSize: alwaysFullSize,
      strokeWidth: strokeWidth,
      strokeColor: strokeColor,
      animateBadgeIcon: animateBadgeIcon,
      onLoaded: onLoaded,
      noPhotoFallback: noPhotoFallback,
    );
  }

  static Widget forUserId(
    BuildContext context,
    int userId, {
    double size = 48,
    double strokeWidth = 0,
    Color strokeColor = Colors.white,
    bool showBadge = true,
    bool animateBadgeIcon = false,
    bool alwaysFullSize = false,
    VoidCallback? onLoaded,
    Widget? noPhotoFallback,
  }) {
    final viewModel = context.userIdentityViewModel(userId);
    return LiveDataBuilder(
      liveData: viewModel.user,
      builder: (context, userIdentity) {
        if (userIdentity == null) return UserAvatar.shimmer(size: size);
        return UserAvatar.fromIdentity(
          userIdentity,
          size: size,
          strokeWidth: strokeWidth,
          strokeColor: strokeColor,
          showBadge: showBadge,
          animateBadgeIcon: animateBadgeIcon,
          alwaysFullSize: alwaysFullSize,
          onLoaded: onLoaded,
          noPhotoFallback: noPhotoFallback,
        );
      },
    );
  }

  static Widget shimmer({
    double size = 48,
    double strokeWidth = 0,
    Color strokeColor = Colors.white,
  }) {
    return _Shimmer(
      size: size,
      strokeWidth: strokeWidth,
      strokeColor: strokeColor,
    );
  }

  BadgeRing get badgeRing => badge?.badgeRing ?? BadgeRing.noRing;

  static Offset badgeIconTopLeft({
    required double iconWidth,
    required double size,
    required double ringWidth,
    required bool hasRing,
  }) {
    // icon should be 30 deg below equator on the right side
    double iconCenterFromAvatarCenter =
        size / 2 - (hasRing ? ringWidth / 2 : ringWidth);
    double iconCenterX = size / 2 +
        iconCenterFromAvatarCenter * cos(badgeAngleDegrees * pi / 180);
    double iconCenterY = size / 2 +
        iconCenterFromAvatarCenter * sin(badgeAngleDegrees * pi / 180);

    return Offset(iconCenterX - iconWidth / 2, iconCenterY - iconWidth / 2);
  }

  static double ringWidth({required double size}) {
    return size * ringWidthSizeRatio;
  }

  static double badgeIconWidth({required double size}) {
    return size >= 100
        ? size * bigIconWidthSizeRatio
        : size * smallIconWidthSizeRatio;
  }

  static double badgeIconHeight({required double size}) {
    return size >= 100
        ? size * bigIconHeightSizeRatio
        : size * smallIconHeightSizeRatio;
  }

  Widget _badgeIcon({
    required double iconWidth,
    required double iconHeight,
  }) {
    return CachedNetworkImage(
        fadeInDuration: Duration.zero,
        fadeOutDuration: Duration.zero,
        imageUrl: badge!.badgeIconUrl!,
        width: iconWidth,
        height: iconHeight,
        cacheManager: AppCacheManager.instance,
        imageBuilder: (context, imageProvider) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _combinedLoadCallback.onEntityLoaded(entityBadgeIcon);
          });
          return SizedBox(
            width: iconWidth,
            height: iconHeight,
            child: Image(image: imageProvider),
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    double ringWidth = size * ringWidthSizeRatio;

    double iconWidth = size >= 100
        ? size * bigIconWidthSizeRatio
        : size * smallIconWidthSizeRatio;
    double iconHeight = size >= 100
        ? size * bigIconHeightSizeRatio
        : size * smallIconHeightSizeRatio;
    Offset iconPosition = badgeIconTopLeft(
      iconWidth: iconWidth,
      size: size,
      ringWidth: ringWidth,
      hasRing: badgeRing != BadgeRing.noRing,
    );
    if (badge?.badgeIconUrl == null) {
      _combinedLoadCallback.onEntityLoaded(entityBadgeIcon);
    }
    return SizedBox(
        width: size,
        height: size,
        child: Stack(
          clipBehavior: Clip.none,
          fit: StackFit.expand,
          children: [
            Align(
              alignment: Alignment.center,
              child: (photo == null && noPhotoFallback != null)
                  ? noPhotoFallback
                  : Avatar(
                      name: name,
                      color: color,
                      photo: photo,
                      size: alwaysFullSize && badgeRing == BadgeRing.noRing
                          ? size
                          : size - 2 * ringWidth,
                      onLoaded: () {
                        _combinedLoadCallback.onEntityLoaded(entityPhoto);
                      },
                    ),
            ),
            if (strokeWidth > 0 &&
                (badge?.badgeRing ?? BadgeRing.noRing) == BadgeRing.noRing)
              Align(
                alignment: Alignment.center,
                child: Container(
                  width: size - 2 * strokeWidth,
                  height: size - 2 * strokeWidth,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: strokeColor,
                      width: strokeWidth,
                    ),
                  ),
                ),
              ),
            BadgeRingUI(
              ring: badgeRing,
              size: size,
            ),
            if (badge?.badgeIconUrl != null)
              Positioned(
                  top: iconPosition.dy,
                  left: iconPosition.dx,
                  child: animateBadgeIcon
                      ? FadeWrapper(
                          child: BounceWrapper(
                              child: _badgeIcon(
                          iconWidth: iconWidth,
                          iconHeight: iconHeight,
                        )))
                      : _badgeIcon(
                          iconWidth: iconWidth,
                          iconHeight: iconHeight,
                        )),
          ],
        ));
  }
}

class _Shimmer extends StatelessWidget {
  final double size;
  final double strokeWidth;
  final Color strokeColor;

  const _Shimmer({
    required this.size,
    this.strokeWidth = 0,
    this.strokeColor = Colors.white,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: Center(
        child: Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
              width: size - 2 * size * ringWidthSizeRatio,
              height: size - 2 * size * ringWidthSizeRatio,
              decoration: BoxDecoration(
                  color: Colors.grey,
                  shape: BoxShape.circle,
                  border: Border.all(color: strokeColor, width: strokeWidth))),
        ),
      ),
    );
  }
}

class BadgeRingUI extends StatelessWidget {
  final BadgeRing ring;
  final double size;

  const BadgeRingUI({
    super.key,
    required this.ring,
    required this.size,
  });

  @override
  Widget build(BuildContext context) {
    if (ring == BadgeRing.noRing) return const SizedBox();

    return SizedBox(
        width: size,
        height: size,
        child: SvgPicture.asset(
          ring.svgPath(),
          fit: BoxFit.contain,
        ));
  }
}

extension SvgPath on BadgeRing {
  String svgPath() {
    if (this == BadgeRing.goldRing) {
      return "assets/images/badges/g_strip.svg";
    } else if (this == BadgeRing.silverRing) {
      return "assets/images/badges/s_strip.svg";
    } else {
      throw ArgumentError("No Svg Path for BadgeRing: $this");
    }
  }
}
