import 'package:flutter/material.dart';
import 'package:praja/models/report_item.dart';

class MultiSelectChip extends StatefulWidget {
  final List<ReportItem> list;
  final Function(ReportItem) onSelected;

  const MultiSelectChip(
      {Key? key, required this.list, required this.onSelected})
      : super(key: key);

  @override
  State<MultiSelectChip> createState() => _MultiSelectChipState();
}

class _MultiSelectChipState extends State<MultiSelectChip> {
  ReportItem? selectedChoice;

  _buildChoiceList() {
    List<Widget> choices = [];
    for (var item in widget.list) {
      final selected = selectedChoice == item;
      choices.add(Container(
        padding: const EdgeInsets.all(2.0),
        child: ChoiceChip(
          label: Text(item.name,
              style: TextStyle(color: selected ? Colors.white : Colors.black)),
          selected: selected,
          selectedColor: Theme.of(context).primaryColor,
          backgroundColor: Colors.grey[200],
          onSelected: (selected) {
            setState(() {
              selectedChoice = item;
            });
            widget.onSelected(item);
          },
        ),
      ));
    }

    return choices;
  }

  @override
  Widget build(BuildContext context) {
    return Wrap(
      children: _buildChoiceList(),
    );
  }
}
