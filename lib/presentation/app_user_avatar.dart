import 'package:flutter/material.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/features/user/models/app_user.dart';

import 'logged_in_user_view_model.dart';
import 'user_avatar.dart';

/// AppUser (logged in user)'s avatar that reacts to changes in
/// app user data stored on the device
class AppUserAvatar extends StatelessWidget {
  final double size;
  final bool showIcon;
  final double strokeWidth;
  final Color strokeColor;
  final VoidCallback? onLoaded;
  final Widget? noPhotoFallback;
  final bool alwaysFullSize;

  const AppUserAvatar({
    super.key,
    this.size = 48,
    this.showIcon = true,
    this.strokeWidth = 0,
    this.strokeColor = Colors.white,
    this.alwaysFullSize = false,
    this.noPhotoFallback,
    this.onLoaded,
  });

  @override
  Widget build(BuildContext context) {
    final viewModel = context.viewModel;
    return LiveDataBuilder<AppUser?>(
        liveData: viewModel.user,
        builder: (context, user) {
          if (user == null) return SizedBox.square(dimension: size);

          return UserAvatar.fromAppUser(user,
              size: size,
              strokeColor: strokeColor,
              strokeWidth: strokeWidth,
              alwaysFullSize: alwaysFullSize,
              noPhotoFallback: noPhotoFallback,
              onLoaded: onLoaded);
        });
  }
}

extension on BuildContext {
  LoggedInUserViewModel get viewModel => getViewModel<LoggedInUserViewModel>();
}
