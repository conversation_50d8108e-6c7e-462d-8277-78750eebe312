import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/common/news_feed_tabs_view_model.dart';
import 'package:praja/common/news_feed_widget.dart';
import 'package:praja/common/widgets/focus_detector.dart';
import 'package:praja/core/ui/page_view_reporter.dart';
import 'package:praja/enums/news_feed_type.dart';
import 'package:praja/features/my_feed/my_feed_widget.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/presentation/lazy_load.dart';
import 'package:praja/screens/posts/trending_feed.dart';
import 'package:praja/services/event_flag.dart';

class NewsFeedSections extends StatefulWidget {
  final VoidCallback viewAllSuggested;
  final EdgeInsets padding;
  final TabController tabController;

  const NewsFeedSections({
    Key? key,
    required this.viewAllSuggested,
    this.padding = EdgeInsets.zero,
    required this.tabController,
  }) : super(key: key);

  @override
  State<NewsFeedSections> createState() => _NewsFeedSectionsState();
}

class _NewsFeedSectionsState extends State<NewsFeedSections>
    with TickerProviderStateMixin, RouteAware {
  bool isTutorialNeedsTobeShown = false;
  late TabController _tabController;

  late NewsFeedTabsViewModel _newsFeedTabsViewModel;
  late List<Widget> _tabWidgets;

  @override
  void initState() {
    super.initState();
    _newsFeedTabsViewModel = context.getViewModel<NewsFeedTabsViewModel>();
    _logDefaultFeedLoaded();
    _tabController = widget.tabController;
    _tabWidgets = _setupTabWidgets();
  }

  void _logDefaultFeedLoaded() {
    AppAnalytics.onDefaultFeedLoaded(_newsFeedTabsViewModel.feeds[0].id);
  }

  List<Widget> _setupTabWidgets() {
    final newsFeeds = _newsFeedTabsViewModel.feeds;
    List<Widget> tabWidgets = [];

    for (var i = 0; i < newsFeeds.length; i++) {
      if (newsFeeds[i].type == NewsFeedTypeEnum.trending) {
        tabWidgets.add(
          ViewModelScope(
            builder: (_) => FocusDetector(
              onVisibilityGained: () {
                if (GetIt.I.get<EventFlags>().isFeedLoadingInBackground) {
                  AppAnalytics.logAppOpenByReturnUsers("App Icon");
                  AppAnalytics.logEvent(name: "trending_feed");
                  GetIt.I.get<EventFlags>().isFeedLoadingInBackground = false;
                  if (GetIt.I.get<EventFlags>().isFeedLoaded) {
                    AppAnalytics.logEvent(
                      name: "trending_feed_loaded",
                    );
                    GetIt.I.get<EventFlags>().isFeedLoaded = false;
                  }
                  if (GetIt.I.get<EventFlags>().isFeedpage1Loaded) {
                    AppAnalytics.logEvent(
                      name: "trending_feed_page",
                      parameters: {"page": 1},
                    );
                    GetIt.I.get<EventFlags>().isFeedpage1Loaded = false;
                  }
                  if (GetIt.I.get<EventFlags>().isTrendingTopicsLoaded) {
                    AppAnalytics.logEvent(name: "trending_topics");
                  }
                }
              },
              child: LazyLoadWidget(
                  child: TrendingFeed(
                scrollController: _newsFeedTabsViewModel.scrollControllers[i],
                viewAllSuggested: widget.viewAllSuggested,
                padding: widget.padding.copyWith(top: widget.padding.top),
              )),
            ),
          ),
        );
      } else if (newsFeeds[i].type == NewsFeedTypeEnum.myFeed) {
        tabWidgets.add(
          ViewModelScope(
            builder: (_) => LazyLoadWidget(
                child: MyFeedWidget(
              scrollController: _newsFeedTabsViewModel.scrollControllers[i],
              viewAllSuggested: widget.viewAllSuggested,
              padding: widget.padding.copyWith(top: widget.padding.top),
            )),
          ),
        );
      } else {
        tabWidgets.add(
          ViewModelScope(
            builder: (_) => LazyLoadWidget(
              child: NewsFeedWidget(
                padding: widget.padding,
                scrollController: _newsFeedTabsViewModel.scrollControllers[i],
                feedId: newsFeeds[i].id,
                feedPath: newsFeeds[i].path,
                source: newsFeeds[i].id,
              ),
            ),
          ),
        );
      }
    }
    return tabWidgets;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final route = ModalRoute.of(context);
    if (route == null) return;
    PageViewReporter.routeObserver.subscribe(this, route);
  }

  @override
  void dispose() {
    PageViewReporter.routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TabBarView(
      controller: _tabController,
      children: _tabWidgets,
    );
  }
}
