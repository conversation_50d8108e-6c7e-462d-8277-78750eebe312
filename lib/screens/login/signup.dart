import 'dart:async';
import 'dart:core';
import 'dart:io';

import 'package:android_sms_retriever/android_sms_retriever.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:praja/common/az_list_view/az_common.dart';
import 'package:praja/common/az_list_view/az_listview.dart';
import 'package:praja/constants/AppConstants.dart';
import 'package:praja/core/ui/page.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/extensions/truecaller_user_profile_ext.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/on_boarding/service/auth_service.dart';
import 'package:praja/features/user/models/app_user.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/circle.dart';
import 'package:praja/screens/external_web_view.dart';
import 'package:praja/screens/login/login.dart';
import 'package:praja/services/endpoint.dart';
import 'package:praja/services/event_flag.dart';
import 'package:praja/styles.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/utils/utils.dart';
import 'package:praja/utils/widgets.dart';
import 'package:truecaller_sdk/truecaller_sdk.dart';

class SignUpPage extends BasePage {
  final int phone;
  final String otp;
  final TruecallerUserProfile? truecallerUserProfile;
  final void Function(AppUser) onSignUp;

  @override
  String get pageName => 'sign_up';

  @override
  Map<String, dynamic> get pageParams => {
        'phone': phone,
      };

  const SignUpPage(
      {super.key,
      required this.phone,
      required this.otp,
      required this.onSignUp,
      this.truecallerUserProfile});

  @override
  Widget buildContent(BuildContext context) {
    return SignUpPageInner(
      phone: phone,
      otp: otp,
      onSignUp: onSignUp,
      truecallerUserProfile: truecallerUserProfile,
    );
  }
}

class SignUpPageInner extends StatefulWidget {
  final int phone;
  final String otp;
  final TruecallerUserProfile? truecallerUserProfile;
  final void Function(AppUser) onSignUp;

  const SignUpPageInner(
      {required this.phone,
      required this.otp,
      required this.onSignUp,
      super.key,
      this.truecallerUserProfile});

  @override
  State<SignUpPageInner> createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPageInner> {
  TextEditingController nameController = TextEditingController();
  TextEditingController otpController = TextEditingController();

  bool _loading = false;
  bool isListening = false;
  bool _isTruecaller = false;

  bool autoDetected = false;
  List<Circle> districts = [];
  List<Circle> mandals = [];
  List<Circle> villages = [];
  Circle? district;
  Circle? mandal;
  Circle? village;
  Timer? timer;
  Duration resendDuration = const Duration(seconds: 30);
  bool _showOtpResend = false;
  final int _codeLength = 5;
  final int _codeLengthForInternalAccounts = 6;

  bool mandalsLoaded = false;
  bool villagesLoaded = false;
  bool mandalsLoading = false;
  bool villagesLoading = false;

  bool isOtpAutoFilled = false;

  final AuthService _authService = GetIt.I.get<AuthService>();

  @override
  void initState() {
    super.initState();
    // //app open by return users event needs to be triggered with already registered user
    // //to remove that event from new users making it's triggered flag as true here
    GetIt.I.get<EventFlags>().appOpenByReturnUsersTriggered = true;
    final TruecallerUserProfile? truecallerUserProfile =
        widget.truecallerUserProfile;
    if (truecallerUserProfile != null) {
      _isTruecaller = true;

      nameController.text = truecallerUserProfile.fullName;
    } else {
      otpController.text = widget.otp;
      if (Platform.isAndroid) {
        isListening = true;
        AndroidSmsRetriever.listenForSms().then((sms) {
          if (sms == null) return;

          String? code = Utils.getCode(sms);
          AndroidSmsRetriever.stopSmsListener();

          if (code != null && code.isNotEmpty) {
            AppAnalytics.logEvent(name: "otp_autofill");
            setState(() {
              otpController.text = code;
              isListening = false;
              isOtpAutoFilled = true;
            });
          }
        }).catchError((e) {
          logNonFatal("Error at Listen For Sms in SignUp Page", e);
        });
      }
    }

    AppAnalytics.logEvent(name: "Signup_Screen");
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    _loadDistricts();
    resetTimer();
  }

  @override
  void dispose() {
    // Clean up the controller when the Widget is disposed
    if (isListening) {
      AndroidSmsRetriever.stopSmsListener();
    }
    nameController.dispose();
    otpController.dispose();
    timer?.cancel();
    super.dispose();
  }

  void resetTimer() {
    resendDuration = const Duration(seconds: 30);
    timer = Timer.periodic(const Duration(seconds: 1), (Timer t) {
      setState(() {
        resendDuration = Duration(seconds: resendDuration.inSeconds - 1);
      });

      if (resendDuration.inSeconds <= 0) {
        timer?.cancel();
        setState(() => _showOtpResend = true);
      }
    });
  }

  Future<void> resendOtp() async {
    AppAnalytics.logEvent(name: "Clicked_OTP_resend");

    try {
      setState(() => _showOtpResend = false);

      resetTimer();

      final response = await _authService.requestOTP(phone: widget.phone);
      setState(() {
        otpController.text = response.getOtp();
      });
    } catch (e) {
      return Utils.showToast(localisedErrorMessage(e));
    }
  }

  Future<void> _loadDistricts() async {
    try {
      AppAnalytics.timeEvent(name: "fetched_districts");
      final fetchedDistricts = await _authService.getAllDistricts();

      if (mounted) {
        setState(() {
          districts = fetchedDistricts;
          if (fetchedDistricts.length == 1) {
            district = fetchedDistricts[0];
          }
          AppAnalytics.logEvent(name: "fetched_districts");
          handleCircleList(districts);
          _loading = false;
        });
      }
    } catch (e) {
      Utils.showToast(localisedErrorMessage(e));
    }
  }

  Future<void> _loadMandals(int districtId) async {
    try {
      AppAnalytics.timeEvent(name: "fetched_mandals");
      setState(() {
        mandalsLoading = true;
      });

      final fetchedMandals = await _authService.getDistrictMandals(districtId);

      if (mounted) {
        setState(() {
          mandals = fetchedMandals;
          if (fetchedMandals.length == 1) {
            mandal = fetchedMandals[0];
          }
          AppAnalytics.logEvent(name: "fetched_mandals");
          handleCircleList(mandals);
          mandalsLoading = false;
          mandalsLoaded = true;
          showMandalSheet();
        });
      }
    } catch (e) {
      Utils.showToast(localisedErrorMessage(e));
    }
  }

  Future<void> _loadVillages(int mandalId) async {
    try {
      AppAnalytics.timeEvent(name: "fetched_villages");
      setState(() {
        villagesLoading = true;
      });
      final fetchedVillages = await _authService.getMandalVillages(mandalId);
      if (mounted) {
        setState(() {
          villages = fetchedVillages;
          if (fetchedVillages.length == 1) {
            village = fetchedVillages[0];
          }
          AppAnalytics.logEvent(name: "fetched_villages");
          handleCircleList(villages);
          villagesLoading = false;
          villagesLoaded = true;
          showVillageSheet();
        });
      }
    } catch (e) {
      Utils.showToast(localisedErrorMessage(e));
    }
  }

  String _getErrorMessage(
      {required String nameText,
      required String otpText,
      required String districtText,
      required String mandalText,
      required String villageText}) {
    if (nameController.text.isEmpty) {
      return context.getString(StringKey.nameNotEmptyWarnText, listen: false);
    } else if (!_isTruecaller && otpController.text.isEmpty) {
      return context.getString(StringKey.otpNotEmptyWarnText, listen: false);
    } else if ((!_isTruecaller &&
        int.parse(otpController.text) > 0 &&
        otpController.text.length != _codeLength &&
        otpController.text.length != _codeLengthForInternalAccounts)) {
      return context.getString(StringKey.otpNotValidWarnText, listen: false);
    } else if (district == null) {
      return context.getString(StringKey.selectDistrictText, listen: false);
    } else if (mandal == null) {
      return context.getString(StringKey.selectMandalText, listen: false);
    } else if (village == null) {
      return context.getString(StringKey.selectVillageText, listen: false);
    } else {
      return "";
    }
  }

  Future<void> _signUpRequest() async {
    var errorMsg = _getErrorMessage(
      nameText: nameController.text,
      otpText: otpController.text,
      districtText: district?.name ?? "",
      mandalText: mandal?.name ?? "",
      villageText: village?.name ?? "",
    );

    if (errorMsg.isNotEmpty) {
      return Utils.showToast(errorMsg);
    }

    if (!isOtpAutoFilled) {
      AppAnalytics.logEvent(name: "clicked_otp_manually");
    }
    AppAnalytics.logEvent(name: "Clicked_Join");

    try {
      if (mounted) {
        setState(() => _loading = true);
      }

      final responseUser = await _authService.sendSignupRequest(
          phone: widget.phone,
          otp: otpController.text,
          name: nameController.text,
          village: village!,
          truecallerUserProfile: widget.truecallerUserProfile);

      if (mounted) {
        setState(() => _loading = false);
      }

      AppConstants appConstants = GetIt.I.get<AppConstants>();
      appConstants.showPartySuggestions = responseUser.showPartySuggestions;
      appConstants.skipPartyMemberDecision =
          responseUser.skipPartyMemberDecision;
      appConstants.exclusivePartyJoin = responseUser.exclusivePartyJoin;

      if (mounted) {
        Navigator.of(context).pop();
        widget.onSignUp(responseUser);
      } else {
        Utils.showToast(
          "ప్రస్తుతం సైన్ అప్ సాధ్యం కాలేదు. కాసేపాగి ప్రయత్నించండి", // Unable to signup. Please try later
        );
        if (!mounted) return;
        Navigator.of(context).popAndPushNamed(LoginPage.tag);
      }
    } catch (e) {
      if (mounted) {
        setState(() => _loading = false);
      }
      return Utils.showToast(localisedErrorMessage(e));
    }
  }

  void onCodeReceived(String value) {
    if (mounted && widget.otp != value && value != "") {
      otpController.text = value;

      if (int.parse(value) > 0 && value.length == _codeLength) {
        _signUpRequest();
      }
    }
  }

  void _showTermsOfUse() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (BuildContext context) => ExternalWebView(
          title: "Terms of Use",
          url: "${Endpoint.getRorApiBaseUrl()}/terms-of-use",
        ),
      ),
    );
  }

  handleCircleList(List<Circle> circlesList) {
    for (var element in circlesList) {
      String tag = element.nameEN.substring(0, 1).toUpperCase();
      if (RegExp('[A-Z]').hasMatch(tag)) {
        element.tagIndex = tag;
        if (element.nameEN.toUpperCase() == "OTHER") {
          // ignore: avoid_hardcoded_strings_in_ui
          element.tagIndex = '#';
        }
      } else {
        // ignore: avoid_hardcoded_strings_in_ui
        element.tagIndex = '#';
      }
    }
    SuspensionUtil.sortListBySuspensionTag(circlesList);

    SuspensionUtil.setShowSuspensionStatus(circlesList);
  }

  showBottomSheet(Function(Circle) onCircleSelect, List<Circle> circles,
      String header) async {
    final selectedCircle = await showModalBottomSheet<Circle>(
        enableDrag: false,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(10.0), topRight: Radius.circular(10.0)),
        ),
        isScrollControlled: true,
        context: context,
        builder: (context) {
          return Wrap(
            children: [
              Padding(
                padding: const EdgeInsets.only(
                    top: 20, left: 10, bottom: 10, right: 10),
                child: Center(
                  child: Text(
                    header,
                    style: const TextStyle(fontSize: 23),
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.only(top: 0, left: 10, right: 10),
                constraints: BoxConstraints(
                    minHeight: MediaQuery.of(context).size.height * 0.7,
                    maxHeight: MediaQuery.of(context).size.height * 0.7,
                    minWidth: MediaQuery.of(context).size.width),
                child: AzListView(
                  data: circles,
                  itemCount: circles.length,
                  susItemBuilder: (context, index) {
                    return Align(
                      alignment: Alignment.centerLeft,
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          color: Colors.blue[50],
                        ),
                        width: MediaQuery.of(context).size.width * 0.85,
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(circles[index].tagIndex ?? ""),
                        ),
                      ),
                    );
                  },
                  itemBuilder: (context, index) {
                    return InkWell(
                      onTap: () {
                        Navigator.of(context).pop(circles[index]);
                      },
                      child: Center(
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                vertical: 15, horizontal: 20),
                            child: Text(
                              circles[index].name +
                                  (circles[index].nameEN.isNotEmpty
                                      // ignore: avoid_hardcoded_strings_in_ui
                                      ? " (${circles[index].nameEN})"
                                      : ""),
                              style: const TextStyle(fontSize: 16),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        });
    if (selectedCircle != null) {
      onCircleSelect(selectedCircle);
    }
  }

  showMandalSheet() async {
    AppAnalytics.logEvent(name: "shown_mandal_popup");
    await showBottomSheet((selectedMandal) {
      setState(() {
        mandal = selectedMandal;
        villages = [];
        village = null;
        villagesLoaded = false;
      });

      _loadVillages(selectedMandal.id);
    }, mandals, context.getString(StringKey.selectMandalTitle, listen: false));
  }

  showVillageSheet() async {
    AppAnalytics.logEvent(name: "shown_village_popup");
    await showBottomSheet((selectedVillage) {
      setState(() {
        village = selectedVillage;
      });
    }, villages,
        context.getString(StringKey.selectVillageTitle, listen: false));
  }

  @override
  Widget build(BuildContext context) {
    final Circle? dist = district;
    final Circle? mand = mandal;
    final Circle? vill = village;

    if (_loading) {
      return Widgets.renderCircularLoader(context);
    }

    Widget districtDropDown;
    Widget mandalDropDown;
    Widget villageDropDown;

    if (autoDetected) {
      districtDropDown = TextFormField(
        initialValue: dist?.name ?? "",
        enabled: false,
        autofocus: false,
        keyboardType: TextInputType.text,
        decoration: InputDecoration(
          hintText: "District",
          contentPadding: const EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(5),
          ),
        ),
      );

      mandalDropDown = TextFormField(
        initialValue: mand?.name ?? "",
        enabled: false,
        autofocus: false,
        keyboardType: TextInputType.text,
        decoration: InputDecoration(
          hintText: "Mandal",
          contentPadding: const EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(5),
          ),
        ),
      );

      villageDropDown = TextFormField(
        initialValue: vill?.name ?? "",
        enabled: false,
        autofocus: false,
        keyboardType: TextInputType.text,
        decoration: InputDecoration(
          hintText: "Village",
          contentPadding: const EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(5),
          ),
        ),
      );
    } else {
      districtDropDown = InkWell(
          onTap: () async {
            AppAnalytics.logEvent(name: "Clicked_district_bar");
            await showBottomSheet((selectedDistrict) {
              setState(() {
                district = selectedDistrict;
                mandals = [];
                mandal = null;
                villages = [];
                village = null;
                villagesLoaded = false;
                mandalsLoaded = false;
              });

              _loadMandals(selectedDistrict.id);
            },
                districts,
                context.getString(StringKey.selectDistrictTitle,
                    listen: false));
          },
          child: Container(
              height: 45,
              padding: const EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
              decoration: BoxDecoration(
                  border: Border.all(color: Colors.black87, width: 0.5),
                  borderRadius: BorderRadius.circular(5)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  district == null
                      ? Text(
                          "జిల్లా (District)",
                          style:
                              TextStyle(color: Colors.grey[700], fontSize: 16),
                        )
                      : Flexible(
                          child: dist != null
                              ? Text(
                                  dist.name +
                                      (dist.nameEN.isNotEmpty
                                          // ignore: avoid_hardcoded_strings_in_ui
                                          ? " (${dist.nameEN})"
                                          : ""),
                                  style: const TextStyle(fontSize: 16),
                                )
                              : const Text(""),
                        ),
                  const Icon(Icons.arrow_drop_down)
                ],
              )));

      mandalDropDown = InkWell(
          onTap: !mandalsLoaded
              ? null
              : () async {
                  AppAnalytics.logEvent(name: "Clicked_mandal_bar");
                  showMandalSheet();
                },
          child: Container(
              height: 45,
              padding: const EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
              decoration: BoxDecoration(
                  border: Border.all(color: Colors.black87, width: 0.5),
                  borderRadius: BorderRadius.circular(5)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  mandal == null
                      ? Text(
                          "మండలం (Mandal)",
                          style:
                              TextStyle(color: Colors.grey[700], fontSize: 16),
                        )
                      : Flexible(
                          child: mand != null
                              ? Text(
                                  mand.name +
                                      (mand.nameEN.isNotEmpty
                                          // ignore: avoid_hardcoded_strings_in_ui
                                          ? " (${mand.nameEN})"
                                          : ""),
                                  style: const TextStyle(fontSize: 16),
                                )
                              : const Text(""),
                        ),
                  mandalsLoading
                      ? Widgets.buttonLoader(color: Colors.blue)
                      : Icon(
                          Icons.arrow_drop_down,
                          color:
                              mandalsLoaded ? Colors.black : Colors.grey[400],
                        )
                ],
              )));

      villageDropDown = InkWell(
          onTap: !villagesLoaded
              ? null
              : () async {
                  AppAnalytics.logEvent(name: "Clicked_village_bar");
                  showVillageSheet();
                },
          child: Container(
              height: 45,
              padding: const EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
              decoration: BoxDecoration(
                  border: Border.all(color: Colors.black87, width: 0.5),
                  borderRadius: BorderRadius.circular(5)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  village == null
                      ? Text(
                          "గ్రామ పంచాయతి (Village)",
                          style:
                              TextStyle(color: Colors.grey[700], fontSize: 16),
                        )
                      : Flexible(
                          child: vill != null
                              ? Text(
                                  vill.name +
                                      (vill.nameEN.isNotEmpty
                                          // ignore: avoid_hardcoded_strings_in_ui
                                          ? " (${vill.nameEN})"
                                          : ""),
                                  style: const TextStyle(fontSize: 16),
                                )
                              : const Text(""),
                        ),
                  villagesLoading
                      ? Widgets.buttonLoader(color: Colors.blue)
                      : Icon(
                          Icons.arrow_drop_down,
                          color:
                              villagesLoaded ? Colors.black : Colors.grey[400],
                        )
                ],
              )));
    }

    final name = TextFormField(
      onTap: () {
        AppAnalytics.logEvent(name: "Clicked_name");
      },
      controller: nameController,
      autofocus: true,
      keyboardType: TextInputType.text,
      decoration: InputDecoration(
        hintText: "పేరు (Name)",
        contentPadding: const EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
        ),
      ),
    );

    final otpField = TextFormField(
      controller: otpController,
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'\d')),
        LengthLimitingTextInputFormatter(6)
      ],
      decoration: InputDecoration(
        hintText: "OTP",
        contentPadding: const EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
        ),
      ),
    );

    final submitButton = ButtonTheme(
      minWidth: double.infinity,
      child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.fromLTRB(20.0, 10.0, 20.0, 10.0),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(5),
            ),
          ),
          onPressed: !_loading ? _signUpRequest : () {},
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Text(
                context.getString(StringKey.joinLabel),
                style: const TextStyle(color: Colors.white),
              ),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(Radius.circular(20)),
                  color: Theme.of(context).primaryColorDark,
                ),
                child: _loading
                    ? Widgets.buttonLoader(size: 14)
                    : const Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.white,
                        size: 14,
                      ),
              )
            ],
          )),
    );

    final resendButton = ElevatedButton(
      style: ElevatedButton.styleFrom(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5),
        ),
        padding: const EdgeInsets.all(12),
        backgroundColor: Styles.resendGrey,
      ),
      onPressed: _showOtpResend ? resendOtp : null,
      child: Text(
        // ignore: avoid_hardcoded_strings_in_ui
        '${context.getString(StringKey.otpResendText)}${resendDuration.inMilliseconds > 0 ? ' (${Utils.durationToStringOnlyMinutesSeconds(resendDuration)})' : ''}',
        style: Theme.of(context)
            .textTheme
            .titleMedium
            ?.copyWith(color: Theme.of(context).primaryColor),
      ),
    );

    final termsText = RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        text: context.getString(StringKey.signUpInfoText),
        style: Theme.of(context).textTheme.bodyMedium,
        children: [
          TextSpan(
            text: context.getString(StringKey.conditionsLabel),
            style: const TextStyle(color: Colors.blueAccent),
            recognizer: (TapGestureRecognizer()..onTap = _showTermsOfUse),
          ),
          // ignore: avoid_hardcoded_strings_in_ui
          const TextSpan(text: " & "),
          TextSpan(
            text: context.getString(StringKey.privacyLabel),
            style: const TextStyle(color: Colors.blueAccent),
            recognizer: (TapGestureRecognizer()
              ..onTap = () => Utils.showPrivacyPolicy(context)),
          ),
          TextSpan(text: context.getString(StringKey.signUpInfoSuffixText)),
        ],
      ),
    );

    return Scaffold(
      backgroundColor: Colors.white,
      body: Builder(
        // Create an inner BuildContext so that the onPressed methods
        // can refer to the Scaffold with Scaffold.of().
        builder: (BuildContext context) {
          return SafeArea(
            child: Container(
              padding: const EdgeInsets.only(top: 20),
              child: Center(
                child: ListView(
                  physics: const BouncingScrollPhysics(),
                  shrinkWrap: true,
                  padding: const EdgeInsets.only(left: 24.0, right: 24.0),
                  children: <Widget>[
                    name,
                    const SizedBox(height: 10.0),
                    !_isTruecaller ? otpField : Container(),
                    !_isTruecaller ? const SizedBox(height: 10.0) : Container(),
                    districtDropDown,
                    const SizedBox(height: 10.0),
                    mandalDropDown,
                    const SizedBox(height: 10.0),
                    villageDropDown,
                    const SizedBox(height: 10.0),
                    autoDetected
                        ? InkWell(
                            child: const Text(
                              "Manually select location",
                              style: TextStyle(color: Colors.blueAccent),
                            ),
                            onTap: () {
                              setState(() {
                                autoDetected = false;
                                district = null;
                                mandal = null;
                                village = null;
                              });
                              _loadDistricts();
                            },
                          )
                        : Container(),
                    autoDetected ? const SizedBox(height: 30.0) : Container(),
                    submitButton,
                    const SizedBox(height: 10.0),
                    !_isTruecaller ? resendButton : Container(),
                    !_isTruecaller ? const SizedBox(height: 20.0) : Container(),
                    termsText,
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
