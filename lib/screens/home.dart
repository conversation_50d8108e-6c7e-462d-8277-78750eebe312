import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart' hide Badge;
import 'package:flutter/rendering.dart';
import 'package:flutter_vibrate/flutter_vibrate.dart';
import 'package:get_it/get_it.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/common/app_event_bus.dart';
import 'package:praja/common/news_feed_tabs_view_model.dart';
import 'package:praja/constants/AppConstants.dart';
import 'package:praja/core/ui/page.dart';
import 'package:praja/core/ui/page_view_reporter.dart';
import 'package:praja/enums/circle_section.dart';
import 'package:praja/enums/news_feed_type.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/app_version_update/app_version_updater.dart';
import 'package:praja/features/direct_messaging/presentation/chat_list_page/chat_list_page.dart';
import 'package:praja/features/direct_messaging/presentation/chat_page/chat_page.dart';
import 'package:praja/features/direct_messaging/presentation/chat_target_picker.dart';
import 'package:praja/features/localization/vertical_adjustment_padding.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/notifications/service/notification.dart';
import 'package:praja/features/posters/posters_feed_view/posters_feed_view_page.dart';
import 'package:praja/features/posters/services/poster_creative_config.dart';
import 'package:praja/features/premium_experience/premium_utils.dart';
import 'package:praja/features/profile/widgets/navigation_drawer.dart';
import 'package:praja/features/user/models/app_user.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/user_identity.dart';
import 'package:praja/presentation/app_user_avatar.dart';
import 'package:praja/presentation/lazy_load.dart';
import 'package:praja/presentation/logged_in_user_view_model.dart';
import 'package:praja/presentation/nav_bar.dart';
import 'package:praja/presentation/nav_bar_view_model.dart';
import 'package:praja/presentation/praja_icons.dart';
import 'package:praja/screens/circles/sections.dart';
import 'package:praja/screens/common_search.dart';
import 'package:praja/screens/create/createPage/presentation/create_page.dart';
import 'package:praja/screens/notifications/list.dart';
import 'package:praja/screens/posts/detail.dart';
import 'package:praja/screens/posts/news_feed_sections.dart';
import 'package:praja/services/app_initializer.dart';
import 'package:praja/services/app_state.dart';
import 'package:praja/services/navigation.dart';
import 'package:praja/services/session_store.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/utils/utils.dart';
import 'package:praja/utils/widgets/page_transition_widget.dart';
import 'package:praja_posters/praja_posters.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HomePage extends BasePage {
  final VoidCallback onSignOut;

  @override
  String get pageName => 'home';

  @override
  bool get pageLoadRequiredForTracking => true;

  const HomePage({required this.onSignOut, super.key});

  @override
  Widget buildContent(BuildContext context) {
    return HomePageInner(
      onSignOut: onSignOut,
    );
  }
}

class HomePageInner extends StatefulWidget {
  final VoidCallback onSignOut;

  const HomePageInner({required this.onSignOut, super.key});

  @override
  State<HomePageInner> createState() => _HomePageState();
}

class _HomePageState extends State<HomePageInner>
    with TickerProviderStateMixin, RouteAware {
  late NavViewModel navViewModel;
  late TabController _tabController;
  late LiveDataObserver _tabChangeObserver;
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  bool isShareClicked = false;
  bool contactScreenShown = false;

  final NotificationService _notificationService =
      GetIt.I.get<NotificationService>();

  late TabController _newsFeedSectionsTabController;

  late AnimationController _appBarAnimationController;
  late Animation<double> _appBarAnimation;
  StreamSubscription<void>? _feedScrollChangeSubscription;
  final StreamController<void> _feedScrollChangeController =
      StreamController.broadcast();

  AssetImage uploadProfilePicImage =
      const AssetImage("assets/images/badge_congo/upload_pic.png");
  AssetImage appShareIcon =
      const AssetImage("assets/images/icons/poster-app-logo.png");

  bool expandedFloatingButton = true;

  static GlobalKey<NotificationsListState> notificationKey =
      GlobalKey<NotificationsListState>();
  static GlobalKey<ChatListTabState> chatListKey =
      GlobalKey<ChatListTabState>();

  final List<NavItemName> _ignoreAppBarShadowNavItems = const [
    NavItemName.chat,
    NavItemName.circles,
  ];

  _HomePageState();

  final _scrollControllers = <NavItemName, ScrollController>{};

  CircleSection circleSection = CircleSection.myCircles;
  late PageViewTracker _pageViewTracker;
  final SessionStore _sessionStore = GetIt.I.get<SessionStore>();

  final AppEventBus _feedEventBus = GetIt.I.get<AppEventBus>();
  StreamSubscription<AppOpenState>? _appOpenStateSubscription;
  StreamSubscription<AppEvent>? _feedEventSubscription;

  late NewsFeedTabsViewModel _newsFeedTabsViewModel;

  @override
  void initState() {
    super.initState();
    _pageViewTracker = Provider.of<PageViewTracker>(context, listen: false);
    navViewModel = context.globalNavViewModel;
    _tabController = TabController(
      vsync: this,
      length: navViewModel.navItems.value.length,
      initialIndex: navViewModel.selectedIndex.value,
    );
    _newsFeedTabsViewModel = context.getViewModel<NewsFeedTabsViewModel>();
    _newsFeedSectionsTabController = TabController(
      vsync: this,
      length: 2,
    );
    _appBarAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _appBarAnimation =
        Tween<double>(begin: 0.0, end: kToolbarHeight).animate(CurvedAnimation(
      parent: _appBarAnimationController,
      curve: Curves.easeInOut,
    ));

    _sessionStore.addListener(_onSessionChange);

    _listenToFeedScrollStream();

    _tabChangeObserver = (newSelectedIndex) {
      if (newSelectedIndex != _tabController.index) {
        if (newSelectedIndex == 0) {
          _listenToFeedScrollStream();
          _animationReverse();
        } else {
          _feedScrollChangeSubscription?.cancel();
          _animationReverse();
        }
        _tabController.animateTo(newSelectedIndex);
        _sendTabToAnalytics(navViewModel.navItems.value[newSelectedIndex]);
      }
    };
    navViewModel.selectedIndex.observe(_tabChangeObserver);
    navViewModel.unreadNotificationsCount
        .observe(_onUnreadNotificationCountChanged);
    navViewModel.unreadChatsState.observe(_onUnreadChatsStateChanged);

    _feedEventSubscription = _feedEventBus.stream.listen((event) {
      if (event is CreatePostSavedEvent) {
        _onCreatePostSaved();
      } else if (event is PostDeeplinkEvent) {
        _onPostDeeplink(event);
      } else if (event is FeedDeeplinkEvent) {
        _shouldScrollFeed = true;
        _feedScrollEvent = event;
        _onFeedScrollEvent(event);
      }
    });

    _newsFeedSectionsTabController.addListener(_newsFeedTabListener);
    _appOpenStateSubscription =
        AppState.appOpenStateStream.listen(_onAppOpenStateChange);
    _initialize(GetIt.I.get<AppInitializer>().initialData!);
  }

  void _newsFeedTabListener() {
    final tabIndex = _newsFeedTabsViewModel.selectedTabIndex.value;
    if (tabIndex == _newsFeedSectionsTabController.index) {
      return;
    }
    _newsFeedTabsViewModel.onTabChanged(_newsFeedSectionsTabController.index);
    _animationReverse();
  }

  void _listenToFeedScrollStream() {
    _feedScrollChangeSubscription = _feedScrollChangeController.stream
        .throttleTime(const Duration(milliseconds: 200), trailing: true)
        .listen((_) {
      _updateAppBarAnimation();
    });
  }

  void _animationReverse() {
    _appBarAnimationController.reverse();
    setState(() {
      expandedFloatingButton = true;
    });
  }

  void _animationForward() {
    _appBarAnimationController.forward();
    setState(() {
      expandedFloatingButton = false;
    });
  }

  void _onScrollChange() {
    _feedScrollChangeController.add(null);
  }

  void _onSessionChange() {
    if (_newsFeedTabsViewModel.feeds[0].type == NewsFeedTypeEnum.myFeed) {
      navViewModel.updateTrendsUnreadDot(true);
    }
  }

  void _updateAppBarAnimation() {
    final currentNewsFeedTabControllerIndex =
        _newsFeedSectionsTabController.index;
    final feedScrollController = _newsFeedTabsViewModel
        .scrollControllers[currentNewsFeedTabControllerIndex];
    if (!feedScrollController.hasClients) {
      return;
    }

    for (var position in feedScrollController.positions) {
      if (position.pixels >= kToolbarHeight &&
          position.userScrollDirection == ScrollDirection.reverse) {
        _animationForward();
      } else if (position.userScrollDirection == ScrollDirection.forward) {
        _animationReverse();
      }
    }
  }

  ScrollController? _getScrollController(NavItemName name) {
    if (name == NavItemName.circles) {
      return null;
    }
    if (!_scrollControllers.containsKey(name)) {
      _scrollControllers[name] = ScrollController();
    }
    return _scrollControllers[name]!;
  }

  void _viewAllSuggested() async {
    AppAnalytics.logEvent(name: "view_all_suggested_circles");
    GetIt.I.get<AppConstants>().defaultCircleSection = CircleSection.suggested;
    navViewModel.switchTo(NavItemName.circles);
  }

  cachingAppAssets() async {
    if (!mounted) return;
    final List<AssetImage> imagesToCache = [
      uploadProfilePicImage,
      appShareIcon
    ];

    for (var image in imagesToCache) {
      await precacheImage(image, context);
    }
  }

  Widget _getTabWidget(NavItemName name) {
    switch (name) {
      case NavItemName.trends:
        return NewsFeedSections(
          viewAllSuggested: _viewAllSuggested,
          padding: const EdgeInsets.only(
              bottom: navBarHeight, top: 2 * kToolbarHeight),
          tabController: _newsFeedSectionsTabController,
        );
      case NavItemName.posters:
        return const PostersFeedViewPage(
          padding: EdgeInsets.only(bottom: navBarHeight),
          source: "home",
        );
      case NavItemName.circles:
        return CircleSections(
          padding: const EdgeInsets.only(
            bottom: navBarHeight,
            top: kToolbarHeight,
          ),
          scrollController: _getScrollController(name),
        );
      case NavItemName.notifications:
        return NotificationsList(
          key: notificationKey,
          controller: _getScrollController(name),
          padding:
              const EdgeInsets.only(bottom: navBarHeight, top: kToolbarHeight),
        );
      case NavItemName.chat:
        return LazyLoadWidget(
            child: ChatListTab(
          key: chatListKey,
          scrollController: _getScrollController(name),
          padding:
              const EdgeInsets.only(bottom: navBarHeight, top: kToolbarHeight),
        ));
    }
  }

  List<Widget> _tabList = [];

  List<Widget> _getTabWidgetList(List<NavItem> navItems) {
    if (_tabList.isEmpty || _tabList.length != navItems.length) {
      _tabList =
          navItems.map((navItem) => _getTabWidget(navItem.name)).toList();
    }
    return _tabList;
  }

  void _loadGoogleFonts() async {
    final config = GetIt.I.get<PosterCreativeConfig>();
    final List<PosterFontConfig> preloadFontsConfig = config.preloadFontsConfig;
    for (var fontConfig in preloadFontsConfig) {
      GoogleFonts.getFont(
        fontConfig.fontFamily,
        textStyle: TextStyle(
          fontWeight:
              fontConfig.toFontWeight(fontWeight: fontConfig.fontWeight),
          fontStyle: fontConfig.toFontStyle(fontStyle: fontConfig.fontStyle),
        ),
      );
    }

    try {
      await GoogleFonts.pendingFonts();
    } catch (e, stackTrace) {
      logNonFatal("Error While Fetching Font", e, stackTrace: stackTrace);
    }
  }

  void _initialize(Map<String, dynamic> initialData) {
    _newsFeedTabsViewModel.onFeedsReceived(initialData['feeds'] ?? []);
    _updateNewsFeedTabController();
    _onTabsInitialized();
    Utils.setUserPermissions(initialData['permissions']);
    Utils.setCanCreateCircle(initialData['can_create_circle']);
    setAppConstants(initialData);

    SharedPreferences.getInstance().then((prefs) async {
      final int showNextAt =
          prefs.getInt('user.invite_prompt_show_next_at') ?? 0;

      if (showNextAt == 0) {
        int currentTime = DateTime.now().millisecondsSinceEpoch;

        // Show the user profile invite prompt after 1 day of opening the app
        prefs.setInt(
          'user.invite_prompt_show_next_at',
          currentTime + (24 * 60 * 60 * 1000),
        );
      }
    });
    if (_newsFeedTabsViewModel.feeds[0].type == NewsFeedTypeEnum.myFeed) {
      final navigationService = GetIt.I.get<NavigationService>();
      navViewModel.updateTrendsUnreadDot(navigationService.canPop() ||
          navViewModel.currentNavItemName != NavItemName.trends);
    }
    _updatePageParams();

    _loadGoogleFonts();
  }

  void _updateNewsFeedTabController() {
    if (!mounted) return;
    _newsFeedSectionsTabController.dispose();
    setState(() {
      _newsFeedSectionsTabController = TabController(
        vsync: this,
        length: _newsFeedTabsViewModel.feeds.length,
      );
    });
    _newsFeedSectionsTabController.addListener(_newsFeedTabListener);
    for (var i = 0; i < _newsFeedTabsViewModel.scrollControllers.length; i++) {
      _newsFeedTabsViewModel.scrollControllers[i].addListener(_onScrollChange);
    }
  }

  setAppConstants(Map data) {
    AppConstants appConstants = GetIt.I.get<AppConstants>();
    appConstants.seenPostIds = {};
    appConstants.showDeeplinkPostInFeed =
        data['show_deeplink_post_in_feed'] ?? true;
    appConstants.trendTutorial = data['enable_trend_tutorial'] ?? true;
    appConstants.trendFeedBack = data['enable_trend_feedback'] ?? false;
    appConstants.trendTutorialTriggerPosts =
        data['trend_tutorial_trigger_posts'] ?? 5;
    appConstants.trendTutorialTimeInSeconds =
        data['trend_tutorial_time_in_seconds'] ?? 4;
    appConstants.trendTutorialText = data['trend_tutorial_text'] ??
        context.getString(StringKey.trendTutorialDefaultText);
    appConstants.trendFeedbackText =
        data['trend_feedback_text'] ?? "మీరు పోస్ట్‌ను ట్రెండ్ చేసారు.";
    appConstants.videoAutoPlay = data['video_auto_play'] ?? true;
    appConstants.videoLoopAfterCompletion =
        data['video_loop_after_completion'] ?? true;
  }

  bool _updateAppOnClose = false;
  void _onAppOpenStateChange(AppOpenState event) {
    if (event == AppOpenState.closed && _updateAppOnClose) {
      AppVersionUpdater.completeUpdate();
      _updateAppOnClose = false;
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final route = ModalRoute.of(context);
    if (route != null) {
      PageViewReporter.routeObserver.subscribe(this, route);
    }
  }

  void _removeNewsFeedTabScrollListeners() {
    for (var i = 0; i < _newsFeedTabsViewModel.scrollControllers.length; i++) {
      _newsFeedTabsViewModel.scrollControllers[i]
          .removeListener(_onScrollChange);
    }
  }

  @override
  void dispose() {
    _feedScrollChangeSubscription?.cancel();
    _removeNewsFeedTabScrollListeners();
    _feedScrollChangeController.close();
    _appBarAnimationController.dispose();
    _newsFeedSectionsTabController.removeListener(_newsFeedTabListener);
    _newsFeedSectionsTabController.dispose();
    _tabController.dispose();
    navViewModel.selectedIndex.removeObserver(_tabChangeObserver);
    navViewModel.unreadNotificationsCount
        .removeObserver(_onUnreadNotificationCountChanged);
    navViewModel.unreadChatsState.removeObserver(_onUnreadChatsStateChanged);
    _sessionStore.removeListener(_onSessionChange);
    _appOpenStateSubscription?.cancel();
    _feedEventSubscription?.cancel();
    PageViewReporter.routeObserver.unsubscribe(this);
    for (var element in _scrollControllers.values) {
      element.dispose();
    }
    super.dispose();
  }

  @override
  void didPush() {
    _sendCurrentTabToAnalytics();
  }

  @override
  void didPopNext() {
    _animationReverse();
    _sendCurrentTabToAnalytics();
    final navItems = navViewModel.navItems.value;
    if (navItems.isNotEmpty &&
        navViewModel.selectedNavItemName() == NavItemName.trends) {
      navViewModel.updateTrendsUnreadDot(false);
    }
  }

  // listener for nav bar's unread notifications count, unread chats count
  void _onUnreadNotificationCountChanged(int _) {
    _updatePageParams();
  }

  void _onUnreadChatsStateChanged(UnreadChatsState _) {
    _updatePageParams();
  }

  void _updatePageParams() {
    _pageViewTracker.onPageLoaded({
      'unread_notifications_count': navViewModel.unreadNotificationsCount.value,
      'unread_chats_count':
          navViewModel.unreadChatsState.value.primaryUnreadCount,
      'unread_other_chats_present':
          navViewModel.unreadChatsState.value.secondaryUnreadChatsPresent,
    });
  }

  void _sendCurrentTabToAnalytics() {
    final selectedIndex = navViewModel.selectedIndex.value;
    final navItems = navViewModel.navItems.value;
    if (navItems.isEmpty ||
        navItems.length <= selectedIndex ||
        selectedIndex < 0) {
      // we haven't loaded yet
      return;
    }
    _sendTabToAnalytics(navItems[selectedIndex]);
  }

  void _sendTabToAnalytics(NavItem currentTab) {
    AppAnalytics.logTabViewed(currentTab.name.name, {
      'unread_notifications_count': navViewModel.unreadNotificationsCount.value,
      'unread_chats_count':
          navViewModel.unreadChatsState.value.primaryUnreadCount,
      'unread_other_chats_present':
          navViewModel.unreadChatsState.value.secondaryUnreadChatsPresent,
      'page_name': 'home',
    });
  }

  Future<void> _markAllNotificationsRead({bool refresh = true}) async {
    try {
      if (notificationKey.currentState != null) {
        await _notificationService.markAllAsRead();
        if (refresh) {
          notificationKey.currentState?.refreshNotifications();
        }
      }
    } catch (e) {
      Utils.showToast(localisedErrorMessage(e));
    }
  }

  void _onCreatePostSaved() async {
    Navigator.of(context).popUntil((route) => route.isFirst);

    final int myFeedIndex = _newsFeedTabsViewModel.getMyFeedIndex();
    if (myFeedIndex == -1) return;

    navViewModel.switchTo(NavItemName.trends);
    _newsFeedSectionsTabController.animateTo(myFeedIndex);
    final scrollController =
        _newsFeedTabsViewModel.scrollControllers[myFeedIndex];
    if (scrollController.hasClients) {
      scrollController.animateTo(0,
          duration: const Duration(milliseconds: 500), curve: Curves.ease);
    }
  }

  ///To Store The Event When App Open Through Post Deeplink
  PostDeeplinkEvent? _postDeeplinkEvent;

  /// To Store The Event When App Open Through Feeds Deeplink
  FeedDeeplinkEvent? _feedScrollEvent;

  /// This Method Will Trigger Once Feed Experiment User Initialized
  void _onTabsInitialized() {
    onTabsInitialized = true;
    _onPostDeeplinkEvent(_postDeeplinkEvent);
    _onFeedScrollEvent(_feedScrollEvent);
  }

  /// This Method Will Trigger Once App Open Through Post Deeplink
  void _onPostDeeplink(PostDeeplinkEvent event) {
    shouldShowPostDeeplink = true;
    _postDeeplinkEvent = event;
    _onPostDeeplinkEvent(_postDeeplinkEvent);
  }

  bool _shouldScrollFeed = false;

  /// This Method Will Trigger Once App Open Through Feeds Deeplink
  void _onFeedScrollEvent(FeedDeeplinkEvent? event) {
    final feedEvent = event;
    if (!onTabsInitialized || !_shouldScrollFeed || feedEvent == null) return;
    final index = _newsFeedTabsViewModel.indexOf(feedEvent.feedId);
    if (index == -1) {
      AppAnalytics.logEvent(name: "failed_feed_deeplink", parameters: {
        "feed_id": feedEvent.feedId,
      });
      return;
    }
    _newsFeedSectionsTabController.animateTo(index);
    _shouldScrollFeed = false;
  }

  bool onTabsInitialized = false;
  bool shouldShowPostDeeplink = false;

  /// To Handle Post Deeplink Event
  /// If User is Feed Exp User, Then We will show the post on top of the feed
  /// Else We will navigate to Post Detail Page
  void _onPostDeeplinkEvent(PostDeeplinkEvent? event) {
    final postDeeplinkEvent = event;
    if (postDeeplinkEvent == null ||
        !onTabsInitialized ||
        !shouldShowPostDeeplink) {
      return;
    }
    if (_newsFeedTabsViewModel.firstFeedType == NewsFeedTypeEnum.myFeed &&
        GetIt.I.get<AppConstants>().showDeeplinkPostInFeed &&
        event?.postId != null) {
      _appBarAnimationController.reverse();
      _newsFeedSectionsTabController.animateTo(0);
      final scrollController = _newsFeedTabsViewModel
          .scrollControllers[_newsFeedSectionsTabController.index];
      if (scrollController.hasClients) {
        scrollController.animateTo(0,
            duration: const Duration(milliseconds: 500), curve: Curves.ease);
      }
      _feedEventBus.fire(MyFeedInvalidatedEvent());
    } else {
      GetIt.I.get<AppConstants>().postIdFromDeepLink = null;
      if (postDeeplinkEvent.postId != null) {
        Navigator.of(context).push(MaterialPageRoute(
          builder: (context) => PostDetail(
            id: postDeeplinkEvent.postId,
            source: "deeplink",
          ),
        ));
      } else {
        Navigator.of(context).push(MaterialPageRoute(
          builder: (context) => PostDetail(
            hashId: postDeeplinkEvent.hashId,
            source: "deeplink",
          ),
        ));
      }
    }
  }

  void _onTapCreatePost() async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (BuildContext context) => const CreatePostPage(),
      ),
    );
    // Check if the device can vibrate
    Vibrate.feedback(FeedbackType.impact);

    AppAnalytics.logEvent(name: "create_post", parameters: {"source": "fab"});
  }

  Future<void> _onSelectedTabClicked(
      BuildContext context, NavItemName name) async {
    if (!Navigator.of(context).canPop()) {
      if (name == NavItemName.trends) {
        final scrollController = _newsFeedTabsViewModel
            .scrollControllers[_newsFeedSectionsTabController.index];
        if (scrollController.hasClients) {
          scrollController.animateTo(0,
              duration: const Duration(milliseconds: 500), curve: Curves.ease);
          _animationReverse();
        }
      } else {
        final scrollController = _getScrollController(name);
        if (scrollController?.hasClients ?? false) {
          await scrollController?.animateTo(0,
              duration: const Duration(milliseconds: 500), curve: Curves.ease);
        }
      }
    }
  }

  List<Widget> _actionList(
      BuildContext context, List<NavItem> navItems, int selectedIndex) {
    if (navItems.isEmpty) {
      return <Widget>[];
    }

    final currentTabName = navItems[selectedIndex].name;
    switch (currentTabName) {
      case NavItemName.trends:
        return <Widget>[
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  // builder: (BuildContext context) => SearchBar(),
                  builder: (BuildContext context) =>
                      const CommonSearchBar(source: 'feed'),
                ),
              );
            },
          )
        ];

      case NavItemName.circles:
        return <Widget>[
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  // builder: (BuildContext context) => CircleSearch(),
                  builder: (BuildContext context) =>
                      const CommonSearchBar(source: 'groups'),
                ),
              );
            },
          )
        ];
      default:
        return [];
    }
  }

  Widget _title(BuildContext context, List<NavItem> navItems, int selectedIndex,
      bool isPremium) {
    if (navItems.isEmpty) {
      return const SizedBox();
    }

    NavItem currentTab = navItems[selectedIndex];
    if (currentTab.name == NavItemName.trends) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.asset(
              isPremium
                  ? "assets/images/app-icon-premium.png"
                  : "assets/images/app-icon.jpg",
              height: 30,
            ),
          ),
          if (!isPremium) ...[
            const SizedBox(
              width: 8,
            ),
            Image.asset(
              "assets/images/praja-header-name.png",
              height: 20,
            ),
          ],
        ],
      );
    }

    return Padding(
        padding: const EdgeInsets.only(left: 2),
        child: Text(context.getString(currentTab.localizedNameKey)));
  }

  Widget? _leading(
      BuildContext context, List<NavItem> navItems, int selectedIndex) {
    return GestureDetector(
        onTap: () {
          _scaffoldKey.currentState?.openDrawer();
          AppAnalytics.onMenuDrawerOpened();
        },
        child: const Center(
            // center is needed because leading widget is presized
            child: AppUserAvatar(
          size: 32,
          strokeWidth: 1,
          strokeColor: Colors.black,
        )));
  }

  Future<void> _onNewMessageTapped() async {
    AppAnalytics.onNewChatClicked(source: "home_fab");
    final navigator = Navigator.of(context);
    final selectedUser = await navigator.push<UserIdentity>(
      MaterialPageRoute(
        builder: (context) => ChatTargetPickerPage.newMessage(),
      ),
    );
    if (selectedUser != null) {
      //navigate to user chat page
      navigator.push(
        PageSlideRight(
          page: ChatPage.withUser(
            selectedUser.id,
          ),
        ),
      );
    }
  }

  Widget? _fabFor(BuildContext context, NavItemName name) {
    switch (name) {
      case NavItemName.trends:
        return SizedBox(
          height: kToolbarHeight,
          child: FloatingActionButton.extended(
            extendedPadding: const EdgeInsets.symmetric(horizontal: 16),
            shape: !expandedFloatingButton ? const CircleBorder() : null,
            onPressed: Feedback.wrapForTap(_onTapCreatePost, context),
            label: AnimatedContainer(
              duration: const Duration(milliseconds: 0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.create_outlined,
                    size: 24,
                  ),
                  AnimatedSize(
                    duration: const Duration(milliseconds: 200),
                    child: !expandedFloatingButton
                        ? const SizedBox()
                        : Row(
                            children: [
                              const SizedBox(
                                width: 8,
                              ),
                              Padding(
                                padding: context.verticalAdjustmentPadding(
                                    fontSize: 18),
                                child: Text(
                                  context.getString(
                                      StringKey.homeCreatePostFabLabel),
                                  textScaler: const TextScaler.linear(1.0),
                                  style: const TextStyle(fontSize: 18),
                                ),
                              ),
                            ],
                          ),
                  ),
                ],
              ),
            ),
          ),
        );
      case NavItemName.chat:
        return FloatingActionButton(
          onPressed: Feedback.wrapForTap(_onNewMessageTapped, context),
          child: const Icon(PrajaIcons.draft_message),
        );
      default:
        return null;
    }
  }

  List<Tab> _getTabs(List<Tab> tabs) {
    final List<Tab> updatedTabs = [];
    if (tabs.length <= 3) {
      return tabs;
    }
    for (var i = 0; i < tabs.length; i++) {
      updatedTabs.add(
        Tab(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Text(
              tabs[i].text ?? "",
            ),
          ),
        ),
      );
    }
    return updatedTabs;
  }

  Widget _getTabBarViewWidget(
      BuildContext context, List<NavItem> navItems, int selectedIndex) {
    if (navItems.isEmpty) {
      return const SizedBox();
    }
    return AnimatedSize(
      duration: const Duration(milliseconds: 200),
      child: navItems[selectedIndex].name == NavItemName.trends
          ? LiveDataBuilder(
              liveData: _newsFeedTabsViewModel.newsFeedTabHeaders,
              builder: (_, tabs) {
                return Container(
                  height: kToolbarHeight,
                  width: double.infinity,
                  color: Colors.white,
                  child: _newsFeedTabsViewModel.newsFeedTabHeaders.value.isEmpty
                      ? const SizedBox()
                      : TabBar(
                          controller: _newsFeedSectionsTabController,
                          tabs: _getTabs(tabs),
                          dragStartBehavior: DragStartBehavior.down,
                          isScrollable: tabs.length > 3,
                          tabAlignment: tabs.length > 3
                              ? TabAlignment.start
                              : TabAlignment.fill,
                        ),
                );
              })
          : const SizedBox(),
    );
  }

  Gradient _getAppBarBackgroundGradient(
      {required bool isPremium, required bool isTrends}) {
    if (isPremium && isTrends) {
      return PremiumUtils.appBarBackgroundGradients();
    } else {
      return const LinearGradient(
        colors: [Colors.white, Colors.white],
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final loggedInUserViewModel = context.getViewModel<LoggedInUserViewModel>();
    return PopScope(
        canPop: navViewModel.selectedIndex.value == 0,
        onPopInvoked: (bool onPop) async {
          if (!onPop) {
            navViewModel.switchTo(NavItemName.trends);
          }
        },
        child: EventListener<NavBarEvent>(
          eventQueue: navViewModel.eventQueue,
          onEvent: (ctx, event) async {
            if (event is SelectedTabTappedEvent) {
              _onSelectedTabClicked(ctx, event.name);
            } else if (event is TabChangedEvent) {
              if (event.prev == NavItemName.notifications) {
                _markAllNotificationsRead(refresh: false);
              }

              if (event.current == NavItemName.chat) {
                chatListKey.currentState?.refresh();
              }

              if (event.current == NavItemName.trends) {
                navViewModel.updateTrendsUnreadDot(false);
              }
            }
          },
          child: LiveDataBuilder(
              liveData: navViewModel.navItems,
              builder: (ctx, navItems) {
                return LiveDataBuilder(
                    liveData: navViewModel.selectedIndex,
                    builder: (_, selectedIndex) {
                      return Scaffold(
                          key: _scaffoldKey,
                          drawer: LiveDataBuilder<AppUser?>(
                              liveData: loggedInUserViewModel.user,
                              builder: (ctx, loggedInUser) {
                                return loggedInUser == null
                                    ? const SizedBox()
                                    : PrajaNavigationDrawer(
                                        preloadedUser: loggedInUser,
                                        onSignOut: () {
                                          navViewModel.onLogout();
                                          widget.onSignOut();
                                        },
                                      );
                              }),
                          body: Stack(
                            children: [
                              SafeArea(
                                child: TabBarView(
                                  controller: _tabController,
                                  physics: const NeverScrollableScrollPhysics(),
                                  children: _getTabWidgetList(navItems),
                                ),
                              ),
                              Positioned(
                                bottom: 0,
                                width: MediaQuery.of(context).size.width,
                                child: SafeArea(
                                  bottom: false,
                                  child: Theme(
                                      data: navItems[selectedIndex].name ==
                                              NavItemName.posters
                                          ? ThemeData.dark(useMaterial3: false)
                                          : Theme.of(context),
                                      child: const GlobalNavBar()),
                                ),
                              ),
                              Positioned(
                                top: 0,
                                left: 0,
                                right: 0,
                                child: AnimatedSwitcher(
                                  switchInCurve: Curves.easeInOut,
                                  switchOutCurve: Curves.easeInOut,
                                  duration: const Duration(milliseconds: 500),
                                  child: navItems[selectedIndex].name ==
                                          NavItemName.posters
                                      ? const SizedBox()
                                      : SafeArea(
                                          top: false,
                                          child: AnimatedBuilder(
                                            animation: _appBarAnimation,
                                            builder: (ctx, child) {
                                              return Transform.translate(
                                                  offset: Offset(
                                                    0,
                                                    -(_appBarAnimation.value),
                                                  ),
                                                  child: child);
                                            },
                                            child: LiveDataBuilder<AppUser?>(
                                                liveData:
                                                    loggedInUserViewModel.user,
                                                builder: (context, user) {
                                                  if (user == null) {
                                                    return const SizedBox();
                                                  }
                                                  return Container(
                                                    decoration: (navItems
                                                                .isEmpty ||
                                                            _ignoreAppBarShadowNavItems
                                                                .contains(navItems[
                                                                        selectedIndex]
                                                                    .name))
                                                        ? null
                                                        : BoxDecoration(
                                                            gradient: _getAppBarBackgroundGradient(
                                                                isPremium: user
                                                                    .isPremium,
                                                                isTrends: navItems[
                                                                            selectedIndex]
                                                                        .name ==
                                                                    NavItemName
                                                                        .trends),
                                                            boxShadow: [
                                                              BoxShadow(
                                                                color: Colors
                                                                    .black
                                                                    .withOpacity(
                                                                        0.1), //color of shadow
                                                                spreadRadius:
                                                                    0.5, //spread radius
                                                                blurRadius:
                                                                    1, // blur radius
                                                                offset:
                                                                    const Offset(
                                                                        0, 1),
                                                              ),
                                                            ],
                                                          ),
                                                    child: Column(
                                                      children: [
                                                        AppBar(
                                                          backgroundColor:
                                                              Colors
                                                                  .transparent,
                                                          centerTitle: true,
                                                          elevation: 0,
                                                          leading: _leading(
                                                              context,
                                                              navItems,
                                                              selectedIndex),
                                                          title: _title(
                                                              context,
                                                              navItems,
                                                              selectedIndex,
                                                              user.isPremium),
                                                          automaticallyImplyLeading:
                                                              false,
                                                          actions: _actionList(
                                                              context,
                                                              navItems,
                                                              selectedIndex),
                                                        ),
                                                        _getTabBarViewWidget(
                                                            context,
                                                            navItems,
                                                            selectedIndex),
                                                      ],
                                                    ),
                                                  );
                                                }),
                                          ),
                                        ),
                                ),
                              ),
                              Positioned(
                                top: 0,
                                right: 0,
                                left: 0,
                                child: LiveDataBuilder<AppUser?>(
                                    liveData: loggedInUserViewModel.user,
                                    builder: (_, user) {
                                      if (user == null) {
                                        return const SizedBox();
                                      }
                                      return Container(
                                        height:
                                            MediaQuery.of(context).padding.top,
                                        decoration: BoxDecoration(
                                          gradient:
                                              _getAppBarBackgroundGradient(
                                                  isPremium: user.isPremium,
                                                  isTrends:
                                                      navItems[selectedIndex]
                                                              .name ==
                                                          NavItemName.trends),
                                        ),
                                      );
                                    }),
                              )
                            ],
                          ),
                          floatingActionButton: Transform.translate(
                            offset: const Offset(0, -navBarHeight),
                            child: Column(
                                // Why do we need a column? 👉 https://stackoverflow.com/a/62500610/3667647, https://github.com/flutter/flutter/issues/30132#issuecomment-478088507
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  if (navItems.isNotEmpty)
                                    _fabFor(context,
                                            navItems[selectedIndex].name) ??
                                        const SizedBox(),
                                ]),
                          ));
                    });
              }),
        ));
  }
}
