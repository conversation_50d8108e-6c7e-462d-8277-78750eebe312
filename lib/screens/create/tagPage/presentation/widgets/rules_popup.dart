import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/screens/create/tagPage/presentation/widgets/warning_popup.dart';
import 'package:praja/screens/create/tagPage/tagBloc/tag_bloc.dart';
import 'package:praja/screens/create/tagPage/tagBloc/tag_bloc_events.dart';

class TagHelper {
  showRulesPopup(BuildContext context) async {
    await showModalBottomSheet(
        enableDrag: false,
        isDismissible: false,
        backgroundColor: Colors.black26,
        context: context,
        elevation: 10,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10),
          topRight: Radius.circular(10),
        )),
        builder: (_) {
          return PopScope(
            canPop: false,
            child: Material(
                shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(10),
                  topRight: Radius.circular(10),
                )),
                elevation: 10,
                child: RulesPopupWidget(
                  ctx: context,
                )),
          );
        });
  }

  showWarningPopup(
    BuildContext context,
  ) async {
    showDialog(
        context: context,
        builder: (_) {
          return WarningPopupWidget(
            ctx: context,
          );
        });
  }
}

class RulesPopupWidget extends StatefulWidget {
  final BuildContext ctx;
  const RulesPopupWidget({super.key, required this.ctx});

  @override
  State<RulesPopupWidget> createState() => _RulesPopupWidgetState();
}

class _RulesPopupWidgetState extends State<RulesPopupWidget> {
  bool allowClick = false;
  late Timer timer;
  int _start = 3;
  @override
  void initState() {
    AppAnalytics.logEvent(name: "guidelines_prompt_shown");
    super.initState();
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_start == 0) {
        setState(() {
          timer.cancel();
          allowClick = true;
        });
      } else {
        setState(() {
          _start--;
        });
      }
    });
  }

  @override
  void dispose() {
    timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final rulesData =
        BlocProvider.of<TagBloc>(widget.ctx).state.tagData!.tagInfoPopUpData;
    return Wrap(
      children: [
        Container(
            padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 30),
            height: MediaQuery.of(context).size.height * 0.5,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                        height: 36,
                        child: Image.asset(
                          "assets/images/action/tag_icon.png",
                          color: Colors.black,
                        )),
                    const SizedBox(
                      width: 10,
                    ),
                    Text(
                      rulesData.headerText,
                      style: const TextStyle(fontSize: 20),
                    )
                  ],
                ),
                Row(
                  children: [
                    const Icon(
                      Icons.check_circle_outline_rounded,
                      color: Colors.green,
                      size: 40,
                    ),
                    const SizedBox(
                      width: 20,
                    ),
                    Flexible(
                      child: Text(
                        rulesData.greenText,
                        style: const TextStyle(
                            fontSize: 14.5,
                            letterSpacing: 0.12,
                            color: Color(0xff333333)),
                      ),
                    )
                  ],
                ),
                Row(
                  children: [
                    const Icon(
                      Icons.cancel_outlined,
                      color: Colors.red,
                      size: 40,
                    ),
                    const SizedBox(
                      width: 20,
                    ),
                    Flexible(
                      child: Text(
                        rulesData.redText,
                        style: const TextStyle(
                            fontSize: 14.5,
                            letterSpacing: 0.12,
                            color: Color(0xff333333)),
                      ),
                    )
                  ],
                ),
                Column(
                  children: [
                    Text(
                      // ignore: avoid_hardcoded_strings_in_ui
                      "$_start  ${context.getString(StringKey.rulesPopupTimeSecondsLabel)}",
                      style: TextStyle(
                        color: allowClick ? Colors.white : Colors.grey,
                        fontSize: 14,
                      ),
                    ),
                    ElevatedButton(
                        style: ButtonStyle(
                          padding: MaterialStateProperty.all(
                              const EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 50)),
                          backgroundColor: MaterialStateProperty.all(
                              allowClick ? Colors.black : Colors.black26),
                        ),
                        onPressed: allowClick
                            ? () {
                                AppAnalytics.logEvent(
                                    name: "guidelines_prompt_yes_click");
                                Navigator.of(context).pop();
                                BlocProvider.of<TagBloc>(widget.ctx)
                                    .add(TagRulesAccepted());
                              }
                            : null,
                        child: Text(rulesData.buttonText)),
                  ],
                )
              ],
            )),
      ],
    );
  }
}
