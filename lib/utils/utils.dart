import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:android_sms_retriever/android_sms_retriever.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:contacts_service/contacts_service.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:encrypt/encrypt.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart' hide Badge;
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get_it/get_it.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:pointycastle/asymmetric/api.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/home/<USER>/home.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/user/models/app_user.dart';
import 'package:praja/features/whatsapp_share/whatsapp_share.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/badge.dart';
import 'package:praja/models/circle.dart';
import 'package:praja/models/hashtag.dart';
import 'package:praja/models/post.dart';
import 'package:praja/models/report_item.dart';
import 'package:praja/models/user_contact.dart';
import 'package:praja/models/v2/local_image.dart';
import 'package:praja/models/v2/photo.dart';
import 'package:praja/models/v2/refer.dart';
import 'package:praja/presentation/app_icons.dart';
import 'package:praja/screens/external_web_view.dart';
import 'package:praja/screens/posts/photo_slider.dart';
import 'package:praja/screens/posts/share_card/post_share_card_screen.dart';
import 'package:praja/screens/users/user.dart';
import 'package:praja/services/app_cache_manager.dart';
import 'package:praja/services/endpoint.dart';
import 'package:praja/services/home.dart';
import 'package:praja/services/user_store.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/utils/processor_info_plugin.dart';
import 'package:praja/utils/widgets/page_transition_widget.dart';
import 'package:praja/utils/widgets/photo_viewer/photo_view_screen.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:store_checker/store_checker.dart';
import 'package:url_launcher/url_launcher.dart' as url_launcher;
import 'package:video_thumbnail/video_thumbnail.dart';

class Utils {
  static double getDefaultVisibilityThresholdvalue() => 0.6;
  final HomeService _homeService = GetIt.I.get<HomeService>();

  static Future<void> showSnackBarMessage(
    BuildContext? context,
    String msg, {
    double fontSize = 14.0,
    Toast toastLength = Toast.LENGTH_SHORT,
  }) async {
    await Fluttertoast.cancel();
    await Fluttertoast.showToast(
      msg: msg,
      backgroundColor: Colors.black.withOpacity(0.7),
      textColor: Colors.white,
      fontSize: fontSize,
      toastLength: toastLength,
    );
  }

  static void showToast(
    String msg, {
    double fontSize = 14.0,
    Toast toastLength = Toast.LENGTH_SHORT,
  }) {
    Fluttertoast.cancel();
    Fluttertoast.showToast(
      msg: msg,
      backgroundColor: Colors.black.withOpacity(0.7),
      textColor: Colors.white,
      fontSize: fontSize,
      toastLength: toastLength,
    );
    return;
  }

  static List<UserContact> cleanUpContacts(Iterable<Contact> contacts) {
    List<UserContact> cleanedContacts = [];

    for (var contact in contacts) {
      var phones = contact.phones;
      if (phones != null && phones.isNotEmpty) {
        if (contact.displayName == null || contact.displayName == "") {
          contact.displayName = "Unknown";
        }

        cleanedContacts.add(
          UserContact(
            name: contact.displayName,
            phone: phones.first.value,
          ),
        );
      }
    }

    return cleanedContacts;
  }

  static Future<String> getAppVersion() async {
    final PackageInfo info = await PackageInfo.fromPlatform();
    return info.version.trim();
  }

  static Future<String> getBuildNumber() async {
    final PackageInfo info = await PackageInfo.fromPlatform();
    return info.buildNumber.trim();
  }

  static Future<LocalImage?> getVideoThumbnail(String videoPathUrl) async {
    var appDocDir = await getApplicationDocumentsDirectory();
    final folderPath = appDocDir.path;

    try {
      String? thumb = await VideoThumbnail.thumbnailFile(
        video: videoPathUrl,
        thumbnailPath: folderPath,
        imageFormat:
            ImageFormat.JPEG, //this image will store in created folder path
        quality: 30,
      );

      if (thumb != null) {
        final thumbFile = File(thumb);
        var decodedImage =
            await decodeImageFromList(thumbFile.readAsBytesSync());

        return LocalImage(
          path: thumb,
          height: decodedImage.height.toDouble(),
          width: decodedImage.width.toDouble(),
        );
      }
      AppAnalytics.logEvent(name: "video_thumbnail_failed", parameters: {
        "thumbnail": thumb.toString(),
      });
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<void> referApp(BuildContext context, String source,
      {bool toWhatsApp = true}) async {
    try {
      Refer refer = await _homeService.getReferText();
      if (Platform.isAndroid && toWhatsApp) {
        AppAnalytics.logShare(
          contentType: "app",
          itemId: "0",
          method:
              "whatsapp${source.isNotEmpty ? "_${source.toLowerCase()}" : ""}",
        );

        // todo: Plugin only supports Android for now
        WhatsappShareAndroid.shareText(refer.text);
      } else {
        AppAnalytics.logShare(
          contentType: "app",
          itemId: "0",
          method:
              "external_share${source.isNotEmpty ? "_${source.toLowerCase()}" : ""}",
        );

        Share.share(
          refer.text,
          subject: "Refer",
        );
      }
    } catch (e) {
      Utils.showToast(localisedErrorMessage(e));
    }
  }

  static Future<File> writeToTempFile(Uint8List imageData,
      {imageName = "invite_card.png"}) async {
    final directory = await getTemporaryDirectory();
    final pathOfImage = await File('${directory.path}/$imageName').create();
    final bytes = imageData.buffer.asUint8List();
    await pathOfImage.writeAsBytes(bytes);
    return pathOfImage;
  }

  static referWithInviteCard(File pathOfImage, Uint8List inviteCardImage,
      BuildContext context, String source, Refer refer,
      {bool toWhatsApp = true}) async {
    try {
      if (Platform.isAndroid && toWhatsApp) {
        // todo: Plugin only supports Android for now
        if (refer.generateInviteCard) {
          WhatsappShareAndroid.shareFiles(
            [XFile(pathOfImage.path)],
            text: refer.text,
          );
        } else {
          WhatsappShareAndroid.shareText(refer.text);
        }
      } else {
        if (refer.generateInviteCard) {
          if (Platform.isAndroid) {
            await Share.shareXFiles([
              XFile.fromData(
                inviteCardImage,
                name: "inviteCard.png",
                mimeType: "image/png",
              )
            ], text: refer.text);
          } else {
            await Share.shareXFiles([
              XFile.fromData(
                inviteCardImage,
                name: "inviteCard.png",
                mimeType: "image/png",
              )
            ]);
          }
        } else {
          Share.share(
            refer.text,
            subject: "Refer",
          );
        }
      }
    } catch (e) {
      Utils.showToast(localisedErrorMessage(e));
    }
  }

  static facebookShare(
      {required int userId,
      required BuildContext context,
      required String source}) async {
    try {
      String msg = "";

      msg = await Home.getFacebookShareText(userId);

      Share.share(
        msg,
      );
    } catch (e) {
      Utils.showToast(localisedErrorMessage(e));
    }
  }

  static Future<Uint8List> captureWidget(Widget widget, BuildContext context,
      {Duration duration = const Duration(seconds: 1)}) async {
    ScreenshotController screenshotController = ScreenshotController();
    final image = await screenshotController.captureFromWidget(widget,
        delay: duration, pixelRatio: 4, context: context);
    return image;
  }

  static bool showBadgeCelebrationInviteCard(Badge? badge) {
    return badge != null &&
        badge.active &&
        badge.verificationStatus == VerificationStatus.verified &&
        badge.badgeBanner != BadgeBanner.none;
  }

  static String getApiKey() {
    // TODO: https://www.notion.so/praja-app/Secure-unauthenticated-APIs-with-HMAC-authentication-44c74173503c49a2a41ac5d61995cd7d
    return '2LJdwz2KV3eyQx4jzvT4nlBvHSQ5i0t6';
  }

  static Future<int> getVideoUploadDurationLimitInSecs() async {
    final FirebaseRemoteConfig remoteConfig = FirebaseRemoteConfig.instance;
    try {
      // Using default duration to force fetching from remote server.
      remoteConfig.setConfigSettings(RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(days: 1)));
      await remoteConfig.fetchAndActivate();
      final durationLimitInSecs =
          remoteConfig.getInt('video_upload_duration_limit_secs');

      if (durationLimitInSecs > 0) {
        return durationLimitInSecs;
      }
    } catch (e) {
      printDebug('Error while fetching video upload duration limit',
          error: e, level: LogLevel.error);
    }

    return 10 * 60;
  }

  static Future<String> getVideoLimitErrorMessage() async {
    final FirebaseRemoteConfig remoteConfig = FirebaseRemoteConfig.instance;
    try {
      // Using default duration to force fetching from remote server.
      remoteConfig.setConfigSettings(RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(days: 1)));
      await remoteConfig.fetchAndActivate();
      final errorMessage =
          remoteConfig.getString('video_upload_limit_error_message');

      if (errorMessage.isNotEmpty) {
        return errorMessage;
      }
    } catch (e) {
      printDebug('Error while fetching video limit error message',
          error: e, level: LogLevel.error);
    }

    return "వీడియో 10 నిమిషాల కన్నా ఎక్కువ ఉండకూడదు";
  }

  static Future<double> getVideoTargetBitrate() async {
    final FirebaseRemoteConfig remoteConfig = FirebaseRemoteConfig.instance;
    try {
      // Using default duration to force fetching from remote server.
      remoteConfig.setConfigSettings(RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(days: 1)));
      await remoteConfig.fetchAndActivate();
      final targetBitrate = remoteConfig.getDouble('video_target_bitrate');

      if (targetBitrate > 0) {
        return targetBitrate;
      }
    } catch (e) {
      printDebug('Error while fetching video target bitrate',
          error: e, level: LogLevel.error);
    }

    return 4.0;
  }

  static Future<int> getPosterTabAutoRefreshTimeInSecs() async {
    final FirebaseRemoteConfig remoteConfig = FirebaseRemoteConfig.instance;
    try {
      remoteConfig.setConfigSettings(RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(days: 1)));
      await remoteConfig.fetchAndActivate();
      final autoRefreshTimeInSecs =
          remoteConfig.getInt('posters_tab_auto_refresh_time_secs');

      if (autoRefreshTimeInSecs > 0) {
        return autoRefreshTimeInSecs;
      }
    } catch (e) {
      printDebug('Error while fetching poster tab auto refresh time',
          error: e, level: LogLevel.error);
    }

    return 1800;
  }

  static Future<double> getVideoMinBitrate() async {
    final FirebaseRemoteConfig remoteConfig = FirebaseRemoteConfig.instance;
    try {
      // Using default duration to force fetching from remote server.
      remoteConfig.setConfigSettings(RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(days: 1)));
      await remoteConfig.fetchAndActivate();
      final minBitrate = remoteConfig.getDouble('video_min_bitrate');

      if (minBitrate > 0) {
        return minBitrate;
      }
    } catch (e) {
      printDebug('Error while fetching video min bitrate',
          error: e, level: LogLevel.error);
    }

    return 2.0;
  }

  static Future<String?> getEncryptedToken() async {
    final AppUser? user = await GetIt.I.get<UserStore>().getAppUser();
    if (user != null) {
      Map<String, Object> jsonData = {
        "user_id": user.id,
        "timestamp": DateTime.now().millisecondsSinceEpoch.toString()
      };
      final String plainText = json.encode(jsonData);
      final pubKey = await rootBundle.loadString("assets/pubKey.pem");
      final publicKey = RSAKeyParser().parse(pubKey) as RSAPublicKey;
      final encrypter = Encrypter(RSA(
        publicKey: publicKey,
      ));

      final encrypted = encrypter.encrypt(plainText);
      return encrypted.base64;
    }
    return null;
  }

  static String getBackupHashtagShareText(Hashtag hashtag) {
    String hashtagUrl =
        "${Endpoint.getMobileWebUrl()}/hashtags/${hashtag.hashId}";
    String shareText =
        "Praja యాప్ లో ఇప్పుడు #${hashtag.name} ట్రెండ్ అవుతుంది. \n$hashtagUrl\n\n#${hashtag.name} టాపిక్ మీద మరిన్ని పోస్టులకు ఇప్పుడే డౌన్లోడ్ చేయండి, Praja యాప్.\nhttps://onelink.to/praja-buzz";

    return shareText;
  }

  static String getBackupReferText() {
    return "హాయ్, పొలిటికల్ ట్రెండ్స్ మరియు లేటెస్ట్ అప్డేట్స్ కోసం నేను PRAJA యాప్ లో చేరాను.\nమీకు కూడా ఈ యాప్ కచ్చితంగా నచ్చుతుంది, ఇప్పుడే డౌన్లోడ్ చేయండి.\n👇👇👇👇👇\nhttps://onelink.to/praja-buzz";
  }

  static String getBackupPostShareText(Post post) {
    String postUrl = "${Endpoint.getMobileWebUrl()}/posts/${post.hashId}";
    return "Praja యాప్ లో ఇప్పుడే ఒక కొత్త పోస్ట్ ని కనుక్కున్నాను.\n\n${post.content ?? ""}\n$postUrl\n\nతెలుగు లో మరిన్ని లేటెస్ట్ ట్రెండ్స్ కోసం ఇపుడే Praja యాప్ ని డౌన్లోడ్ చేస్కోండి.\nhttps://onelink.to/praja-buzz";
  }

  static Future<void> setUserPermissions(dynamic permissions) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    if (permissions.isEmpty || permissions == null) {
      permissions = [];
    }
    prefs.setString("user.permissions", json.encode(permissions));
  }

  static Future<void> setCanCreateCircle(bool canCreateCircle) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool("user.can_create_circle", canCreateCircle);
  }

  static Future<bool?> getCanCreateCircle() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool("user.can_create_circle");
  }

  static Future<void> setCirclesTutorialShown(bool shown) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool("user.circles_tutorial_shown", shown);
  }

  static Future<bool?> getCirclesTutorialShown() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool("user.circles_tutorial_shown");
  }

  static Future<void> setHashtagTutorialShown(bool shown) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool("user.hashtag_tutorial_shown", shown);
  }

  static Future<bool?> getHashtagTutorialShown() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool("user.hashtag_tutorial_shown");
  }

  static Future<void> setSmsHash(String hash) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString("sms_hash", hash);
  }

  static Future<void> setReferUserHashId(String userHashId) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString("user.refer_user_hash_id", userHashId);
  }

  static Future<String?> getReferUserHashId() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString("user.refer_user_hash_id");
  }

  static String? getCode(String sms) {
    final intRegex = RegExp(r'\d+', multiLine: true);
    final code = intRegex.allMatches(sms).first.group(0);
    return code;
  }

  static Future<String> getSmsHash() async {
    if (Platform.isIOS) {
      return "";
    }

    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? hash = prefs.getString("sms_hash");

    if (hash == null || hash.isEmpty) {
      hash = await AndroidSmsRetriever.getAppSignature() ?? "";
      setSmsHash(hash);
    }

    return hash;
  }

  // Method to show privacy policy in external web view
  static void showPrivacyPolicy(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (BuildContext context) => ExternalWebView(
          title: "Privacy Policy",
          url: "${Endpoint.getRorApiBaseUrl()}/privacy-policy",
        ),
      ),
    );
  }

  // Method to show privacy policy in external web view
  static void showReferralScreen(BuildContext context, String signature) {
    final String uriEncodedSignature = Uri.encodeComponent(signature);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (BuildContext context) => ExternalWebView(
          title: "Referral Points",
          url: Endpoint.getMobileWebUrl() +
              "/referrals-data?signature=$uriEncodedSignature".toString(),
        ),
      ),
    );
  }

  static Future<List<String>> getUserPermissions() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String permissionString = prefs.getString("user.permissions") ?? "[]";
    List<String> permissions = [];
    if (permissionString != "[]" &&
        permissionString != "[[]]" &&
        permissionString != "") {
      List<dynamic> permissionsList = json.decode(permissionString);

      for (var i = 0; i < permissionsList.length; i++) {
        permissions.add(permissionsList[i].toString());
      }
    }

    return permissions;
  }

  /// Test here - https://regex101.com/r/OPIjKP/1
  static String getUrlRegexPattern() {
    return r'(https?:\/\/|www\.)?[\w/\-?=%.]+\.[a-zA-Z]{2,6}(?:[\/\w\-?=%.&#@]*)?(?=\)|\}|\]|\s|$)';
  }

  //Method to check if the link is valid youtube link
  static bool isValidYouTubeUrl(String content) {
    // Consider link valid only if the video id is extracted.
    String? videoId = Utils.convertUrlToId(content);
    if (videoId == null) {
      return false;
    }
    return true;
  }

  static String? convertUrlToId(String url, {bool trimWhitespaces = true}) {
    if (trimWhitespaces) url = url.trim();

    for (var regex in [
      r'^https:\/\/(?:www\.|m\.)?youtube\.com\/shorts\/([_\-a-zA-Z0-9]{11}).*$',
      r'^https:\/\/(?:www\.|m\.)?youtube\.com\/watch\?v=([_\-a-zA-Z0-9]{11}).*$',
      r'^https:\/\/(?:www\.|m\.)?youtube(?:-nocookie)?\.com\/embed\/([_\-a-zA-Z0-9]{11}).*$',
      r'^https:\/\/youtu\.be\/([_\-a-zA-Z0-9]{11}).*$',
    ]) {
      RegExpMatch? match = RegExp(regex).firstMatch(url);
      if (match != null) {
        if (match.groupNames.isNotEmpty) return match.group(1);
      }
    }
    return null;
  }

  static void launchURL(String url) async {
    //parse URI and handle exceptions
    Uri? uri = Uri.tryParse(url);

    if (uri != null && !uri.hasScheme) {
      uri = Uri.tryParse("https://$url");
    }

    if (uri == null || !uri.hasScheme) {
      throw ArgumentError.value(url, 'url', 'Invalid URL: $url');
    }

    //check if the url is valid
    if (await url_launcher.canLaunchUrl(uri)) {
      //launch the url
      await url_launcher.launchUrl(uri,
          mode: url_launcher.LaunchMode.externalApplication);
    } else {
      //if the url is not valid, throw an error
      throw UnsupportedError('Cannot launch $url');
    }
  }

  static Future<int> getReferPromptDelay() async {
    // Get Latest version info from firebase config
    final FirebaseRemoteConfig remoteConfig = FirebaseRemoteConfig.instance;
    try {
      // Using default duration to force fetching from remote server.
      //TODO: GG UPGRADE

      remoteConfig.setConfigSettings(RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(seconds: 3600)));
      await remoteConfig.fetchAndActivate();

      int delayDays = remoteConfig.getInt('invite_prompt_delay_days');
      if (delayDays > 0) {
        return remoteConfig.getInt('invite_prompt_delay_days');
      }
    } catch (e, st) {
      printDebug(localisedErrorMessage(e),
          level: LogLevel.debug, stackTrace: st);
    }

    return 7;
  }

  static Widget getBadgeIconImage(String url) {
    return CachedNetworkImage(
        cacheManager: AppCacheManager.instance,
        imageUrl: url,
        errorWidget: (context, url, error) {
          return const Icon(Icons.error);
        });
  }

  static Future<int> getImageCompressionQuality() async {
    return 80;
  }

  static Future<double> getPostVisibilityThreshold() async {
    //Get Latest version info from firebase config
    final FirebaseRemoteConfig remoteConfig = FirebaseRemoteConfig.instance;
    try {
      // Using default duration to force fetching from remote server.
      //TODO: GG UPGRADE
      remoteConfig.setConfigSettings(RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(days: 1)));
      await remoteConfig.fetchAndActivate();

      double v = remoteConfig.getDouble('post_visibility_threshold');

      if (v > 0.0) {
        return v;
      }
    } catch (e, st) {
      printDebug(localisedErrorMessage(e),
          level: LogLevel.debug, stackTrace: st);
    }

    return getDefaultVisibilityThresholdvalue();
  }

  static Future<Map<String, dynamic>> getContactsTexts() async {
    Map<String, dynamic> defaultValues = {
      "header": "Praja లో మీ స్నేహితులను కనుగొనండి",
      "body_1":
          "లాగిన్/సైన్ అప్ సమయంలో మీ ఫోన్ బుక్ పరిచయాలను అను ప్రజా యాప్ సేకరించి నిల్వ చేస్తుంది. మీ స్నేహితులను పరిచయస్తుల ను యాప్ లో చేరేందుకు వాట్సాప్ ద్వారా ఆహ్వానిస్తాం. మీ సమాచారం విషయంలో ప్రజా యాప్ అత్యంత గోప్యతను పాటిస్తుంది. దీని గురించి మరింత తెలుసుకోండి ",
      //"PRAJA App collects/stores your contacts to enable connecting with your phonebook contacts, during login/signup, and thus enabling you to consume content in a more personalised fashion. Know more about it in our ",
      "body_2": ".",
      "privacy_policy": "గోప్యతా విధానం",
      "settings_header": "సెట్టింగ్స్ లో మార్చండి",
      "settings_body": "సెట్టింగ్స్ లో Contacts పర్మిషన్ ఇవ్వండి"
    };

    final FirebaseRemoteConfig remoteConfig = FirebaseRemoteConfig.instance;
    try {
      remoteConfig.setDefaults(defaultValues);
      remoteConfig.setConfigSettings(RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(days: 1)));
      await remoteConfig.fetchAndActivate();
      Map<String, dynamic> mapValues = json.decode(
        remoteConfig.getValue("contacts_texts").asString(),
      );
      return mapValues;
    } catch (e, st) {
      printDebug(localisedErrorMessage(e),
          level: LogLevel.debug, stackTrace: st);
    }

    return defaultValues;
  }

  static String getCopyrightText() {
    DateTime now = DateTime.now();
    String year = now.year.toString();
    return "$year © Circleapp Online Services";
  }

  static void showPostShareCard(
      BuildContext context,
      AssetImage uploadProfilePicImage,
      AssetImage appShareIcon,
      Post post) async {
    await Future.wait([
      precacheImage(uploadProfilePicImage, context),
      precacheImage(appShareIcon, context),
    ]);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (post.showPostShareCard) {
        showGeneralDialog(
          context: context,
          barrierColor: Colors.black38,
          barrierLabel: 'PostShareCard',
          barrierDismissible: true,
          pageBuilder: (_, __, ___) => Center(
            child: Container(
              color: Colors.transparent,
              child: Material(
                  color: Colors.transparent,
                  child: PostShareCardScreen(
                    post: post,
                    user: post.user,
                    screenWidth: MediaQuery.of(context).size.width,
                    appPostShareCardIcon: appShareIcon,
                    uploadProfilePicImage: uploadProfilePicImage,
                  )),
            ),
          ),
        );
        bool hasPic = post.user.photo != null;
        AppAnalytics.logEvent(
            name: "post_share_card_shown",
            parameters: {"has_profile_picture": hasPic});
      }
    });
  }

  static void setAutoFillMobileNumber(String mobileNumber) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString("last_login_mobile", mobileNumber);
  }

  static LinearGradient getGoldAndSilverGradients(BadgeBanner badgeBanner) {
    LinearGradient goldGradient = const LinearGradient(colors: [
      Color(0xffD27209),
      Color(0xffDB9E00),
      Color(0xffFEF2DC),
      Color(0xffDB9F01),
      Color(0xffBF5E17),
      Color(0xffD89703),
      Color(0xffFDF1D7),
      Color(0xffDA9C01),
      Color(0xffBD5B18),
      Color(0xffFEEC95),
      Color(0xffDEA60E),
      Color(0xffFDEB93),
    ]);
    LinearGradient silverGradient = const LinearGradient(colors: [
      Color(0xff8D8985),
      Color(0xffB1B1B0),
      Color(0xffF3F1EC),
      Color(0xffDAD9D6),
      Color(0xffB6B0AD),
      Color(0xffAFADA9),
      Color(0xffF1F0EE),
      Color(0xffA1A1A1),
      Color(0xff808080),
      Color(0xffC6C6C6),
      Color(0xffACACAC),
      Color(0xffBBBBBB),
    ]);
    LinearGradient whiteGradient = const LinearGradient(colors: [
      Color(0xff8899A6),
      Color(0xffAFC1D9),
      Color(0xffCADBF2),
      Color(0xffE4EEFC),
      Color(0xffCADBF2),
      Color(0xffC2D7F8),
      Color(0xffCADBF2),
      Color(0xffCADBF2),
      Color(0xffE4EEFC),
      Color(0xffCADBF2),
      Color(0xffC2D7F8),
      Color(0xffC2D7F8),
      Color(0xffE4EEFC),
      Color(0xffCADBF2),
      Color(0xffF1F0EE),
      Color(0xffE4EEFC),
      Color(0xffCADBF2),
      Color(0xffAFC1D9),
    ], stops: [
      0.0,
      0.02,
      0.13,
      0.15,
      0.17,
      0.23,
      0.33,
      0.37,
      0.42,
      0.46,
      0.55,
      0.69,
      0.72,
      0.85,
      0.89,
      0.9,
      0.96,
      1
    ]);
    switch (badgeBanner) {
      case BadgeBanner.gold:
        return goldGradient;
      case BadgeBanner.silver:
        return silverGradient;
      case BadgeBanner.white:
        return whiteGradient;
      case BadgeBanner.none:
        return const LinearGradient(
            colors: [Colors.transparent, Colors.transparent]);
    }
  }

  static String durationToStringOnlyMinutesSeconds(Duration d) {
    String twoDigits(int n) {
      if (n >= 10) return "$n";
      return "0$n";
    }

    if (d.inMicroseconds < 0) {
      return "-${-d}";
    }
    String twoDigitMinutes =
        twoDigits(d.inMinutes.remainder(Duration.minutesPerHour));
    String twoDigitSeconds =
        twoDigits(d.inSeconds.remainder(Duration.secondsPerMinute));
    return "$twoDigitMinutes:$twoDigitSeconds";
  }

  static void openUserPage(BuildContext context, int userId,
      {required String source}) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (BuildContext context) => UserPage(
          id: userId,
          source: source,
        ),
      ),
    );
  }

  static void openPhoto(BuildContext context, String url) {
    Navigator.push(
        context,
        PageSlideRight(
            page: PhotoViewScreen(
          photoUrl: url,
        )));
  }

  static void openUserPhoto(BuildContext context, Photo? photo) {
    if (photo != null && photo.url.isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (BuildContext context) => PhotoSlider(
            photos: <Photo>[photo],
          ),
        ),
      );
    }
  }

  static List<ReportItem> getPostReportItems() {
    return [
      ReportItem(id: 7, name: "తప్పు ట్యాగ్ ఎంచుకోబడినది"), // Wrong tag
      ReportItem(id: 8, name: "స్పామ్ పోస్ట్"), // Spam
      ReportItem(id: 9, name: "హింసాత్మకంగా ఉంది"), // Violence
      ReportItem(id: 10, name: 'అశ్లీల / పిల్లల అశ్లీలత ఉంది'), // Adult content
      ReportItem(id: 11, name: "మా నమ్మకాలకి వ్యతిరేకంగా ఉంది"), // Belief
      ReportItem(id: 12, name: "నా వ్యక్తిగతమైన విషయం"), // Too Personal
      ReportItem(id: 13, name: "ఇతర కారణాలు"), // Others
    ];
  }

  static List<ReportItem> getCommentReportItems() {
    return [
      ReportItem(id: 7, name: "తప్పు ట్యాగ్ ఎంచుకోబడినది"), // Wrong tag
      ReportItem(id: 8, name: "స్పామ్ కామెంట్"), // Spam
      ReportItem(id: 9, name: "హింసాత్మకంగా ఉంది"), // Violence
      ReportItem(id: 10, name: 'అశ్లీల / పిల్లల అశ్లీలత ఉంది'), // Adult content
      ReportItem(id: 11, name: "మా నమ్మకాలకి వ్యతిరేకంగా ఉంది"), // Belief
      ReportItem(id: 12, name: "నా వ్యక్తిగతమైన విషయం"), // Too Personal
      ReportItem(id: 13, name: "ఇతర కారణాలు"), // Others
    ];
  }

  static List<ReportItem> getUserReportItems() {
    return [
      ReportItem(id: 1, name: "ద్వేషపూరిత ప్రసంగం లేదా చిహ్నాలు"), //hate speech
      ReportItem(id: 2, name: "స్వీయ గాయం లేదా ఆత్మహత్య"), // self injury
      ReportItem(id: 3, name: "హింసాత్మకంగా ఉంది"), // Violence
      ReportItem(id: 4, name: 'అశ్లీల / పిల్లల అశ్లీలత ఉంది'), // Adult content
      ReportItem(id: 5, name: "బెదిరింపు లేదా వేధింపు"), //harassment
      ReportItem(id: 6, name: "తప్పుదారి పట్టించే లేదా సాధ్యం స్కామ్"), //scam
      ReportItem(id: 7, name: "మేధో సంపత్తి ఉల్లంఘన"),
      ReportItem(id: 8, name: "ఇతర కారణాలు"), // Others
    ];
  }

  static showTryAgainSheet(VoidCallback tryAgainCallback, BuildContext context,
      {String error = ""}) {
    AppAnalytics.logEvent(
        name: "try_again_sheet_shown",
        parameters: {"error": error.toString(), "source": "initial_data"});
    showModalBottomSheet(
        enableDrag: false,
        isDismissible: false,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10.0),
                topRight: Radius.circular(10.0))),
        context: context,
        builder: (context) {
          return PopScope(
            canPop: false,
            child: Wrap(
              children: [
                Padding(
                  padding: const EdgeInsets.only(
                      bottom: 25, left: 25, right: 25, top: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Align(
                        alignment: Alignment.center,
                        child: Container(
                          width: 50,
                          height: 5,
                          decoration: BoxDecoration(
                              color: Colors.grey[400],
                              borderRadius: BorderRadius.circular(10)),
                        ),
                      ),
                      const SizedBox(height: 20),
                      Text(
                        context.getString(StringKey.noInternetConnectionText),
                        style:
                            const TextStyle(fontSize: 17, color: Colors.black),
                      ),
                      const SizedBox(height: 20),
                      ElevatedButton(
                          style: ButtonStyle(
                              fixedSize: MaterialStateProperty.all(
                                  Size(MediaQuery.of(context).size.width, 50))),
                          onPressed: () {
                            AppAnalytics.logEvent(
                                name: "try_again_click",
                                parameters: {
                                  "error": error.toString(),
                                  "source": "error_bottom_sheet"
                                });
                            Navigator.of(context).pop();
                            tryAgainCallback();
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.refresh),
                              const SizedBox(width: 10),
                              Text(
                                context.getString(StringKey.retryLabel),
                                style: const TextStyle(fontSize: 17),
                              ),
                            ],
                          ))
                    ],
                  ),
                )
              ],
            ),
          );
        });
  }

  static showRetryCircleSheet(
      VoidCallback tryAgainCallback, BuildContext context,
      {String error = ""}) {
    AppAnalytics.logEvent(
        name: "circle_try_again_sheet_shown",
        parameters: {"error": error.toString(), "source": "circle_page"});
    showModalBottomSheet(
        enableDrag: false,
        isDismissible: false,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10.0),
                topRight: Radius.circular(10.0))),
        context: context,
        builder: (context) {
          return WillPopScope(
            onWillPop: () async {
              Navigator.pop(context);
              Navigator.pop(context);
              return true;
            },
            child: Wrap(
              children: [
                Padding(
                  padding: const EdgeInsets.only(
                      bottom: 25, left: 25, right: 25, top: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Align(
                        alignment: Alignment.center,
                        child: Container(
                          width: 50,
                          height: 5,
                          decoration: BoxDecoration(
                              color: Colors.grey[400],
                              borderRadius: BorderRadius.circular(10)),
                        ),
                      ),
                      const SizedBox(height: 20),
                      Text(
                        context.getString(StringKey.noInternetConnectionText),
                        style:
                            const TextStyle(fontSize: 17, color: Colors.black),
                      ),
                      const SizedBox(height: 20),
                      ElevatedButton(
                          style: ButtonStyle(
                              fixedSize: MaterialStateProperty.all(
                                  Size(MediaQuery.of(context).size.width, 50))),
                          onPressed: () {
                            AppAnalytics.logEvent(
                                name: "try_again_click",
                                parameters: {
                                  "error": error.toString(),
                                  "source": "error_bottom_sheet_circle"
                                });
                            Navigator.of(context).pop();
                            tryAgainCallback();
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.refresh),
                              const SizedBox(width: 10),
                              Text(
                                context.getString(StringKey.retryLabel),
                                style: const TextStyle(fontSize: 17),
                              ),
                            ],
                          ))
                    ],
                  ),
                )
              ],
            ),
          );
        });
  }

  static showSpeakerAccessSheet(BuildContext context) {
    AppAnalytics.logEvent(name: "microphone_permission_sheet_shown");
    showModalBottomSheet(
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10.0),
                topRight: Radius.circular(10.0))),
        context: context,
        builder: (context) {
          return Wrap(
            children: [
              const SizedBox(height: 10),
              SizedBox(
                height: 100,
                child: Center(
                    child: Image.asset("assets/images/action/mic_icon.png")),
              ),
              Padding(
                padding: const EdgeInsets.only(
                    bottom: 25, left: 25, right: 25, top: 20),
                child: Align(
                    alignment: Alignment.center,
                    child: Text(
                      context
                          .getString(StringKey.microphoneRequestPermissionText),
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                          fontWeight: FontWeight.w600, fontSize: 16),
                    )),
              ),
              const SizedBox(height: 20),
              Padding(
                padding: const EdgeInsets.only(bottom: 20),
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        height: MediaQuery.of(context).size.height * 0.05,
                        width: MediaQuery.of(context).size.width * 0.4,
                        child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                                foregroundColor: Colors.black,
                                backgroundColor: Colors.grey[300],
                                elevation: 0,
                                padding: const EdgeInsets.all(10)),
                            onPressed: () {
                              AppAnalytics.logEvent(
                                  name: "microphone_permission",
                                  parameters: {"opened_settings": "no"});
                              Navigator.pop(context);
                            },
                            child: Text(
                                context.getString(StringKey.noAlternateLabel))),
                      ),
                      const SizedBox(width: 20),
                      SizedBox(
                        height: MediaQuery.of(context).size.height * 0.05,
                        width: MediaQuery.of(context).size.width * 0.4,
                        child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.all(10)),
                            onPressed: () {
                              AppAnalytics.logEvent(
                                  name: "microphone_permission",
                                  parameters: {"opened_settings": "yes"});
                              PhotoManager.openSetting();
                              Navigator.pop(context);
                            },
                            child: Text(context
                                .getString(StringKey.goToSettingsLabel))),
                      )
                    ],
                  ),
                ),
              )
            ],
          );
        });
  }

  static String firstLetterExtraction(String name, {bool isCircle = false}) {
    String defaultLetter = isCircle ? "C" : "U";
    if (name.isEmpty) {
      return defaultLetter;
    }
    return name.isNotEmpty ? name.characters.first : defaultLetter;
  }

  static String trimLeft(String from, String pattern) {
    if (from.isEmpty || pattern.isEmpty || pattern.length > from.length) {
      return from;
    }

    while (from.startsWith(pattern)) {
      from = from.substring(pattern.length);
    }
    return from;
  }

  static String trimRight(String from, String pattern) {
    if (from.isEmpty || pattern.isEmpty || pattern.length > from.length) {
      return from;
    }

    while (from.endsWith(pattern)) {
      from = from.substring(0, from.length - pattern.length);
    }
    return from;
  }

  static String trim(String from, String pattern) {
    return trimLeft(trimRight(from, pattern), pattern);
  }

  static showJoinedOptionsSheet(
      BuildContext context, Circle circle, VoidCallback unfollowCallBack) {
    AppAnalytics.logEvent(
        name: "joined_options_sheet_shown",
        parameters: {"circle": circle.name, "circle_id": circle.id});
    showModalBottomSheet(
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10.0),
                topRight: Radius.circular(10.0))),
        context: context,
        builder: (context) {
          return PopScope(
            onPopInvoked: (bool onPop) {
              if (onPop) {
                AppAnalytics.logEvent(
                    name: "joined_options_sheet_closed",
                    parameters: {
                      "circle": circle.name,
                      "circle_id": circle.id,
                      "source": 'back-button',
                    });
              }
            },
            child: Wrap(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(
                          top: 30.0, left: 30, bottom: 20),
                      child: Text(circle.name.isNotEmpty ? circle.name : '',
                          style: const TextStyle(
                              fontSize: 14, fontWeight: FontWeight.w600)),
                    ),
                    const Divider(thickness: 0.5, height: 0),
                    TextButton.icon(
                        onPressed: () {
                          AppAnalytics.logEvent(
                              name: "unfollow_button_click",
                              parameters: {
                                "circle": circle.name,
                                "circle_id": circle.id
                              });
                          Navigator.pop(context);
                          unfollowCallBack();
                          AppAnalytics.logEvent(
                              name: "joined_options_sheet_closed",
                              parameters: {
                                "circle": circle.name,
                                "circle_id": circle.id,
                                "source": 'unfollow_button',
                              });
                        },
                        icon: const Padding(
                          padding: EdgeInsets.only(left: 20.0),
                          child: Icon(
                            AppIcons.unfollowIcon,
                            color: Colors.black,
                            size: 20,
                          ),
                        ),
                        label: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          // padding: EdgeInsets.only(top: 10, bottom: 10),
                          child: Text(
                            context.getString(StringKey.leaveLabel),
                            style: const TextStyle(
                              color: Colors.black,
                              fontSize: 15,
                              height: 3,
                            ),
                          ),
                        )),
                    const Padding(
                      padding: EdgeInsets.only(bottom: 30.0),
                      child: Divider(thickness: 0.5, height: 0),
                    )
                  ],
                )
              ],
            ),
          );
        });
  }

  static Future<String> getInstallationSource() async {
    Source installationSource = await StoreChecker.getSource;

    String appInstallSource = "";
    switch (installationSource) {
      case Source.IS_INSTALLED_FROM_PLAY_STORE:
        // Installed from Play Store
        appInstallSource = "Play Store";
        break;
      case Source.IS_INSTALLED_FROM_PLAY_PACKAGE_INSTALLER:
        // Installed from Google Package installer
        appInstallSource = "Google Package installer";
        break;
      case Source.IS_INSTALLED_FROM_LOCAL_SOURCE:
        // Installed using adb commands or side loading or any cloud service
        appInstallSource = "Local Source";
        break;
      case Source.IS_INSTALLED_FROM_AMAZON_APP_STORE:
        // Installed from Amazon app store
        appInstallSource = "Amazon Store";
        break;
      case Source.IS_INSTALLED_FROM_HUAWEI_APP_GALLERY:
        // Installed from Huawei app store
        appInstallSource = "Huawei App Gallery";
        break;
      case Source.IS_INSTALLED_FROM_SAMSUNG_GALAXY_STORE:
        // Installed from Samsung app store
        appInstallSource = "Samsung Galaxy Store";
        break;
      case Source.IS_INSTALLED_FROM_SAMSUNG_SMART_SWITCH_MOBILE:
        // Installed from Samsung Smart Switch Mobile
        appInstallSource = "Samsung Smart Switch Mobile";
        break;
      case Source.IS_INSTALLED_FROM_XIAOMI_GET_APPS:
        // Installed from Xiaomi app store
        appInstallSource = "Xiaomi Get Apps";
        break;
      case Source.IS_INSTALLED_FROM_OPPO_APP_MARKET:
        // Installed from Oppo app store
        appInstallSource = "Oppo App Market";
        break;
      case Source.IS_INSTALLED_FROM_VIVO_APP_STORE:
        // Installed from Vivo app store
        appInstallSource = "Vivo App Store";
        break;
      case Source.IS_INSTALLED_FROM_OTHER_SOURCE:
        // Installed from other market store
        appInstallSource = "Other Source";
        break;
      case Source.IS_INSTALLED_FROM_APP_STORE:
        // Installed from app store
        appInstallSource = "App Store";
        break;
      case Source.IS_INSTALLED_FROM_TEST_FLIGHT:
        // Installed from Test Flight
        appInstallSource = "Test Flight";
        break;
      case Source.IS_INSTALLED_FROM_RU_STORE:
        // Installed from RU store
        appInstallSource = "RU Store";
        break;
      case Source.UNKNOWN:
        // Installed from Unknown source
        appInstallSource = "Unknown Source";
        break;
    }
    return appInstallSource;
  }

  // Device Information
  static DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

  static Future<bool> isAppRunningOnEmulator() async {
    final deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      return !androidInfo.isPhysicalDevice;
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      return !iosInfo.isPhysicalDevice;
    } else {
      // Web, Windows, macOS, Linux
      return false;
    }
  }

  static Future<String> getDeviceModel() async {
    String deviceModel = "";
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      deviceModel = androidInfo.model;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      deviceModel = iosInfo.utsname.machine;
    } else {
      deviceModel = "unknown";
    }
    return deviceModel;
  }

  static Future<String> getDeviceManufacturer() async {
    String deviceMake = "";
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      deviceMake = androidInfo.manufacturer;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      deviceMake = iosInfo.model;
    } else {
      deviceMake = "unknown";
    }
    return deviceMake;
  }

  static Future<String> getDeviceSOCDetails() async {
    String deviceSOCDetails = "";
    if (Platform.isAndroid) {
      deviceSOCDetails = await ProcessorInfoPlugin.getProcessorInfo();
    } else {
      deviceSOCDetails = "";
    }
    return deviceSOCDetails;
  }
}
