import 'dart:convert';

import 'package:hive_flutter/hive_flutter.dart';
import 'package:injectable/injectable.dart';
import 'package:praja/constants/hive_type_ids.dart';
import 'package:praja/models/post_preview.dart';
import 'package:praja/services/hive.dart';

part 'post_preview_store.g.dart';

@lazySingleton
class PostPreviewStore {
  final HiveDelegate hive;

  PostPreviewStore(this.hive);

  Future<void> put(PostPreview postPreview) async {
    final box = await _box();
    final value = PostPreviewValue(
      postPreviewSerialized: json.encode(postPreview.toJson()),
      updatedAt: DateTime.now().millisecondsSinceEpoch,
    );
    await box.put(postPreview.id, value);
  }

  Future<void> del(int postId) async {
    final box = await _box();
    box.delete(postId);
  }

  Future<PostPreviewValue?> get(int postId) async {
    final box = await _box();
    return box.get(postId);
  }

  Box<PostPreviewValue>? _backingFieldBox;

  Future<Box<PostPreviewValue>> _box() async {
    _backingFieldBox ??= await hive.openBox<PostPreviewValue>('post_preview');
    return _backingFieldBox!;
  }
}

@HiveType(typeId: HiveTypeId.postPreviewValue)
class PostPreviewValue extends HiveObject {
  @HiveField(0)
  final String postPreviewSerialized;

  @HiveField(1)
  final int updatedAt;

  PostPreviewValue({
    required this.postPreviewSerialized,
    required this.updatedAt,
  });

  bool get isStale {
    final now = DateTime.now().millisecondsSinceEpoch;
    return now - updatedAt > const Duration(minutes: 15).inMilliseconds;
  }

  @override
  String toString() {
    return 'PostPreviewValue{postPreviewSerialized: $postPreviewSerialized, updatedAt: $updatedAt}';
  }
}
