import 'package:permission_handler/permission_handler.dart';

class PermissionsService {
  /// Requests the users permission to read their contacts.
  Future<bool> requestContactsPermission() async {
    return await Permission.contacts.request().isGranted;
  }

  Future<PermissionStatus> requestContactsPermissionStatus() async {
    return await Permission.contacts.request();
  }

  /// Requests the users permission to read their location when the app is in use
  Future<bool> requestLocationPermission() async {
    return await Permission.location.serviceStatus.isEnabled &&
        await Permission.location.request().isGranted;
  }

  /// Requests the users permission to read their phone data
  Future<bool> requestPhonePermission() async {
    return await Permission.phone.serviceStatus.isEnabled &&
        await Permission.phone.request().isGranted;
  }

  Future<bool> hasLocationPermission() async {
    return await Permission.location.isGranted;
  }

  Future<bool> hasContactsPermission() async {
    return await Permission.contacts.isGranted;
  }

  Future<PermissionStatus> getContactsPermissionStatus() async {
    return await Permission.contacts.status;
  }

  Future<bool> hasPhonePermission() async {
    return await Permission.phone.isGranted;
  }

  Future<bool> checkAndRequestLocationPermission() async {
    bool active = await hasLocationPermission();

    if (!active) {
      return await requestLocationPermission();
    }

    return true;
  }

  Future<bool> checkAndRequestContactPermission() async {
    bool active = await hasContactsPermission();

    if (!active) {
      return await requestContactsPermission();
    }

    return true;
  }

  Future<bool> checkAndRequestPhonePermission() async {
    bool active = await hasPhonePermission();

    if (!active) {
      return await requestPhonePermission();
    }

    return true;
  }

  Future<PermissionStatus> getAndRequestContactPermissionStatus() async {
    PermissionStatus status = await getContactsPermissionStatus();

    if (!status.isGranted) {
      return await requestContactsPermissionStatus();
    }

    return PermissionStatus.denied;
  }

  Future<String> getNotificationPermissionStatus() async {
    PermissionStatus status = await Permission.notification.status;

    if (status.isGranted) {
      return "granted";
    } else if (status.isDenied) {
      return "denied";
    } else if (status.isPermanentlyDenied) {
      return "permanentlyDenied";
    } else if (status.isRestricted) {
      return "restricted";
    } else if (status.isLimited) {
      return "limited";
    } else {
      return "unknown";
    }
  }

  Future<String> getContactsPermissionStatusForSuperProperties() async {
    PermissionStatus status = await Permission.contacts.status;

    if (status.isGranted) {
      return "granted";
    } else if (status.isDenied) {
      return "denied";
    } else if (status.isPermanentlyDenied) {
      return "permanently_denied";
    } else if (status.isRestricted) {
      return "restricted";
    } else if (status.isLimited) {
      return "limited";
    } else {
      return "unknown";
    }
  }
}
