import 'dart:io';

import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';

abstract class AppUnreadBadge {
  void setCount(int count);
}

class AppUnreadBadgeNoOp implements AppUnreadBadge {
  @override
  void setCount(int count) {}
}

class IosAppUnreadBadge implements AppUnreadBadge {
  final MethodChannel _channel = const MethodChannel('app_unread_badge');

  @override
  void setCount(int count) {
    _channel.invokeMethod('setCount', count);
  }
}

@module
abstract class AppUnreadBadgeModule {
  @lazySingleton
  AppUnreadBadge provideAppUnreadBadgeImpl() {
    if (Platform.isIOS) {
      return IosAppUnreadBadge();
    } else {
      return AppUnreadBadgeNoOp();
    }
  }
}
