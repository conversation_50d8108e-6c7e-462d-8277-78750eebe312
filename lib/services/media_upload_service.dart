import 'dart:io';

import 'package:injectable/injectable.dart';
import 'package:path/path.dart' as p;
import 'package:dio/dio.dart';
import 'package:praja/exceptions/api_exception.dart';
import 'package:praja/models/cloud_media_file.dart';
import 'package:praja/models/v2/local_image.dart';
import 'package:praja/models/v2/local_video.dart';
import 'package:praja/network/network_constants.dart';
import 'package:praja/screens/create/createPage/bloc/create_bloc_states.dart';

@injectable
class MediaUploadService {
  @Named(nonRor)
  Dio httpClient;

  MediaUploadService(@Named(nonRor) this.httpClient);

  Future<List<CloudImageFile>> uploadPhotosToCloud({
    required List<NewPhoto> photos,
    required Function(int, int) onSendProgress,
    required String url,
  }) async {
    return await uploadImagesToCloud(
      images: photos.map((e) => e.toImageFile()).toList(),
      onSendProgress: onSendProgress,
      url: url,
    );
  }

  Future<List<CloudImageFile>> uploadImagesToCloud({
    required List<ImageFile> images,
    required Function(int, int) onSendProgress,
    required String url,
    bool disableMediaExpire = false,
  }) async {
    try {
      Map<String, dynamic> data = {};

      List<MultipartFile> files = [];
      for (var i = 0; i < images.length; i++) {
        files.add(await MultipartFile.fromFile(
          images[i].file.path,
          filename: p.basename(images[i].file.path),
        ));
      }
      data['photos'] = files;

      FormData formData = FormData.fromMap(data);

      final response = await httpClient.post(url,
          data: formData,
          options: Options(
            sendTimeout: const Duration(minutes: 10),
            receiveTimeout: const Duration(minutes: 1),
            responseType: ResponseType.json,
            headers: disableMediaExpire ? {'X-Media-Expire': '-1'} : {},
          ),
          onSendProgress: onSendProgress);

      List<CloudImageFile> cloudImageFiles = [];
      for (var i = 0; i < response.data['files'].length; i++) {
        final received = CloudImageFile.fromJson(response.data['files'][i]);
        final receivedWithDimensions = received.copyWith(
          width: images[i].width,
          height: images[i].height,
        );
        cloudImageFiles.add(receivedWithDimensions);
      }
      return cloudImageFiles;
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<CloudVideoFile> uploadVideoToCloud(
      {required LocalVideo video,
      required String url,
      required int durationSecs,
      required double bitrateMbps,
      required Function(int, int) onSendProgress,
      bool disableMediaExpire = false}) async {
    try {
      final localThumbnail = video.localThumbnail;
      final formData = FormData.fromMap({
        'thumbnail': localThumbnail != null
            ? await MultipartFile.fromFile(
                localThumbnail.path,
                filename: p.basename(localThumbnail.path),
              )
            : null,
        'video': await MultipartFile.fromFile(
          video.path,
          filename: p.basename(video.path),
        ),
        'duration_secs': durationSecs,
        'bitrate_mbps': bitrateMbps,
      });

      final response = await httpClient.post(
        url,
        data: formData,
        options: Options(
            responseType: ResponseType.json,
            receiveTimeout: const Duration(minutes: 1),
            headers: disableMediaExpire ? {'X-Media-Expire': '-1'} : {}),
        onSendProgress: onSendProgress,
      );

      final cloudMediaFile = CloudVideoFile.fromJson(response.data);

      return cloudMediaFile;
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }

  Future<CloudImageFile> removePosterPhotoBg({
    required String url,
    required ImageFile image,
    CancelToken? cancelToken,
  }) async {
    try {
      final formData = FormData.fromMap({
        'photo': await MultipartFile.fromFile(
          image.file.path,
          filename: p.basename(image.file.path),
        ),
      });

      final response = await httpClient.post(
        url,
        data: formData,
        options: Options(
          responseType: ResponseType.json,
          receiveTimeout: const Duration(minutes: 1),
        ),
        cancelToken: cancelToken,
      );

      final cloudMediaFile = CloudImageFile.fromJson(response.data);

      return cloudMediaFile;
    } on DioException catch (e) {
      throw ApiException.fromDioError(e);
    }
  }
}

class ImageFile {
  final File file;
  final int width;
  final int height;

  ImageFile({
    required this.file,
    required this.width,
    required this.height,
  });

  factory ImageFile.fromLocalImage(LocalImage localImage) {
    return ImageFile(
      file: File(localImage.path),
      width: localImage.width?.toInt() ?? 0,
      height: localImage.height?.toInt() ?? 0,
    );
  }

  @override
  String toString() {
    return 'ImageFile(file: $file, width: $width, height: $height)';
  }
}

extension on NewPhoto {
  ImageFile toImageFile() {
    return ImageFile(
      file: data,
      width: width,
      height: height,
    );
  }
}
