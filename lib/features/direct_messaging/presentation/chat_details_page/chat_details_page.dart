import 'package:flutter/material.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/core/ui/page.dart';
import 'package:praja/errors/ui/retry_ui.dart';
import 'package:praja/features/direct_messaging/models/ui_conversation.dart';
import 'package:praja/features/direct_messaging/presentation/chat_details_page/chat_details_page_view_model.dart';
import 'package:praja/features/direct_messaging/ui/channel_invite_icon.dart';
import 'package:praja/features/direct_messaging/ui/chat_user_block_dialog.dart';
import 'package:praja/features/direct_messaging/ui/member_ui.dart';
import 'package:praja/features/intl/intl.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/features/share/ui/share_sheet.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/circle_identity.dart';
import 'package:praja/presentation/app_icons.dart';
import 'package:praja/presentation/circle_display_picture.dart';
import 'package:praja/presentation/circle_official_badge.dart';
import 'package:praja/presentation/up_down_animated_switcher.dart';
import 'package:praja/presentation/user_avatar.dart';
import 'package:praja/screens/circles_v2/circle_item.dart';
import 'package:praja/styles.dart';
import 'package:praja/test/elevated_icon_label_button.dart';
import 'package:praja/utils/utils.dart';
import 'package:praja/utils/widgets/badge_strip_profile.dart';

import 'chat_members_page.dart';
import 'chat_members_page_view_model.dart';

class ChatDetailsPage extends BasePage {
  final String conversationId;
  final UIConversation? preloadConversation;

  const ChatDetailsPage({
    super.key,
    required this.conversationId,
    this.preloadConversation,
  });

  @override
  String get pageName => 'chat_detail';

  @override
  Map<String, dynamic> get pageParams => {
        'conversation_id': conversationId,
      };

  void _onUserClicked(
      BuildContext context, UIConversation conversation, int userId) {
    AppAnalytics.onClickedUserNameInChat(
        params: conversation.toAnalyticsParams());
    Utils.openUserPage(
      context,
      userId,
      source: "chat_details_page",
    );
  }

  Widget _oneOneInfo(
      BuildContext context, UIOneToOneConversation conversation) {
    final badge = conversation.otherUserIdentity.badge;
    return Align(
      alignment: Alignment.center,
      child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              GestureDetector(
                  onTap: () {
                    final photo = conversation.otherUserIdentity.photo;
                    if (photo != null) {
                      AppAnalytics.onClickedUserPhotoInChat(
                        params: conversation.toAnalyticsParams(),
                      );
                      Utils.openUserPhoto(context, photo);
                    }
                  },
                  child: UserAvatar.fromIdentity(
                    conversation.otherUserIdentity,
                    size: 140,
                  )),
              const SizedBox(height: 4),
              GestureDetector(
                  onTap: () {
                    _onUserClicked(context, conversation,
                        conversation.otherUserIdentity.id);
                  },
                  child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      child: Text(conversation.otherUserIdentity.name,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: _nameTextStyle))),
              if (badge != null) const SizedBox(height: 4),
              if (badge != null)
                BadgeStripProfile(badge, height: 20, fontSize: 12),
            ],
          )),
    );
  }

  Widget _channelInfo(
      BuildContext context, UIChannelConversation conversation) {
    final verified = conversation.circleIdentity.verified;
    return Align(
        alignment: Alignment.center,
        child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                GestureDetector(
                    onTap: () {
                      final photo = conversation.circleIdentity.profilePhoto;
                      if (photo != null) {
                        AppAnalytics.onClickedCirclePhotoInChat(
                          params: conversation.toAnalyticsParams(),
                        );
                        Utils.openUserPhoto(context, photo);
                      }
                    },
                    child: CircleDisplayPicture.fromCircleIdentity(
                      conversation.circleIdentity,
                      size: 140,
                    )),
                const SizedBox(height: 4),
                GestureDetector(
                    onTap: () {
                      AppAnalytics.onClickedCircleNameInChat(
                          params: conversation.toAnalyticsParams());
                      Navigator.of(context).push(
                        MaterialPageRoute(builder: (_) {
                          return CircleV2Item(
                            id: conversation.circleIdentity.id,
                            source: "chat_details_page",
                          );
                        }),
                      );
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      child: Text(conversation.circleIdentity.name,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: _nameTextStyle),
                    )),
                if (verified) const SizedBox(height: 4),
                if (verified)
                  CircleOfficialBadge.channel(
                    height: 28,
                  ),
                const SizedBox(height: 4),
                Text(
                  // ignore: avoid_hardcoded_strings_in_ui
                  "${(conversation.circleIdentity.membersCount).toDisplayFormat(usedAsPrefix: true)} ${context.getString(StringKey.circleMembersLabel)}",
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.normal,
                    color: Colors.grey.shade600,
                  ),
                ),
                if (conversation.circleIdentity.description != null &&
                    conversation.circleIdentity.description!.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 8.0, horizontal: 12),
                    child: Text(
                      conversation.circleIdentity.description!,
                      maxLines: 4,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.normal,
                        height: 1.2,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
              ],
            )));
  }

  Widget _privateGroupInfo(
      BuildContext context, UIPrivateGroupConversation conversation) {
    final verified = conversation.circleIdentity.verified;
    return Align(
        alignment: Alignment.center,
        child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                GestureDetector(
                    onTap: () {
                      final photo = conversation.circleIdentity.profilePhoto;
                      if (photo != null) {
                        AppAnalytics.onClickedCirclePhotoInChat(
                          params: conversation.toAnalyticsParams(),
                        );
                        Utils.openUserPhoto(context, photo);
                      }
                    },
                    child: CircleDisplayPicture.fromCircleIdentity(
                      conversation.circleIdentity,
                      size: 140,
                    )),
                const SizedBox(height: 4),
                GestureDetector(
                    onTap: () {
                      AppAnalytics.onClickedCircleNameInChat(
                          params: conversation.toAnalyticsParams());
                      Navigator.of(context).push(
                        MaterialPageRoute(builder: (_) {
                          return CircleV2Item(
                            id: conversation.circleIdentity.id,
                            source: "chat_details_page",
                          );
                        }),
                      );
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      child: Text(conversation.circleIdentity.name,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: _nameTextStyle),
                    )),
                if (verified) const SizedBox(height: 4),
                if (verified)
                  CircleOfficialBadge.group(
                    height: 28,
                  ),
                const SizedBox(height: 4),
                Text(
                  // ignore: avoid_hardcoded_strings_in_ui
                  "${(conversation.circleIdentity.membersCount).toDisplayFormat(usedAsPrefix: true)} ${context.getString(StringKey.circleMembersLabel)}",
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.normal,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            )));
  }

  Widget _info(BuildContext context, UIConversation conversation) {
    if (conversation is UIOneToOneConversation) {
      return _oneOneInfo(context, conversation);
    } else if (conversation is UIChannelConversation) {
      return _channelInfo(context, conversation);
    } else if (conversation is UIPrivateGroupConversation) {
      return _privateGroupInfo(context, conversation);
    } else {
      return const SizedBox();
    }
  }

  Future<bool?> _confirmLeaveChannel(
      BuildContext context, UIChannelConversation conversation) async {
    final bool? result = await showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            title: Text(
              context.getString(StringKey.leaveChannelConfirmationText,
                  parameters: {'name': conversation.circleIdentity.name}),
              textScaler: const TextScaler.linear(1.0),
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black,
                fontWeight: FontWeight.w500,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(false);
                },
                child: Text(
                  context.getString(StringKey.noAlternateLabel),
                  textScaler: const TextScaler.linear(1.0),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(true);
                },
                child: Text(
                  context.getString(StringKey.leaveLabel),
                  textScaler: const TextScaler.linear(1.0),
                  style: const TextStyle(color: _negativeTextColor),
                ),
              ),
            ],
          );
        });
    return result;
  }

  List<Widget> _negativeOptions(BuildContext context,
      ChatDetailsPageSuccessState state, ChatDetailsViewModel viewModel) {
    final conversation = state.conversation;
    return [
      if (conversation is UIOneToOneConversation) ...[
        const SizedBox(height: 16),
        const Divider(),
        InkWell(
            onTap: () async {
              if (state.isReceiverBlocked) {
                viewModel.onUnblockClicked();
              } else {
                AppAnalytics.onClickedBlockInChat(
                  userId: conversation.otherUserIdentity.id,
                  params: state.conversation.toAnalyticsParams(),
                  source: "chat_detail_page",
                );
                final blockResult = await showDialog(
                    context: context,
                    builder: (context) => ChatUserBlockDialog(
                        userId: conversation.otherUserIdentity.id,
                        userName: conversation.otherUserIdentity.name));
                if (blockResult != null) {
                  viewModel.onUserBlockStateChange();
                }
              }
            },
            child: Padding(
                padding: const EdgeInsets.only(
                    left: 16, right: 16, top: 12, bottom: 4),
                child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const Icon(Icons.block_outlined,
                          size: 24, color: _negativeTextColor),
                      const SizedBox(width: 16),
                      Text(
                          state.isReceiverBlocked
                              ? context.getString(StringKey.unblockLabel)
                              : context.getString(StringKey.blockLabel),
                          style: _negativeTextStyle),
                      const Expanded(child: SizedBox()),
                    ]))),
        Padding(
            padding: const EdgeInsets.only(right: 16, left: 56),
            child: Text(
                context.getString(StringKey.blockedUserCannotMessageYouText),
                style: _dimTextStyle)),
      ],
      if (conversation is UIChannelConversation && state.isParticipant) ...[
        const SizedBox(height: 16),
        const Divider(),
        InkWell(
            onTap: () async {
              final bool? result =
                  await _confirmLeaveChannel(context, conversation);
              if (result != null && result) {
                await viewModel.onChannelLeave();
              }
            },
            child: Padding(
                padding: const EdgeInsets.only(
                    left: 16, right: 16, top: 12, bottom: 4),
                child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const Icon(AppIcons.unfollowIcon,
                          size: 24, color: _negativeTextColor),
                      const SizedBox(width: 16),
                      Text(context.getString(StringKey.leaveChannelText),
                          style: _negativeTextStyle),
                      const Expanded(child: SizedBox()),
                    ])))
      ],
    ];
  }

  Widget _title(BuildContext context, ChatDetailsPageState state) {
    final conversation = preloadConversation ??
        (state is ChatDetailsPageSuccessState ? state.conversation : null);
    if (conversation == null) {
      return const SizedBox();
    }
    if (conversation is UIOneToOneConversation) {
      return Text(context.getString(StringKey.chatSettingsLabel));
    } else if (conversation is UIChannelConversation) {
      return Text(context.getString(StringKey.channelSettingsLabel));
    } else {
      return const SizedBox();
    }
  }

  void _onViewCircleTapped(BuildContext context, UIConversation conversation) {
    final CircleIdentity circleIdentity;
    if (conversation is UIChannelConversation) {
      circleIdentity = conversation.circleIdentity;
    } else if (conversation is UIPrivateGroupConversation) {
      circleIdentity = conversation.circleIdentity;
    } else {
      return;
    }

    AppAnalytics.onClickedViewCircleInChat(
        params: conversation.toAnalyticsParams());
    Navigator.of(context).push(
      MaterialPageRoute(builder: (_) {
        return CircleV2Item(
          id: circleIdentity.id,
          source: "chat_details_page",
        );
      }),
    );
  }

  void _onViewProfileTapped(
      BuildContext context, UIOneToOneConversation conversation) {
    AppAnalytics.onClickedViewProfileInChat(
        params: conversation.toAnalyticsParams());
    Utils.openUserPage(
      context,
      conversation.otherUserIdentity.id,
      source: "chat_details_page",
    );
  }

  void _onInviteTapped(
      BuildContext context, UIChannelConversation conversation) {
    AppAnalytics.onInviteToChannelClicked(
        params: conversation.toAnalyticsParams(), source: 'chat_details_page');
    ShareSheet.show(context, SharedEntity.channel(conversation.circleIdentity));
  }

  Widget _options(
    BuildContext context,
    ChatDetailsPageSuccessState state,
    ChatDetailsViewModel viewModel,
  ) {
    final conversation = state.conversation;

    const labelStyle = TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w500,
    );
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: conversation is UIChannelConversation ? 12 : 96),
      child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          mainAxisSize: MainAxisSize.max,
          children: [
            if (conversation is UIChannelConversation)
              ElevatedIconLabelButton(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  icon: const Padding(
                    padding: EdgeInsets.all(12.0),
                    child: ChannelInviteIcon(size: 24, color: Colors.black),
                  ),
                  label: Text(context.getString(StringKey.inviteLabel),
                      style: labelStyle),
                  onPressed: () => _onInviteTapped(context, conversation)),
            if (conversation is UIOneToOneConversation)
              ElevatedIconLabelButton(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                icon: const Padding(
                  padding: EdgeInsets.all(12.0),
                  child: Icon(Icons.person_outlined, size: 24),
                ),
                label: Text(context.getString(StringKey.profileLabel),
                    style: labelStyle),
                onPressed: () => _onViewProfileTapped(context, conversation),
              ),
            if (conversation is UIChannelConversation &&
                conversation.circleIdentity.type != 'sub')
              ElevatedIconLabelButton(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  icon: const Padding(
                    padding: EdgeInsets.all(12.0),
                    child: Icon(Icons.groups_outlined, size: 24),
                  ),
                  label: Text(
                      context.getPluralizedString(StringKey.circleLabel, 1),
                      style: labelStyle),
                  onPressed: () => _onViewCircleTapped(context, conversation)),
            if (conversation is UIPrivateGroupConversation &&
                conversation.circleIdentity.type != 'sub')
              ElevatedIconLabelButton(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  icon: const Padding(
                    padding: EdgeInsets.all(12.0),
                    child: Icon(Icons.groups_outlined, size: 24),
                  ),
                  label: Text(
                      context.getPluralizedString(StringKey.circleLabel, 1),
                      style: labelStyle),
                  onPressed: () => _onViewCircleTapped(context, conversation)),
            ElevatedIconLabelButton(
                padding: const EdgeInsets.symmetric(horizontal: 9),
                icon: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: UpDownAnimatedSwitcher(
                      value: !conversation.isMuted,
                      child: conversation.isMuted
                          ? const Icon(Icons.notifications_off_outlined,
                              color: Color(0xFF757575), size: 22)
                          : const Icon(Icons.notifications_on_outlined,
                              size: 22)),
                ),
                label: Text(
                    context.getPluralizedString(StringKey.notificationLabel, 0),
                    style: labelStyle),
                onPressed: () =>
                    viewModel.onMuteChanged(!conversation.isMuted)),
          ]),
    );
  }

  Widget _conversationMembersList(
      {required BuildContext context, required UIConversation conversation}) {
    const initialMembersCount = 4;
    final circleId = conversation is UIChannelConversation
        ? conversation.circleIdentity.id
        : conversation is UIPrivateGroupConversation
            ? conversation.circleIdentity.id
            : -1;
    if (circleId == -1 || conversation is UIOneToOneConversation) {
      return const SizedBox();
    }
    final chatMembersViewModel = context
        .getViewModel<ChatMembersPageViewModel>()
      ..initialise(circleId, initialMembersCount: initialMembersCount);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(height: 1),
        const SizedBox(height: 16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(context.getString(StringKey.circleMembersLabel),
              style: _headerTextStyle),
        ),
        const SizedBox(height: 10),
        const Divider(height: 1),
        LiveDataBuilder<ChatMembersPageState>(
            liveData: chatMembersViewModel.state,
            builder: (context, state) {
              if (state is ChatMembersPageLoadingState) {
                return const SizedBox(
                    height: 300,
                    child: Center(child: CircularProgressIndicator()));
              }
              if (state is ChatMembersPageSuccessState) {
                return Column(
                  children: [
                    for (final member in state.members)
                      Column(children: [
                        MemberUI(
                            label: member.label,
                            type: member.type,
                            user: member.user,
                            onTap: () {
                              _onUserClicked(
                                  context, conversation, member.user.id);
                            }),
                        const Divider(height: 1, indent: 25),
                      ]),
                    state.isLastPage
                        ? const SizedBox()
                        : InkWell(
                            onTap: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(builder: (_) {
                                  return ChatMembersPage(
                                      conversation: conversation,
                                      existingMembers: state.members);
                                }),
                              );
                            },
                            child: Padding(
                              padding: const EdgeInsets.only(top: 16, left: 80),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        top: 2.0, right: 5),
                                    child: Text(
                                      context.getString(
                                          StringKey.viewRemainingMembersLabel),
                                      style: TextStyle(
                                          fontSize: 16,
                                          color:
                                              Theme.of(context).primaryColor),
                                    ),
                                  ),
                                  Icon(Icons.arrow_circle_right,
                                      size: 24,
                                      color: Theme.of(context).primaryColor),
                                ],
                              ),
                            ),
                          )
                  ],
                );
              }
              if (state is ChatMembersPageErrorState) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: RetryUI(
                    onRetry: () => chatMembersViewModel
                        .getFirstNMembers(initialMembersCount),
                    source: 'chat_details_page',
                    displayMessage: state.message,
                  ),
                );
              }
              return const SizedBox();
            }),
      ],
    );
  }

  Widget _getBody({
    required BuildContext context,
    required ChatDetailsViewModel viewModel,
    required ChatDetailsPageState state,
  }) {
    if (state == ChatDetailsPageState.loading) {
      return const Center(child: CircularProgressIndicator());
    } else if (state == ChatDetailsPageState.noConversation) {
      Navigator.of(context).pop();
      return const SizedBox();
    } else if (state is ChatDetailsPageSuccessState) {
      final conversation = state.conversation;
      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _info(context, conversation),
            const SizedBox(height: 8),
            _options(context, state, viewModel),
            const SizedBox(height: 8),
            if (conversation is UIChannelConversation ||
                conversation is UIPrivateGroupConversation)
              _conversationMembersList(
                  context: context, conversation: conversation),
            ..._negativeOptions(context, state, viewModel),
          ],
        ),
      );
    } else {
      return const SizedBox();
    }
  }

  @override
  Widget buildContent(BuildContext context) {
    final viewModel = context.viewModel(conversationId);
    return EventListener(
      eventQueue: viewModel.eventQueue,
      onEvent: (_, event) async {
        if (event is ChatDetailsPageErrorEvent) {
          Utils.showToast(event.message);
        } else if (event is ChannelLeaveEvent) {
          Navigator.of(context).pop();
        }
      },
      child: LiveDataBuilder<ChatDetailsPageState>(
          liveData: viewModel.state,
          builder: (context, state) {
            return Scaffold(
              appBar: AppBar(
                title: _title(context, state),
              ),
              body: _getBody(
                context: context,
                viewModel: viewModel,
                state: state,
              ),
            );
          }),
    );
  }
}

extension _ViewModelProvider on BuildContext {
  ChatDetailsViewModel viewModel(String conversationId) =>
      getViewModel<ChatDetailsViewModel>()..init(conversationId);
}

const Color _textColor = Color(0xFF131313);
const Color _negativeTextColor = Color(0xFFF44A4A);

const TextStyle _headerTextStyle = TextStyle(
  fontSize: 18,
  fontWeight: FontWeight.w500,
  color: _textColor,
);

const TextStyle _negativeTextStyle = TextStyle(
  fontSize: 18,
  fontWeight: FontWeight.w400,
  color: _negativeTextColor,
);

const TextStyle _descriptionTextStyle = TextStyle(
  fontSize: 15,
  fontWeight: FontWeight.w400,
  color: _textColor,
);

const TextStyle _dimTextStyle = TextStyle(
  fontSize: 13,
  fontWeight: FontWeight.w500,
  color: Styles.dimTextColor,
  height: 1.25,
);

const TextStyle _nameTextStyle = TextStyle(
  fontSize: 20,
  fontWeight: FontWeight.w600,
  color: _textColor,
);

class SwitchSetting extends StatelessWidget {
  final bool isOn;
  final IconData onIcon;
  final IconData offIcon;
  final String title;
  final String description;
  final Function(bool)? onChanged;

  const SwitchSetting({
    super.key,
    required this.isOn,
    required this.onIcon,
    required this.offIcon,
    required this.title,
    required this.description,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: _headerTextStyle),
            if (description.isNotEmpty) const SizedBox(height: 8),
            if (description.isNotEmpty)
              Text(description, style: _descriptionTextStyle),
          ],
        ),
        const Expanded(child: SizedBox()),
        // Text(offLabel, style: _descriptionTextStyle),
        // const SizedBox(width: 4),
        InkWell(
            onTap: () {
              if (isOn) {
                onChanged?.call(false);
              }
            },
            child: Icon(offIcon,
                size: 20,
                color: !isOn
                    ? Theme.of(context).primaryColor
                    : Styles.dimIconColor)),
        const SizedBox(width: 4),
        Switch(
          value: isOn,
          onChanged: onChanged,
        ),
        const SizedBox(width: 4),
        InkWell(
            onTap: () {
              if (!isOn) {
                onChanged?.call(true);
              }
            },
            child: Icon(onIcon,
                size: 20,
                color: isOn
                    ? Theme.of(context).primaryColor
                    : Styles.dimIconColor)),
        // const SizedBox(width: 4),
        // Text(onLabel, style: _descriptionTextStyle),
      ],
    );
  }
}
