// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attachment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Attachment _$AttachmentFromJson(Map<String, dynamic> json) => Attachment(
      backingFieldType: json['type'] as String,
      backingFieldAttachmentData:
          json['attachmentData'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$AttachmentToJson(Attachment instance) =>
    <String, dynamic>{
      'type': instance.backingFieldType,
      'attachmentData': instance.backingFieldAttachmentData,
    };

AssetImageAttachmentData _$AssetImageAttachmentDataFromJson(
        Map<String, dynamic> json) =>
    AssetImageAttachmentData(
      path: json['path'] as String,
      width: (json['width'] as num).toInt(),
      height: (json['height'] as num).toInt(),
    );

Map<String, dynamic> _$AssetImageAttachmentDataToJson(
        AssetImageAttachmentData instance) =>
    <String, dynamic>{
      'path': instance.path,
      'width': instance.width,
      'height': instance.height,
    };

AssetVideoAttachmentData _$AssetVideoAttachmentDataFromJson(
        Map<String, dynamic> json) =>
    AssetVideoAttachmentData(
      path: json['path'] as String,
      width: (json['width'] as num).toInt(),
      height: (json['height'] as num).toInt(),
      durationInSecs: (json['durationInSecs'] as num).toInt(),
      thumbnailPath: json['thumbnailPath'] as String?,
    );

Map<String, dynamic> _$AssetVideoAttachmentDataToJson(
    AssetVideoAttachmentData instance) {
  final val = <String, dynamic>{
    'path': instance.path,
    'width': instance.width,
    'height': instance.height,
    'durationInSecs': instance.durationInSecs,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('thumbnailPath', instance.thumbnailPath);
  return val;
}

ImageAttachmentData _$ImageAttachmentDataFromJson(Map<String, dynamic> json) =>
    ImageAttachmentData(
      url: json['assetUrl'] as String,
      backingFieldWidth: (json['width'] as num?)?.toInt(),
      backingFieldHeight: (json['height'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ImageAttachmentDataToJson(ImageAttachmentData instance) {
  final val = <String, dynamic>{
    'assetUrl': instance.url,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('width', instance.backingFieldWidth);
  writeNotNull('height', instance.backingFieldHeight);
  return val;
}

VideoAttachmentData _$VideoAttachmentDataFromJson(Map<String, dynamic> json) =>
    VideoAttachmentData(
      url: json['assetUrl'] as String,
      backingFieldWidth: (json['width'] as num?)?.toInt(),
      backingFieldHeight: (json['height'] as num?)?.toInt(),
      backingFieldDurationInSecs: (json['durationInSeconds'] as num?)?.toInt(),
      thumbnailUrl: json['thumbnailUrl'] as String?,
    );

Map<String, dynamic> _$VideoAttachmentDataToJson(VideoAttachmentData instance) {
  final val = <String, dynamic>{
    'assetUrl': instance.url,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('width', instance.backingFieldWidth);
  writeNotNull('height', instance.backingFieldHeight);
  writeNotNull('durationInSeconds', instance.backingFieldDurationInSecs);
  writeNotNull('thumbnailUrl', instance.thumbnailUrl);
  return val;
}

PostAttachmentData _$PostAttachmentDataFromJson(Map<String, dynamic> json) =>
    PostAttachmentData(
      postId: json['postId'] as String,
    );

Map<String, dynamic> _$PostAttachmentDataToJson(PostAttachmentData instance) =>
    <String, dynamic>{
      'postId': instance.postId,
    };

MessageAttachmentData _$MessageAttachmentDataFromJson(
        Map<String, dynamic> json) =>
    MessageAttachmentData(
      messageId: json['messageId'] as String,
    );

Map<String, dynamic> _$MessageAttachmentDataToJson(
        MessageAttachmentData instance) =>
    <String, dynamic>{
      'messageId': instance.messageId,
    };

CreatePosterAttachmentData _$CreatePosterAttachmentDataFromJson(
        Map<String, dynamic> json) =>
    CreatePosterAttachmentData(
      params: json['params'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$CreatePosterAttachmentDataToJson(
        CreatePosterAttachmentData instance) =>
    <String, dynamic>{
      'params': instance.params,
    };

ChannelAttachmentData _$ChannelAttachmentDataFromJson(
        Map<String, dynamic> json) =>
    ChannelAttachmentData(
      circleId: json['circleId'] as String,
    );

Map<String, dynamic> _$ChannelAttachmentDataToJson(
        ChannelAttachmentData instance) =>
    <String, dynamic>{
      'circleId': instance.circleId,
    };
