import 'package:flutter/material.dart';
import 'package:flutter_vibrate/flutter_vibrate.dart';
import 'package:intl/intl.dart';
import 'package:praja/features/direct_messaging/database/entities/db_conversation.dart';
import 'package:praja/features/direct_messaging/database/entities/db_message.dart';
import 'package:praja/features/direct_messaging/models/action_data.dart';
import 'package:praja/features/direct_messaging/models/ui_conversation.dart';
import 'package:praja/features/direct_messaging/models/ui_message.dart';
import 'package:praja/features/direct_messaging/ui/channel_message_layout.dart';
import 'package:praja/features/direct_messaging/ui/message_content_ui.dart';
import 'package:praja/features/direct_messaging/ui/regular_message_layout.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/presentation/praja_icons.dart';
import 'package:praja/styles.dart';

import 'conversation_created_ui.dart';

typedef MessageCallback = Function(UIMessage message);
typedef ReplyCallback = Function(UIMessage message, String interaction);

class MessageUI extends StatelessWidget {
  final UIMessage message;
  final UIMessage? prev;
  final UIMessage? next;
  final MessageCallback? onForward;
  final MessageCallback? onRetry;
  final MessageCallback? onDelete;
  final MessageCallback? onCopy;
  final MessageCallback? onGotoLink;
  final VoidCallback? onSettingsTap;
  final ReplyCallback? onReply;
  final PostPreviewCallback? onPostPreviewTap;
  final PostPreviewChildCallback? onPostPreviewChildTap;
  final PostPreviewImageCallback? onPostPreviewImageTap;
  final PostPreviewCallback? onPostPreviewVideoTap;
  final ImageAttachmentCallback? onImageAttachmentTap;
  final VideoAttachmentCallback? onVideoAttachmentTap;
  final ChannelPreviewCallback? onChannelPreviewTap;
  final MessageCallback? onLinkPreviewTap;
  final Function(String messageId)? onReplyTap;

  const MessageUI(
      {super.key,
      required this.message,
      this.onForward,
      this.onRetry,
      this.onDelete,
      this.onCopy,
      this.onGotoLink,
      this.onSettingsTap,
      this.onReply,
      this.onReplyTap,
      this.onPostPreviewTap,
      this.onPostPreviewChildTap,
      this.onPostPreviewImageTap,
      this.onPostPreviewVideoTap,
      this.onImageAttachmentTap,
      this.onVideoAttachmentTap,
      this.onChannelPreviewTap,
      this.onLinkPreviewTap,
      this.prev,
      this.next});

  Future<MessageOption> showOptions(
      BuildContext context, List<MessageOption> options) async {
    final result = await showModalBottomSheet<MessageOption>(
        context: context,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16), topRight: Radius.circular(16))),
        builder: (context) {
          return Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                for (var index = 0; index < options.length; index++) ...[
                  InkWell(
                      onTap: () => Navigator.of(context).pop(options[index]),
                      child: ListTile(
                        leading: Icon(options[index].icon,
                            color: options[index].color),
                        title: Text(options[index].title,
                            style: TextStyle(color: options[index].color)),
                      )),
                  if (index < options.length - 1) const Divider(height: 1)
                ]
              ],
            ),
          );
        });
    return result ?? MessageOption.none;
  }

  Future<void> _onMessageLongPressed(BuildContext context) async {
    final msg = message;
    if (msg is! UINormalMessage) return;
    final options = msg.options;
    if (options.isNotEmpty) {
      final chosenOption = await showOptions(context, options);
      switch (chosenOption) {
        case MessageOption.forward:
          onForward?.call(msg);
          break;
        case MessageOption.retry:
          onRetry?.call(msg);
          break;
        case MessageOption.delete:
          onDelete?.call(msg);
          break;
        case MessageOption.reply:
          onReply?.call(msg, "option_selected");
          break;
        case MessageOption.copy:
          onCopy?.call(msg);
          break;
        case MessageOption.goToLink:
          onGotoLink?.call(msg);
          break;
        case MessageOption.none:
          // do nothing
          break;
      }
    }
  }

  Widget _buildRegularMessageLayout(BuildContext context, UINormalMessage msg) {
    final sameDayAsPrev = isOfSameDay(message, prev);
    final sameSenderAsPrev = isOfSameSender(message, prev);
    final isWithinAMinToNext = isWithinAMinute(message, next);
    final sameSenderAsNext = isOfSameSender(message, next);
    final sameStatusAsNext = isOfSameStatus(message, next);
    final showAvatar = !(sameDayAsPrev && sameSenderAsPrev);
    var showFooter = !(isWithinAMinToNext && sameSenderAsNext);
    if (msg.isSender) {
      showFooter =
          showFooter || !sameStatusAsNext || msg.status == MessageStatus.failed;
    }

    return RegularMessageLayout(
        message: msg,
        showAvatar: showAvatar,
        showFooter: showFooter,
        onReveal: () {
          onReply?.call(msg, "swiped");
        },
        onLongPress: () {
          Vibrate.feedback(FeedbackType.impact);
          _onMessageLongPressed(context);
        },
        onForward: () {
          onForward?.call(msg);
        },
        callbacks: MessageContentCallbacks(
          onReplyTap: onReplyTap,
          onPostPreviewTap: onPostPreviewTap,
          onPostPreviewChildTap: onPostPreviewChildTap,
          onPostPreviewImageTap: onPostPreviewImageTap,
          onPostPreviewVideoTap: onPostPreviewVideoTap,
          onImageAttachmentTap: onImageAttachmentTap,
          onVideoAttachmentTap: onVideoAttachmentTap,
          onChannelPreviewTap: onChannelPreviewTap,
          onLinkPreviewTap: () {
            onLinkPreviewTap?.call(msg);
          },
        ));
  }

  Widget _buildChannelMessageLayout(BuildContext context, UINormalMessage msg) {
    final UIChannelConversation conversation =
        msg.conversation as UIChannelConversation;
    return ChannelMessageLayout(
      message: msg,
      showSender: conversation.canSendMessage(),
      callbacks: MessageContentCallbacks(
        onReplyTap: onReplyTap,
        onPostPreviewTap: onPostPreviewTap,
        onPostPreviewChildTap: onPostPreviewChildTap,
        onPostPreviewImageTap: onPostPreviewImageTap,
        onPostPreviewVideoTap: onPostPreviewVideoTap,
        onImageAttachmentTap: onImageAttachmentTap,
        onVideoAttachmentTap: onVideoAttachmentTap,
        onChannelPreviewTap: onChannelPreviewTap,
        onLinkPreviewTap: () {
          onLinkPreviewTap?.call(msg);
        },
      ),
      onForward: () {
        onForward?.call(msg);
      },
      onLongPress: () {
        Vibrate.feedback(FeedbackType.impact);
        _onMessageLongPressed(context);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // type inference is not working with the class field
    final msg = message;
    if (msg is UINormalMessage && msg.conversation is UIChannelConversation) {
      return _buildChannelMessageLayout(context, msg);
    } else if (msg is UINormalMessage) {
      return _buildRegularMessageLayout(context, msg);
    } else if (msg is UIActionMessage) {
      if (msg.actionData.type == ActionType.conversationCreated) {
        return ConversationCreatedUI(
            message: msg, onSettingsTap: onSettingsTap);
      }
    }

    return const SizedBox();
  }

  static Widget buildSeparator(
      BuildContext context, UIMessage cur, UIMessage prev,
      {bool showUnread = false}) {
    final curTime = cur.timestamp;
    final prevTime = prev.timestamp;
    final differentDay = curTime.year != prevTime.year ||
        curTime.month != prevTime.month ||
        curTime.day != prevTime.day;
    DateFormat format = DateFormat.MMMMd("te_IN");
    if (curTime.year != prevTime.year) {
      format = DateFormat.yMMMMd("te_IN");
    }
    if (showUnread && differentDay) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 8),
          _unreadSeparator(cur.conversation.type, context: context),
          _dateSeparator(curTime, format)
        ],
      );
    } else if (showUnread) {
      return Column(children: [
        _unreadSeparator(cur.conversation.type, context: context),
        const SizedBox(height: 8)
      ]);
    } else if (differentDay) {
      return _dateSeparator(curTime, format);
    } else if (cur is UINormalMessage && prev is UINormalMessage) {
      if (cur.isSender == prev.isSender) {
        return const SizedBox(height: 2);
      }
    }

    return const SizedBox(height: 8);
  }

  static Widget _dateSeparator(DateTime date, DateFormat format) {
    return Container(
        margin: const EdgeInsets.symmetric(vertical: 16),
        child: Row(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Expanded(child: Divider(color: Styles.dimTextColor)),
              const SizedBox(width: 12),
              Text(format.format(date),
                  style: const TextStyle(
                    color: Styles.dimTextColor,
                    fontSize: 14,
                  )),
              const SizedBox(width: 12),
              const Expanded(child: Divider(color: Styles.dimTextColor)),
            ]));
  }

  static Widget _unreadSeparator(ConversationType conversationType,
      {required BuildContext context}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Expanded(child: Divider(color: Styles.circleIndigo)),
          const SizedBox(width: 12),
          Text(
            conversationType == ConversationType.channel
                ? context.getString(StringKey.latestUpdatesLabel)
                : context.getString(StringKey.unreadLabel),
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Styles.circleIndigo,
            ),
          ),
          const SizedBox(width: 4),
          const Padding(
            padding: EdgeInsets.only(bottom: 4.0),
            child: Icon(
              Icons.south,
              size: 12,
              color: Styles.circleIndigo,
            ),
          ),
          const SizedBox(width: 12),
          const Expanded(child: Divider(color: Styles.circleIndigo)),
        ],
      ),
    );
  }

  static bool isOfSameDay(UIMessage message, UIMessage? other) {
    if (other == null) return false;

    final curTime = message.timestamp;
    final prevTime = other.timestamp;
    if (curTime.year != prevTime.year ||
        curTime.month != prevTime.month ||
        curTime.day != prevTime.day) {
      return false;
    }

    return true;
  }

  static bool isWithinAMinute(UIMessage message, UIMessage? next) {
    if (next == null) return false;

    final curTime = message.timestamp;
    final nextTime = next.timestamp;
    return nextTime.difference(curTime).inMinutes < 1;
  }

  static bool isOfSameSender(UIMessage message, UIMessage? other) {
    if (other == null) return false;

    if (message is UINormalMessage && other is UINormalMessage) {
      if (message.senderId == other.senderId) {
        return true;
      }
    }

    return false;
  }

  static bool isOfSameStatus(UIMessage message, UIMessage? other) {
    if (other == null) return false;

    if (message is UINormalMessage && other is UINormalMessage) {
      if (message.status == other.status) {
        return true;
      }
    }

    return false;
  }

  static double bubbleWidth(BuildContext context) {
    return MediaQuery.of(context).size.width - 96;
  }
}

enum MessageOption { forward, retry, reply, delete, copy, goToLink, none }

extension on MessageOption {
  String get title {
    switch (this) {
      case MessageOption.forward:
        return "ఫార్వర్డ్";
      case MessageOption.retry:
        return "మల్లి ప్రయత్నించండి";
      case MessageOption.delete:
        return "డిలీట్";
      case MessageOption.copy:
        return "కాపీ";
      case MessageOption.reply:
        return "రిప్లై";
      case MessageOption.goToLink:
        return "వెబ్సైటుకు వెళ్ళండి";
      case MessageOption.none:
        return "";
    }
  }

  IconData get icon {
    switch (this) {
      case MessageOption.forward:
        return PrajaIcons.forward_message;
      case MessageOption.retry:
        return Icons.refresh;
      case MessageOption.reply:
        return PrajaIcons.replyto_message;
      case MessageOption.delete:
        return Icons.delete_outlined;
      case MessageOption.copy:
        return Icons.copy;
      case MessageOption.goToLink:
        return Icons.language;
      case MessageOption.none:
        return Icons.close;
    }
  }

  Color get color {
    switch (this) {
      case MessageOption.delete:
        return const Color(0xFFF44A4A);
      default:
        return Colors.black;
    }
  }
}

extension on UIMessage {
  List<MessageOption> get options {
    final options = <MessageOption>[];
    if (this is UIActionMessage) {
      return options;
    }

    if (this is UINormalMessage) {
      final msg = this as UINormalMessage;
      if (msg.isDeleted) {
        return options;
      }
      if (msg.status == MessageStatus.failed) {
        options.add(MessageOption.retry);
        options.add(MessageOption.delete);
      } else {
        if (msg.hasLinkInText) {
          options.add(MessageOption.goToLink);
        }

        if (!msg.stillToBeSent) {
          options.add(MessageOption.forward);
          if (msg.conversation.canSendMessage()) {
            options.add(MessageOption.reply);
          }
          if (msg.messageData.text.isNotEmpty) {
            options.add(MessageOption.copy);
          }
          if (msg.conversation.canDeleteMessage(int.parse(msg.senderId))) {
            options.add(MessageOption.delete);
          }
        }
      }
    }

    return options;
  }
}
