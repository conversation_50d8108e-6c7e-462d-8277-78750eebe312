import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:praja/features/posters/widgets/poster_constants.dart';
import 'package:praja/services/app_cache_manager.dart';

const _widgetAspectRatio = 224 / 230;
const _posterToWidgetWidthRatio = 144 / 224;
const _posterAspectRatio = posterWidth / posterHeight;

class CategoryPreview extends StatelessWidget {
  final double width;
  final List<String> images;
  const CategoryPreview({
    super.key,
    required this.width,
    required this.images,
  });

  double _posterHeight() {
    return width * _posterToWidgetWidthRatio / _posterAspectRatio;
  }

  double _posterWidth() {
    return width * _posterToWidgetWidthRatio;
  }

  Widget _buildImage(String imageUrl) {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.5),
            offset: const Offset(1, 1),
            blurRadius: 4,
          ),
        ],
        borderRadius: BorderRadius.circular(4),
      ),
      height: _posterHeight(),
      width: _posterWidth(),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: CachedNetworkImage(
          imageUrl: imageUrl,
          fadeInDuration: const Duration(milliseconds: 0),
          cacheManager: AppCacheManager.instance,
          fit: BoxFit.cover,
          placeholder: (context, url) => Center(
            child: Container(
              color: Colors.grey.shade200,
            ),
          ),
          imageBuilder: (context, imageProvider) => Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: imageProvider,
                fit: BoxFit.cover,
              ),
            ),
          ),
          errorWidget: (context, url, error) => const Icon(Icons.error),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (images.isEmpty) return const SizedBox();

    return SizedBox(
        width: width,
        height: width / _widgetAspectRatio,
        child: Stack(
          alignment: Alignment.center,
          children: [
            if (images.length > 2)
              Transform.translate(
                  offset: Offset(_posterWidth() / 6, -(_posterHeight() / 32)),
                  child: Transform.rotate(
                    angle: 0.128,
                    child: Transform.scale(
                      scale: 0.945,
                      child: _buildImage(images[2]),
                    ),
                  )),
            if (images.length > 1)
              Transform.translate(
                  offset:
                      Offset(-(_posterWidth() / 6), -(_posterHeight() / 32)),
                  child: Transform.rotate(
                    angle: -0.128,
                    child: Transform.scale(
                      scale: 0.945,
                      child: _buildImage(images[1]),
                    ),
                  )),

            _buildImage(images[0]),
            //The text
          ],
        ));
  }
}
