import 'package:json_annotation/json_annotation.dart';
import 'package:praja/features/video_posters/models/video_poster_element.dart';
import 'package:praja/models/feed_item_abstract_class.dart';
import 'package:praja/enums/video_mode.dart';

part 'video_poster_carousel.g.dart';

@JsonSerializable()
class VideoPosterCarousel extends FeedItem {
  @Json<PERSON>ey(name: 'video_frame_id')
  final int videoFrameId;
  @<PERSON>son<PERSON>ey(name: 'video_creative_id')
  final int videoCreativeId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'frame_height')
  final double frameHeight;
  @<PERSON>son<PERSON>ey(name: 'frame_width')
  final double frameWidth;
  @Json<PERSON>ey(name: 'video_mode')
  final VideoMode videoMode;
  @Json<PERSON>ey(name: 'elements', defaultValue: [])
  final List<VideoPosterElement> elements;
  @Json<PERSON>ey(name: 'share_text', defaultValue: '')
  final String shareText;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'locked_deeplink', defaultValue: '')
  final String lockedDeeplink;
  @JsonKey(name: 'analytics_params')
  final Map<String, dynamic>? analyticsParams;
  @<PERSON>son<PERSON>ey(name: 'enable_auto_generate', defaultValue: true)
  final bool enableAutoGenerate;
  @JsonKey(name: 'auto_generate_duration', defaultValue: 5)
  final int autoGenerateDuration;

  VideoPosterCarousel({
    required this.videoFrameId,
    required this.videoCreativeId,
    required this.frameHeight,
    required this.frameWidth,
    required this.videoMode,
    required this.elements,
    required this.shareText,
    required this.lockedDeeplink,
    this.analyticsParams,
    required this.enableAutoGenerate,
    required this.autoGenerateDuration,
    required super.feedType,
    super.feedItemId,
  });

  factory VideoPosterCarousel.fromJson(Map<String, dynamic> json) =>
      _$VideoPosterCarouselFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$VideoPosterCarouselToJson(this);

  VideoPosterCarousel copyWith({
    int? videoFrameId,
    int? videoCreativeId,
    double? frameHeight,
    double? frameWidth,
    VideoMode? videoMode,
    List<VideoPosterElement>? elements,
    String? shareText,
    String? lockedDeeplink,
    Map<String, dynamic>? analyticsParams,
    bool? enableAutoGenerate,
    int? autoGenerateDuration,
    String? feedType,
    String? feedItemId,
  }) {
    return VideoPosterCarousel(
      videoFrameId: videoFrameId ?? this.videoFrameId,
      videoCreativeId: videoCreativeId ?? this.videoCreativeId,
      frameHeight: frameHeight ?? this.frameHeight,
      frameWidth: frameWidth ?? this.frameWidth,
      videoMode: videoMode ?? this.videoMode,
      elements: elements ?? this.elements,
      shareText: shareText ?? this.shareText,
      lockedDeeplink: lockedDeeplink ?? this.lockedDeeplink,
      analyticsParams: analyticsParams ?? this.analyticsParams,
      enableAutoGenerate: enableAutoGenerate ?? this.enableAutoGenerate,
      autoGenerateDuration: autoGenerateDuration ?? this.autoGenerateDuration,
      feedType: feedType ?? this.feedType,
      feedItemId: feedItemId ?? this.feedItemId,
    );
  }

  @override
  String toString() {
    return 'VideoPosterCarousel{frameId: $videoFrameId, videoCreativeId: $videoCreativeId, frameHeight: $frameHeight, frameWidth: $frameWidth, videoMode: $videoMode, elements: $elements, shareText: $shareText, lockedDeeplink: $lockedDeeplink, analyticsParams: $analyticsParams, enableAutoGenerate: $enableAutoGenerate, autoGenerateDuration: $autoGenerateDuration, feedType: $feedType, feedItemId: $feedItemId}';
  }
}
