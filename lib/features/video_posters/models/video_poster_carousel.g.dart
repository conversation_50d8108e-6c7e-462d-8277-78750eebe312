// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'video_poster_carousel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VideoPosterCarousel _$VideoPosterCarouselFromJson(Map<String, dynamic> json) =>
    VideoPosterCarousel(
      videoFrameId: (json['video_frame_id'] as num).toInt(),
      videoCreativeId: (json['video_creative_id'] as num).toInt(),
      frameHeight: (json['frame_height'] as num).toDouble(),
      frameWidth: (json['frame_width'] as num).toDouble(),
      videoMode: $enumDecode(_$VideoModeEnumMap, json['video_mode']),
      elements: (json['elements'] as List<dynamic>?)
              ?.map(
                  (e) => VideoPosterElement.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      shareText: json['share_text'] as String? ?? '',
      lockedDeeplink: json['locked_deeplink'] as String? ?? '',
      analyticsParams: json['analytics_params'] as Map<String, dynamic>?,
      enableAutoGeneration: json['enable_auto_generation'] as bool? ?? true,
      triggerAutoGenerationAfter:
          (json['trigger_auto_generation_after'] as num?)?.toInt() ?? 5,
      feedType: json['feed_type'] as String,
      feedItemId: json['feed_item_id'] as String?,
    );

Map<String, dynamic> _$VideoPosterCarouselToJson(VideoPosterCarousel instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('feed_item_id', instance.feedItemId);
  val['feed_type'] = instance.feedType;
  val['video_frame_id'] = instance.videoFrameId;
  val['video_creative_id'] = instance.videoCreativeId;
  val['frame_height'] = instance.frameHeight;
  val['frame_width'] = instance.frameWidth;
  val['video_mode'] = _$VideoModeEnumMap[instance.videoMode]!;
  val['elements'] = instance.elements.map((e) => e.toJson()).toList();
  val['share_text'] = instance.shareText;
  val['locked_deeplink'] = instance.lockedDeeplink;
  writeNotNull('analytics_params', instance.analyticsParams);
  val['enable_auto_generation'] = instance.enableAutoGeneration;
  val['trigger_auto_generation_after'] = instance.triggerAutoGenerationAfter;
  return val;
}

const _$VideoModeEnumMap = {
  VideoMode.portrait: 'PORTRAIT',
  VideoMode.landscape: 'LANDSCAPE',
  VideoMode.square: 'SQUARE',
};
