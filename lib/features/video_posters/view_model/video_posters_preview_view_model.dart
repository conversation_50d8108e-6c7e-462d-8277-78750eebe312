import 'package:carousel_slider/carousel_controller.dart';
import 'package:carousel_slider/carousel_options.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/common/widgets/dots_indicator_widget.dart';
import 'package:praja/features/video_posters/models/video_frame.dart';
import 'package:praja/features/video_posters/video_posters_status_page.dart';
import 'package:praja/features/video_posters/video_posters_service.dart';
import 'package:praja/models/video.dart';
import 'package:praja/services/app_cache_manager.dart';
import 'package:praja/utils/image_utils.dart';

@injectable
class VideoPostersPreviewViewModel extends ViewModel {
  late final Video sourceVideo;
  late final List<VideoFrame> videoFrames;

  final DotsIndicatorController dotsIndicatorController =
      DotsIndicatorController();

  final MutableLiveData<VideoPostersPreviewState> _state =
      MutableLiveData(VideoPostersPreviewLoading());
  LiveData<VideoPostersPreviewState> get state => _state;

  final MutableLiveData<bool> _isMute = MutableLiveData(false);
  LiveData<bool> get isMute => _isMute;

  final MutableEventQueue<VideoPostersPreviewEvent> _eventQueue =
      MutableEventQueue();
  EventQueue<VideoPostersPreviewEvent> get eventQueue => _eventQueue;

  final CarouselController carouselController = CarouselController();

  final VideoPostersService _videoPostersService;

  VideoPostersPreviewViewModel(this._videoPostersService);

  bool _isInitialized = false;
  void init(Video sourceVideo, List<VideoFrame> videoFrames, bool isMute) {
    if (_isInitialized) return;
    _isInitialized = true;
    this.sourceVideo = sourceVideo;
    this.videoFrames = videoFrames;
    _isMute.value = isMute;
    fetchVideoDimensions(sourceVideo.thumbnailUrl);
  }

  Future<void> onDownloadClicked(int videoFrameId, int videoId,
      {ShareMethod initiateShareMethod = ShareMethod.none}) async {
    int videoPosterId =
        await _videoPostersService.generateAndDownloadVideoPosters(
      videoFrameId,
      videoId,
      triggerSource: "user",
    );
    _eventQueue
        .push(GoToVideoPosterStatusEvent(videoPosterId, initiateShareMethod));
  }

  void onRetryClicked() {
    fetchVideoDimensions(sourceVideo.thumbnailUrl);
  }

  void onPageChanged(int index, CarouselPageChangedReason reason) {
    dotsIndicatorController.updateIndex(index);
  }

  void updateMuteStatus(bool isMute) {
    _isMute.value = isMute;
  }

  Future<void> fetchVideoDimensions(String thumbnailUrl) async {
    _state.value = VideoPostersPreviewLoading();
    try {
      if (sourceVideo.videoHeight == null || sourceVideo.videoWidth == null) {
        try {
          final thumbnail =
              await AppCacheManager.instance.getSingleFile(thumbnailUrl);
          final size = await ImageUtils.getImageSize(thumbnail);
          if (size.height == 0 || size.width == 0) {
            throw Exception("Video Dimensions should not be zero");
          }
          _state.value = VideoPostersPreviewSuccess(
            size.height,
            size.width,
          );
        } catch (e) {
          _state.value = VideoPostersPreviewError(e.toString());
        }
      } else {
        _state.value = VideoPostersPreviewSuccess(
          sourceVideo.videoHeight!.toDouble(),
          sourceVideo.videoWidth!.toDouble(),
        );
      }
    } catch (e) {
      _state.value = VideoPostersPreviewError(e.toString());
    }
  }
}

sealed class VideoPostersPreviewState {}

class VideoPostersPreviewLoading extends VideoPostersPreviewState {}

class VideoPostersPreviewError extends VideoPostersPreviewState {
  final String message;
  VideoPostersPreviewError(this.message);
}

class VideoPostersPreviewSuccess extends VideoPostersPreviewState {
  final double videoHeight;
  final double videoWidth;
  VideoPostersPreviewSuccess(this.videoHeight, this.videoWidth);
}

sealed class VideoPostersPreviewEvent {}

class GoToVideoPosterStatusEvent extends VideoPostersPreviewEvent {
  final int videoPosterId;
  final ShareMethod initiateShareMethod;

  GoToVideoPosterStatusEvent(this.videoPosterId, this.initiateShareMethod);
}
