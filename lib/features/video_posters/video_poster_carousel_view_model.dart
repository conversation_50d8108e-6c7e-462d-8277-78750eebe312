import 'dart:async';

import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:jetpack/jetpack.dart';
import 'package:praja/enums/poster_share_destination.dart';
import 'package:praja/errors/error_delegate.dart';
import 'package:praja/features/video_posters/models/video_poster_carousel.dart';
import 'package:praja/features/video_posters/models/video_poster_with_status.dart';
import 'package:praja/features/video_posters/video_posters_service.dart';
import 'package:praja/utils/logger.dart';

@injectable
class VideoPosterCarouselViewModel extends ViewModel {
  final VideoPostersService _videoPostersService;

  VideoPosterCarouselViewModel(
    this._videoPostersService,
  );

  final MutableLiveData<VideoPosterCarouselState> _state =
      MutableLiveData(VideoPosterCarouselIdle());
  LiveData<VideoPosterCarouselState> get state => _state;

  final MutableLiveData<int> _progress = MutableLiveData(0);
  LiveData<int> get progress => _progress;

  final MutableEventQueue<VideoPosterCarouselEvent> _eventQueue =
      MutableEventQueue();
  EventQueue<VideoPosterCarouselEvent> get eventQueue => _eventQueue;

  late int _frameId;
  late int _videoCreativeId;
  late VideoPosterCarousel _carousel;
  Map<String, dynamic>? _sourceAnalyticsParams;
  StreamSubscription? _videoPosterStatusSubscription;
  PosterShareDestination _pendingActionMethod = PosterShareDestination.unknown;
  int? _currentVideoPosterId;
  Timer? _successTimer;
  bool _isInitialized = false;
  String _shareText = "";
  VoidCallback? _onOperationStarted;
  VoidCallback? _onOperationEnded;

  // Auto-generation state management
  bool _hasTriggeredAutoGeneration = false;
  int? _autoGeneratedVideoPosterId;
  VideoPosterAutoGenerationStatus _autoGenerationStatus =
      VideoPosterAutoGenerationStatus.notStarted;

  /// Getter to expose pending action method for UI
  PosterShareDestination get pendingActionMethod => _pendingActionMethod;

  /// Getter to expose auto-generation status for UI logic
  VideoPosterAutoGenerationStatus get autoGenerationStatus =>
      _autoGenerationStatus;

  void _init({
    required VideoPosterCarousel carousel,
    VoidCallback? onOperationStarted,
    VoidCallback? onOperationEnded,
  }) {
    if (_isInitialized) return;
    _carousel = carousel;
    _frameId = carousel.videoFrameId;
    _videoCreativeId = carousel.videoCreativeId;
    _sourceAnalyticsParams = carousel.analyticsParams;
    _shareText = carousel.shareText;
    _onOperationStarted = onOperationStarted;
    _onOperationEnded = onOperationEnded;
    _isInitialized = true;
  }

  // TODO: Need to Refactor Return URL Logic
  void _triggerLayoutLockedEvent(String method) {
    final deeplink = _carousel.lockedDeeplink;
    if (deeplink.isEmpty) return;
    final uri = Uri.parse(deeplink);
    final queryParams = Map<String, String>.from(uri.queryParameters);
    final path = uri.path;

    final source = queryParams.remove('source');
    final returnUrl = queryParams.remove('return_url');
    final frameId = queryParams.remove('frame_id');

    // Reconstruct finalSource with method
    final finalSource = source != null ? '${source}_$method' : method;
    queryParams['source'] = finalSource;

    if (returnUrl != null) {
      //attach frame_id to return_url if present
      // ignore: avoid_hardcoded_strings_in_ui
      final sep = returnUrl.contains('?') ? '&' : '?';
      final nested = (frameId != null && !returnUrl.contains('frame_id='))
          ? '$returnUrl${sep}frame_id=$frameId&source=$finalSource'
          : '$returnUrl${sep}source=$finalSource';
      queryParams['return_url'] = nested;
    }

    // Generate the final deeplink with all params
    final updatedUri = uri.replace(path: path, queryParameters: queryParams);
    final navigateDeeplink = updatedUri.toString();

    _eventQueue.push(VideoPosterLockedEvent(navigateDeeplink));
  }

  Future<void> onDownloadClicked() async {
    if (_carousel.lockedDeeplink.isNotEmpty) {
      _triggerLayoutLockedEvent('download');
      return;
    }
    await _handleContextAwareDownload(PosterShareDestination.download);
  }

  Future<void> onShareClicked() async {
    if (_carousel.lockedDeeplink.isNotEmpty) {
      _triggerLayoutLockedEvent('external_share');
      return;
    }
    await _handleContextAwareDownload(PosterShareDestination.externalShare);
  }

  Future<void> onWhatsappClicked() async {
    if (_carousel.lockedDeeplink.isNotEmpty) {
      _triggerLayoutLockedEvent('whatsapp');
      return;
    }
    await _handleContextAwareDownload(PosterShareDestination.whatsapp);
  }

  void onRetryClicked() {
    if (_currentVideoPosterId != null) {
      fetchVideoDetails(_currentVideoPosterId!);
    } else {
      _generateAndDownloadVideoPosters(_pendingActionMethod);
    }
  }

  void onCloseErrorClicked() {
    _state.value = VideoPosterCarouselIdle();
    _pendingActionMethod = PosterShareDestination.unknown;
    _currentVideoPosterId = null;
    _successTimer?.cancel();

    // Notify parent that operation ended
    _onOperationEnded?.call();
  }

  /// Called when user watches video for 3 seconds to trigger auto-generation
  Future<void> onVideoWatched3Seconds() async {
    // Only trigger auto-generation once per video poster
    if (_hasTriggeredAutoGeneration) return;

    _hasTriggeredAutoGeneration = true;
    _autoGenerationStatus = VideoPosterAutoGenerationStatus.inProgress;

    printDebug(
        'Starting auto-generation for video poster: frameId=$_frameId, videoCreativeId=$_videoCreativeId');

    try {
      // Generate video poster in background without showing UI
      final videoPosterId = await _videoPostersService.generateVideoPosterAuto(
        _frameId,
        _videoCreativeId,
      );

      _autoGeneratedVideoPosterId = videoPosterId;

      // Listen to status updates for auto-generated video poster
      _listenToAutoGenerationStatusEvent(videoPosterId);

      printDebug('Auto-generation started for video poster ID: $videoPosterId');
    } catch (e) {
      printDebug('Auto-generation failed: $e');
      _autoGenerationStatus = VideoPosterAutoGenerationStatus.failed;
    }
  }

  /// Context-aware download that checks auto-generation status
  Future<void> _handleContextAwareDownload(
      PosterShareDestination actionMethod) async {
    final currentState = _state.value;
    if (currentState is VideoPosterCarouselGenerating ||
        currentState is VideoPosterCarouselDownloading) return;

    // Store action method for retry functionality
    _pendingActionMethod = actionMethod;

    // Check auto-generation status and handle accordingly
    switch (_autoGenerationStatus) {
      case VideoPosterAutoGenerationStatus.notStarted:
        // User clicked before 3 seconds - trigger generation and download with user source
        await _generateAndDownloadVideoPosters(actionMethod,
            triggerSource: "user");
        break;

      case VideoPosterAutoGenerationStatus.inProgress:
        // Auto-generation is in progress - show generation UI and wait for completion
        _currentVideoPosterId = _autoGeneratedVideoPosterId;
        _state.value = VideoPosterCarouselGenerating();
        _onOperationStarted?.call();

        if (_autoGeneratedVideoPosterId != null) {
          listenToVideoPosterStatusEvent(_autoGeneratedVideoPosterId!);
        }
        break;

      case VideoPosterAutoGenerationStatus.completed:
        // Auto-generation completed - directly show download UI
        _currentVideoPosterId = _autoGeneratedVideoPosterId;
        _state.value = VideoPosterCarouselDownloading();
        _onOperationStarted?.call();

        if (_autoGeneratedVideoPosterId != null) {
          listenToVideoPosterStatusEvent(_autoGeneratedVideoPosterId!);
        }
        break;

      case VideoPosterAutoGenerationStatus.failed:
        // Auto-generation failed - trigger new generation with user source
        await _generateAndDownloadVideoPosters(actionMethod,
            triggerSource: "user");
        break;
    }
  }

  Future<void> _generateAndDownloadVideoPosters(
      PosterShareDestination actionMethod,
      {String triggerSource = "user"}) async {
    final currentState = _state.value;
    if (currentState is VideoPosterCarouselGenerating ||
        currentState is VideoPosterCarouselDownloading) return;

    // Store action method for retry functionality
    _pendingActionMethod = actionMethod;

    // Reset progress
    _progress.value = 0;

    // Start with generating state
    _state.value = VideoPosterCarouselGenerating();

    // Notify parent that operation started
    _onOperationStarted?.call();

    try {
      // Call the service to generate and download video posters
      final videoPosterId =
          await _videoPostersService.generateAndDownloadVideoPosters(
        _frameId,
        _videoCreativeId,
        triggerSource: triggerSource,
      );

      _currentVideoPosterId = videoPosterId;

      // Listen to the service stream for status updates
      // The service handles internal socket listening and timer fallbacks
      listenToVideoPosterStatusEvent(videoPosterId);

      printDebug('Video poster generation started for ID: $videoPosterId');
    } catch (e) {
      printDebug('Video poster generation failed: $e');
      _state.value = VideoPosterCarouselError(localisedErrorMessage(e));
      _resetState();

      // Notify parent that operation ended
      _onOperationEnded?.call();
    }
  }

  // Add fetchVideoDetails method for retry functionality
  Future<void> fetchVideoDetails(int videoPosterId) async {
    _state.value = VideoPosterCarouselGenerating();
    try {
      final response =
          await _videoPostersService.getVideoPosterWithStatus(videoPosterId);
      final videoPosterStatus = response.videoPosterStatus;

      printDebug('Fetched video poster status: $videoPosterStatus');

      if (videoPosterStatus == VideoPosterStatus.downloadCompleted) {
        _state.value = VideoPosterCarouselSuccess();
        _triggerActionMethod(_pendingActionMethod, videoPosterId);
        _autoHideSuccess();
        // Don't reset state here - it will be reset when success UI is hidden

        // Notify parent that operation ended (success)
        _onOperationEnded?.call();
      } else {
        // Continue listening for status updates
        listenToVideoPosterStatusEvent(videoPosterId);
        _updateStateFromStatus(videoPosterStatus);
      }
    } catch (e) {
      printDebug('Failed to fetch video details: $e');
      _state.value = VideoPosterCarouselError(localisedErrorMessage(e));

      // Notify parent that operation ended (error)
      _onOperationEnded?.call();
    }
  }

  void listenToVideoPosterStatusEvent(int videoPosterId) {
    _videoPosterStatusSubscription?.cancel();
    _videoPosterStatusSubscription = _videoPostersService.stream
        .where((event) => event.videoPosterId == videoPosterId)
        .listen(
      (event) {
        if (event is VideoPosterStatusUpdate) {
          _handleStatusUpdate(event.videoPosterStatus, videoPosterId);
        } else if (event is VideoPosterDownloadProgressUpdate) {
          _progress.value = event.progress;
        }
      },
      onError: (e) {
        printDebug('Video poster status stream error: $e');
        _state.value = VideoPosterCarouselError(localisedErrorMessage(e));
      },
    );
  }

  /// Listen to auto-generation status events (silent, no UI updates)
  void _listenToAutoGenerationStatusEvent(int videoPosterId) {
    _videoPostersService.stream
        .where((event) => event.videoPosterId == videoPosterId)
        .listen(
      (event) {
        if (event is VideoPosterStatusUpdate) {
          _handleAutoGenerationStatusUpdate(
              event.videoPosterStatus, videoPosterId);
        }
      },
      onError: (e) {
        printDebug('Auto-generation status stream error: $e');
        _autoGenerationStatus = VideoPosterAutoGenerationStatus.failed;
      },
    );
  }

  /// Handle auto-generation status updates (silent, no UI changes)
  void _handleAutoGenerationStatusUpdate(
      VideoPosterStatus status, int videoPosterId) {
    printDebug(
        'Auto-generation status update: $status for video poster ID: $videoPosterId');

    switch (status) {
      case VideoPosterStatus.generationPending:
      case VideoPosterStatus.generationProcessing:
        _autoGenerationStatus = VideoPosterAutoGenerationStatus.inProgress;
        break;
      case VideoPosterStatus.downloadEnqueued:
      case VideoPosterStatus.downloadRunning:
      case VideoPosterStatus.downloadCompleted:
        _autoGenerationStatus = VideoPosterAutoGenerationStatus.completed;
        printDebug(
            'Auto-generation completed for video poster ID: $videoPosterId');
        break;
      case VideoPosterStatus.generationFailed:
      case VideoPosterStatus.downloadFailed:
      case VideoPosterStatus.downloadCancelled:
        _autoGenerationStatus = VideoPosterAutoGenerationStatus.failed;
        printDebug(
            'Auto-generation failed for video poster ID: $videoPosterId');
        break;
      default:
        break;
    }
  }

  void _handleStatusUpdate(VideoPosterStatus status, int videoPosterId) {
    printDebug('Video poster status update: $status');
    _updateStateFromStatus(status);

    if (status == VideoPosterStatus.downloadCompleted) {
      _state.value = VideoPosterCarouselSuccess();
      _triggerActionMethod(_pendingActionMethod, videoPosterId);
      _autoHideSuccess();
      // Don't reset state here - it will be reset when success UI is hidden

      // Notify parent that operation ended (success)
      _onOperationEnded?.call();
    } else if (status == VideoPosterStatus.generationFailed ||
        status == VideoPosterStatus.downloadFailed ||
        status == VideoPosterStatus.downloadCancelled) {
      final errorMessage = _getErrorMessage(status);
      _state.value = VideoPosterCarouselError(errorMessage);
      // Don't reset state here to allow retry functionality

      // Notify parent that operation ended (error)
      _onOperationEnded?.call();
    }
  }

  void _updateStateFromStatus(VideoPosterStatus status) {
    switch (status) {
      case VideoPosterStatus.generationPending:
      case VideoPosterStatus.generationProcessing:
        _state.value = VideoPosterCarouselGenerating();
        break;

      case VideoPosterStatus.downloadEnqueued:
      case VideoPosterStatus.downloadRunning:
        _state.value = VideoPosterCarouselDownloading();
        break;

      case VideoPosterStatus.downloadUndefined:
      case VideoPosterStatus.downloadPaused:
        // Keep current state for these intermediate states
        break;

      default:
        // Handle other states in _handleStatusUpdate
        break;
    }
  }

  String _getErrorMessage(VideoPosterStatus status) {
    switch (status) {
      case VideoPosterStatus.generationFailed:
        return 'Video poster generation failed';
      case VideoPosterStatus.downloadFailed:
        return 'Video poster download failed';
      case VideoPosterStatus.downloadCancelled:
        return 'Video poster download was cancelled';
      default:
        return 'An error occurred';
    }
  }

  void _autoHideSuccess() {
    _successTimer?.cancel();
    _successTimer = Timer(const Duration(seconds: 2), () {
      if (_state.value is VideoPosterCarouselSuccess) {
        _state.value = VideoPosterCarouselIdle();
        _resetState(); // Reset state after hiding success UI

        // Note: We don't call _onOperationEnded here because it was already called
        // when the success state was first set
      }
    });
  }

  void _resetState() {
    _pendingActionMethod = PosterShareDestination.unknown;
    _currentVideoPosterId = null;
    // Note: We don't reset auto-generation state here as it should persist
    // across user interactions until the widget is disposed
  }

  Future<void> _triggerActionMethod(
      PosterShareDestination destination, int videoPosterId) async {
    try {
      final deeplinkUrl = await _recordShare(destination, videoPosterId);
      final downloadFilePath =
          await _videoPostersService.getDownloadedFilePath(videoPosterId);

      // Emit event with destination and deeplink
      // right now we are firing same event for all the destinations
      // but in future if we want to handle them differently, we can add more events
      switch (destination) {
        case PosterShareDestination.whatsapp:
        case PosterShareDestination.externalShare:
        case PosterShareDestination.download:
        case PosterShareDestination.unknown:
          _eventQueue.push(
            VideoPosterShareEvent(
              filePath: downloadFilePath,
              shareText: _shareText,
              destination: destination,
              deeplinkUrl: deeplinkUrl,
            ),
          );
          break;
      }
    } catch (e, st) {
      logNonFatalIfAppError(
        "Failure while video poster share in _triggerActionMethod",
        e,
        stackTrace: st,
      );
    }
  }

  Future<String?> _recordShare(
    PosterShareDestination destination,
    int videoPosterId,
  ) async {
    try {
      final response = await _videoPostersService.recordShare(
        shareDestination: destination.asMethod(),
        videoPosterId: videoPosterId,
        videoFrameId: _frameId,
        videoCreativeId: _videoCreativeId,
        analyticsParams: _sourceAnalyticsParams,
      );

      return response.deeplinkUrl;
    } catch (e, st) {
      logNonFatalIfAppError("Failure while recording video poster share", e,
          stackTrace: st);
      return null;
    }
  }

  @override
  void onDispose() {
    _videoPosterStatusSubscription?.cancel();
    _successTimer?.cancel();
    super.onDispose();
  }
}

/// Represents the different states of video poster carousel during generation and download process
sealed class VideoPosterCarouselState {}

/// Initial state when no operation is in progress
class VideoPosterCarouselIdle extends VideoPosterCarouselState {}

/// State when video poster is being generated on the server
class VideoPosterCarouselGenerating extends VideoPosterCarouselState {}

/// State when video poster is being downloaded with progress tracking
class VideoPosterCarouselDownloading extends VideoPosterCarouselState {}

/// State when video poster generation and download completed successfully
class VideoPosterCarouselSuccess extends VideoPosterCarouselState {}

/// State when an error occurred during generation or download process
class VideoPosterCarouselError extends VideoPosterCarouselState {
  final String message;
  VideoPosterCarouselError(this.message);
}

/// Events for video poster carousel actions
sealed class VideoPosterCarouselEvent {}

/// Event to trigger sharing with specified destination
class VideoPosterShareEvent extends VideoPosterCarouselEvent {
  final String filePath;
  final String shareText;
  final PosterShareDestination destination;

  ///This Deeplink is to redirect user to any of the screen that we want after the share action is completed
  final String? deeplinkUrl;

  VideoPosterShareEvent({
    required this.filePath,
    required this.shareText,
    required this.destination,
    this.deeplinkUrl,
  });
}

class VideoPosterLockedEvent extends VideoPosterCarouselEvent {
  final String deeplink;

  VideoPosterLockedEvent(this.deeplink);
}

/// Enum to track auto-generation status
enum VideoPosterAutoGenerationStatus {
  notStarted,
  inProgress,
  completed,
  failed,
}

extension VideoPosterCarouselViewModelX on BuildContext {
  VideoPosterCarouselViewModel videoPosterCarouselViewModel({
    required String key,
    required VideoPosterCarousel carousel,
    VoidCallback? onOperationStarted,
    VoidCallback? onOperationEnded,
  }) {
    return getViewModel<VideoPosterCarouselViewModel>(key: key)
      .._init(
        carousel: carousel,
        onOperationStarted: onOperationStarted,
        onOperationEnded: onOperationEnded,
      );
  }
}
