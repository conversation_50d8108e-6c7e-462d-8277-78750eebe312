import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/mixins/analytics.dart';
import 'package:praja/models/admin_user.dart';
import 'package:praja/presentation/praja_icons.dart';
import 'package:praja/utils/utils.dart';
import 'package:url_launcher/url_launcher.dart';

class CancelMembershipHelperWidgets {
  static Widget rmUserWidget({
    required AdminUser rmUser,
    required BuildContext context,
    Map<String, dynamic>? analyticsParams,
    required String source,
  }) {
    final rmUserPhoto = rmUser.photoUrl;

    return InkWell(
      customBorder: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(34),
      ),
      onTap: () async {
        final String unableToOpenPhoneText = context.getString(
            StringKey.premiumUnableToOpenPhoneErrorMessage,
            listen: false);
        //open dialer
        final Uri phoneLaunchUri = Uri(
          scheme: 'tel',
          path: rmUser.phone.toString(),
        );
        if (await canLaunchUrl(phoneLaunchUri)) {
          AppAnalytics.onInitiatedCallToRm(
            source: source,
            params: analyticsParams,
          );
          await launchUrl(phoneLaunchUri);
        } else {
          AppAnalytics.onFailedCallToRm(
              source: source,
              reason: 'failed to open dialer',
              params: analyticsParams);
          Utils.showToast(unableToOpenPhoneText);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: const Color(0xFFD3AF4A), width: 1),
          borderRadius: BorderRadius.circular(34),
        ),
        child: Row(
          children: [
            if (rmUserPhoto != null)
              CachedNetworkImage(
                imageUrl: rmUserPhoto,
                fadeOutDuration: const Duration(milliseconds: 300),
                fadeInDuration: const Duration(milliseconds: 300),
                placeholder: (context, url) => const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(),
                ),
                errorWidget: (context, url, error) => const Icon(Icons.error),
                imageBuilder: (context, imageProvider) => Container(
                  width: 30,
                  height: 30,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    image: DecorationImage(
                      image: imageProvider,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
            const SizedBox(width: 12),
            const Icon(
              PrajaIcons.call,
              color: Color(0xffD3AF4A),
              size: 20,
            ),
            const SizedBox(width: 4),
          ],
        ),
      ),
    );
  }

  static Widget extendOrSwitchPlanButtonUI({
    required String text,
    required void Function()? onTap,
    required bool isSwitchingOrExtending,
  }) {
    return InkWell(
      onTap: isSwitchingOrExtending ? null : onTap,
      child: Container(
        width: double.infinity,
        height: 48,
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: const Color(0xFF00A650),
        ),
        child: isSwitchingOrExtending
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Colors.white,
                  ),
                ),
              )
            : Text(
                text,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
      ),
    );
  }

  static Widget cancelMembershipButtonUI({
    required String text,
    required void Function()? onTap,
    required bool isDisabled,
    bool cancelling = false,
  }) {
    return InkWell(
      onTap: isDisabled ? null : onTap,
      child: Container(
        width: double.infinity,
        height: 48,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color:
                isDisabled ? const Color(0xFFCCCCCC) : const Color(0xFFFF7171),
            width: 1,
          ),
        ),
        child: cancelling
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Color(0xFFFF7171),
                  ),
                ),
              )
            : Text(
                text,
                style: TextStyle(
                  color: isDisabled
                      ? const Color(0xFFCCCCCC)
                      : const Color(0xFFFF7171),
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
      ),
    );
  }
}
