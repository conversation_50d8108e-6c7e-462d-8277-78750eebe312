// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_bottom_sheet_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaymentBottomSheetResponse _$PaymentBottomSheetResponseFromJson(
        Map<String, dynamic> json) =>
    PaymentBottomSheetResponse(
      title: json['title'] as String? ?? '',
      titleTextColor: (json['title_text_color'] as num?)?.toInt() ?? 4294329926,
      subtitle: json['sub_title'] as String? ?? '',
      subtitleTextColor:
          (json['sub_title_text_color'] as num?)?.toInt() ?? 4278190080,
      existingPremiumUsers: json['existing_premium_users'] == null
          ? null
          : ExistingPremiumUsers.fromJson(
              json['existing_premium_users'] as Map<String, dynamic>),
      autoRechargeText: json['auto_recharge_text'] as String? ?? '',
      autoRechargeCancelText:
          json['auto_recharge_cancel_text'] as String? ?? '',
      rmUser: json['rm_user'] == null
          ? null
          : AdminUser.fromJson(json['rm_user'] as Map<String, dynamic>),
      plans: (json['plans'] as List<dynamic>?)
              ?.map((e) => PlanItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      paymentShareText: json['payment_share_text'] as String,
      terms: PosterTerms.fromJson(json['terms'] as Map<String, dynamic>),
      buttonDetails: PremiumExperienceButtonDetails.fromJson(
          json['button_details'] as Map<String, dynamic>),
      analyticsParams: json['analytics_params'] as Map<String, dynamic>? ?? {},
      paymentGateway: $enumDecodeNullable(
              _$PaymentGatewayEnumEnumMap, json['payment_gateway'],
              unknownValue: PaymentGatewayEnum.url) ??
          PaymentGatewayEnum.url,
    );

Map<String, dynamic> _$PaymentBottomSheetResponseToJson(
    PaymentBottomSheetResponse instance) {
  final val = <String, dynamic>{
    'title': instance.title,
    'title_text_color': instance.titleTextColor,
    'sub_title': instance.subtitle,
    'sub_title_text_color': instance.subtitleTextColor,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull(
      'existing_premium_users', instance.existingPremiumUsers?.toJson());
  val['auto_recharge_text'] = instance.autoRechargeText;
  val['auto_recharge_cancel_text'] = instance.autoRechargeCancelText;
  writeNotNull('rm_user', instance.rmUser?.toJson());
  val['plans'] = instance.plans.map((e) => e.toJson()).toList();
  val['payment_share_text'] = instance.paymentShareText;
  val['terms'] = instance.terms.toJson();
  val['button_details'] = instance.buttonDetails.toJson();
  val['analytics_params'] = instance.analyticsParams;
  val['payment_gateway'] =
      _$PaymentGatewayEnumEnumMap[instance.paymentGateway]!;
  return val;
}

const _$PaymentGatewayEnumEnumMap = {
  PaymentGatewayEnum.url: 'url',
  PaymentGatewayEnum.juspay: 'juspay',
  PaymentGatewayEnum.intent: 'intent',
};

PlanItem _$PlanItemFromJson(Map<String, dynamic> json) => PlanItem(
      id: (json['id'] as num?)?.toInt() ?? 0,
      durationText: json['duration_text'] as String? ?? '',
      perMonthText: json['per_month_text'] as String? ?? '',
      showStrikeThrough: json['show_strike_through'] as bool? ?? false,
      amount: (json['amount'] as num?)?.toInt() ?? 0,
      discountText: json['discount_text'] as String? ?? '',
      discountTextColor:
          (json['discount_text_color'] as num?)?.toInt() ?? 4294967295,
      discountTextBgColor:
          (json['discount_text_bg_color'] as num?)?.toInt() ?? 4283344130,
      selected: json['selected'] as bool? ?? false,
      mandatePresent: json['mandate_present'] as bool? ?? false,
      mandatePresentTextSuffix:
          json['mandate_present_text_suffix'] as String? ?? '',
    );

Map<String, dynamic> _$PlanItemToJson(PlanItem instance) => <String, dynamic>{
      'id': instance.id,
      'duration_text': instance.durationText,
      'per_month_text': instance.perMonthText,
      'show_strike_through': instance.showStrikeThrough,
      'amount': instance.amount,
      'discount_text': instance.discountText,
      'discount_text_color': instance.discountTextColor,
      'discount_text_bg_color': instance.discountTextBgColor,
      'selected': instance.selected,
      'mandate_present': instance.mandatePresent,
      'mandate_present_text_suffix': instance.mandatePresentTextSuffix,
    };
