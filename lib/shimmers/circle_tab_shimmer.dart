import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class TabIconShimmer extends StatelessWidget {
  const TabIconShimmer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
        baseColor: Colors.grey.shade300,
        highlightColor: Colors.grey.shade100,
        child: Container(
            // padding: EdgeInsets.only(left: 15),
            alignment: Alignment.centerLeft,
            height: 30,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                color: Colors.white,
              ),
              width: 20,
              height: 25,
            )));
  }
}

class TabNameShimmer extends StatelessWidget {
  const TabNameShimmer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
        baseColor: Colors.grey.shade300,
        highlightColor: Colors.grey.shade100,
        child: Container(
            alignment: Alignment.centerLeft,
            height: 20,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.red,
              ),
              height: 15,
              width: 50,
            )));
  }
}

class SliverCircleTabBarShimmer extends SliverPersistentHeaderDelegate {
  TabController tabController;

  SliverCircleTabBarShimmer({required this.tabController});

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      color: Colors.white,
      child: TabBar(
        controller: tabController,
        onTap: null,
        indicatorColor: Colors.black,
        isScrollable: true,
        labelColor: Colors.black,
        unselectedLabelColor: Colors.grey,
        tabs: [
          Container(
            alignment: Alignment.center,
            child: const TabNameShimmer(),
          )
        ],
      ),
    );
  }

  @override
  double get maxExtent => 49;

  @override
  double get minExtent => 45;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) =>
      oldDelegate.maxExtent != maxExtent || oldDelegate.minExtent != minExtent;
}
