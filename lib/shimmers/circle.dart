import 'package:flutter/material.dart';
import 'package:praja/shimmers/circle_details.dart';
import 'package:praja/shimmers/circle_name.dart';
import 'package:praja/shimmers/circle_party_base_info.dart';
import 'package:praja/shimmers/circle_party_type.dart';
import 'package:praja/shimmers/circle_photo.dart';
import 'package:shimmer/shimmer.dart';

class CircleShimmers extends StatelessWidget {
  const CircleShimmers({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: const [
          SizedB<PERSON>(
            height: 70,
          ),
          CirclePhotoShimmer(),
          <PERSON><PERSON><PERSON><PERSON>(
            height: 10,
          ),
          <PERSON>NameShimmer(),
          <PERSON><PERSON><PERSON><PERSON>(
            height: 10,
          ),
          CirclePartyTypeShimmer(),
          <PERSON><PERSON><PERSON><PERSON>(
            height: 20,
          ),
          CircleDetailsShimmer(),
        ],
      ),
    );
  }
}
