import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ContactsShimmer extends StatelessWidget {
  const ContactsShimmer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      child: ListView.builder(
        itemBuilder: (_, __) => Padding(
          padding: const EdgeInsets.only(top: 5, bottom: 5),
          child: ListTile(
            leading: const CircleAvatar(
              radius: 20,
              backgroundColor: Colors.white,
              child: null,
            ),
            title: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
              ),
              width: 100,
              height: 12.0,
            ),
            subtitle: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
              ),
              width: 50,
              height: 10.0,
            ),
            trailing: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
              ),
              width: 20,
              height: 20,
            ),
          ),
        ),
        itemCount: 5,
      ),
    );
  }
}
