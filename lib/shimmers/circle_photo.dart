import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class CirclePhotoShimmer extends StatelessWidget {
  const CirclePhotoShimmer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
        baseColor: Colors.grey.shade300,
        highlightColor: Colors.grey.shade100,
        child: Container(
          alignment: Alignment.centerLeft,
          height: MediaQuery.of(context).size.width * 0.35,
          width: MediaQuery.of(context).size.height * 0.35,
          child: const CircleAvatar(
            radius: 60,
          ),
        ));
  }
}
