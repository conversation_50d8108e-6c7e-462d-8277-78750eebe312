import 'package:flutter/material.dart';
import 'package:praja/presentation/app_icons.dart';
import 'package:shimmer/shimmer.dart';

class CirclePartyTypeShimmer extends StatelessWidget {
  const CirclePartyTypeShimmer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
        baseColor: Colors.grey.shade300,
        highlightColor: Colors.grey.shade100,
        child: Row(
          children: [
            const Icon(
              AppIcons.groupTypeIcon,
              size: 10,
            ),
            const SizedBox(
              width: 4,
            ),
            Container(
                height: MediaQuery.of(context).size.height * 0.02,
                width: MediaQuery.of(context).size.width * 0.2,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10))),
          ],
        ));
  }
}
