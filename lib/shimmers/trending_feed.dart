import 'package:flutter/material.dart';
import 'package:praja/features/localization/string_key.dart';
import 'package:praja/shimmers/post.dart';
import 'package:shimmer/shimmer.dart';

class TrendingFeedShimmer extends StatelessWidget {
  const TrendingFeedShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      itemBuilder: (_, index) => Padding(
        padding: const EdgeInsets.only(top: 5, bottom: 5),
        child: index >= 3 ? const PostShimmer() : _TrendingHashtagShimmer(),
      ),
      itemCount: 4,
    );
  }
}

class _TrendingHashtagShimmer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Shimmer.fromColors(
        baseColor: Colors.grey.shade300,
        highlightColor: Colors.grey.shade100,
        child: _TrendingHashtagShimmerElement(),
      ),
    );
  }
}

class _TrendingHashtagShimmerElement extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    double deviceWidth = MediaQuery.of(context).size.width;

    return Container(
      height: 50,
      margin: const EdgeInsets.only(bottom: 10),
      child: ListTile(
        leading: const Icon(
          Icons.trending_up,
          color: Colors.deepOrange,
        ),
        trailing: const Icon(Icons.arrow_forward_ios),
        dense: true,
        title: Container(
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(10)),
          width: 2 * deviceWidth / 3,
          height: 12,
        ),
        subtitle: Text(context.getString(StringKey.trendingFeedShimmerText)),
      ),
    );
  }
}
