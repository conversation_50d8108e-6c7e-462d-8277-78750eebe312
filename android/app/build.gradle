plugins {
  id("com.google.devtools.ksp").version("1.9.0-1.0.12")
  id "com.android.application"
  id "kotlin-android"
  id "dev.flutter.flutter-gradle-plugin"
  id "com.google.gms.google-services"
  id "com.google.firebase.crashlytics"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

// Debug
def debugKeystoreProperties = new Properties()
def debugKeystorePropertiesFile = rootProject.file('debug.properties')
if (debugKeystorePropertiesFile.exists()) {
	debugKeystoreProperties.load(new FileInputStream(debugKeystorePropertiesFile))
}

// Release
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
} else {
	// use debug properties if release `key.properties` doesn't exist
	// This allows for a developer to have build working without any
	// extra setup commands
	keystoreProperties = debugKeystoreProperties
}

android {
  namespace 'buzz.praja.app'
  compileSdkVersion 34
  ndkVersion "27.0.12077973" // for rive

  sourceSets {
    main.java.srcDirs += 'src/main/kotlin'
  }

  lintOptions {
    disable 'InvalidPackage'
  }

  defaultConfig {
    applicationId "buzz.praja.app"
    minSdkVersion 24
    targetSdkVersion 34
    versionCode flutterVersionCode.toInteger()
    versionName flutterVersionName
    testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    multiDexEnabled true
    resConfigs "en", "te"
    ndk {
      abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86_64'
    }
  }

  compileOptions {
    // Flag to enable support for the new language APIs
    coreLibraryDesugaringEnabled true
  }

  signingConfigs {
    debug {
      keyAlias debugKeystoreProperties['keyAlias']
      keyPassword debugKeystoreProperties['keyPassword']
      storeFile file(debugKeystoreProperties['storeFile'])
      storePassword debugKeystoreProperties['storePassword']
    }
    release {
      keyAlias keystoreProperties['keyAlias']
      keyPassword keystoreProperties['keyPassword']
      storeFile file(keystoreProperties['storeFile'])
      storePassword keystoreProperties['storePassword']
    }
  }

  buildTypes {
    debug {
      signingConfig signingConfigs.debug
      manifestPlaceholders = [truecallerPartnerKey: "1iS8X0b3559d0edc4428da8852ac891d90594"]
    }
    profile {
      signingConfig signingConfigs.debug
      manifestPlaceholders = [truecallerPartnerKey: "1iS8X0b3559d0edc4428da8852ac891d90594"]
      matchingFallbacks = ['release']
      debuggable false
    }
    release {
      signingConfig signingConfigs.release

      // Enables code shrinking, obfuscation, and optimization for only
      // your project's release build type.
      minifyEnabled true

      // Enables resource shrinking, which is performed by the
      // Android Gradle plugin.
      shrinkResources true

      // Includes the default ProGuard rules files that are packaged with
      // the Android Gradle plugin. To learn more, go to the section about
      // R8 configuration files.
      // useProguard true
      proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

      manifestPlaceholders = [truecallerPartnerKey: "uuTIV78b8219f44754421bc344ebfe2162ca8"]
    }
  }

  java {
    toolchain.languageVersion.set(JavaLanguageVersion.of(21))
  }

  kotlin {
    jvmToolchain {
      languageVersion = JavaLanguageVersion.of(21)
    }
  }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20"
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test:runner:1.3.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'

    //Singular
//    implementation 'com.google.android.gms:play-services-ads:20.6.0'
    implementation("com.google.android.gms:play-services-ads-identifier:18.0.1")
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'com.android.installreferrer:installreferrer:2.2'

    implementation 'com.google.firebase:firebase-messaging:23.1.2'
    implementation 'androidx.lifecycle:lifecycle-process:2.6.1'
    implementation("androidx.core:core-ktx:1.10.1")
    implementation("androidx.appcompat:appcompat:1.6.1")
    implementation 'com.truecaller.android.sdk:truecaller-sdk:2.7.0'

    //light_compressor for video compression
    implementation 'com.github.AbedElazizShe:LightCompressor:1.2.3'

    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4")

    implementation("com.squareup.moshi:moshi-kotlin:1.14.0")
    ksp("com.squareup.moshi:moshi-kotlin-codegen:1.14.0")

    implementation("com.squareup.okhttp3:okhttp:4.11.0")

    implementation "androidx.work:work-runtime-ktx:2.8.1"
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.3'
}
