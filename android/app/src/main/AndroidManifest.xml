<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:tools="http://schemas.android.com/tools"
	package="buzz.praja.app">

<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.FLASHLIGHT" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.READ_CONTACTS" />
<uses-permission android:name="android.permission.WRITE_CONTACTS" />
<uses-permission android:name="android.permission.VIBRATE" />
<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

<!-- Singular -->
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="BIND_GET_INSTALL_REFERRER_SERVICE" />
<uses-permission android:name="com.android.vending.CHECK_LICENSE" />
<uses-permission android:name="com.google.android.gms.permission.AD_ID" />

<queries>

	<!-- If your app opens https URLs -->
	<intent>
		<action android:name="android.intent.action.VIEW" />

		<data android:scheme="https" />
	</intent>
	<!-- If your app makes calls -->
	<intent>
		<action android:name="android.intent.action.DIAL" />

		<data android:scheme="tel" />
	</intent>
	<!-- If your sends SMS messages -->
	<intent>
		<action android:name="android.intent.action.SENDTO" />

		<data android:scheme="smsto" />
	</intent>
	<!-- If your app sends emails -->
	<intent>
		<action android:name="android.intent.action.SEND" />

		<data android:mimeType="*/*" />
	</intent>
</queries>

<application
	android:name=".MainApp"
	android:allowBackup="false"
	android:extractNativeLibs="true"
	android:fullBackupContent="false"
	android:icon="@mipmap/ic_launcher"
	android:label="Praja"
	android:requestLegacyExternalStorage="true">
<profileable
	android:shell="true"
	tools:targetApi="29" />

<meta-data
	android:name="com.google.firebase.messaging.default_notification_icon"
	android:resource="@drawable/ic_stat_onesignal_default" /> 
<meta-data
	android:name="com.truecaller.android.sdk.PartnerKey"
	android:value="${truecallerPartnerKey}" />

<!-- Branch init -->
<meta-data
	android:name="io.branch.sdk.BranchKey"
	android:value="key_live_kfP6jqGbRze2jzFjPGggrfabBqgBFpXg" />
<meta-data
	android:name="io.branch.sdk.TestMode"
	android:value="false" /> <!-- Facebook SDK -->
<meta-data
	android:name="com.facebook.sdk.ApplicationId"
	android:value="@string/facebook_app_id" />
<meta-data
	android:name="com.facebook.sdk.ClientToken"
	android:value="@string/facebook_client_token"/>

<activity
	android:name="com.yalantis.ucrop.UCropActivity"
	android:screenOrientation="portrait"
	android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
<activity
	android:name=".MainActivity"
	android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
	android:exported="true"
	android:hardwareAccelerated="true"
	android:launchMode="singleTask"
	android:theme="@style/LaunchTheme"
	android:windowSoftInputMode="adjustResize">

<!--
	Specifies an Android theme to apply to this Activity as soon as
	the Android process has started. This theme is visible to the user
	while the Flutter UI initializes. After that, this theme continues
	to determine the Window background behind the Flutter UI.
-->
<meta-data
	android:name="io.flutter.embedding.android.NormalTheme"
	android:resource="@style/NormalTheme" />
<!--
	Displays an Android View that continues showing the launch screen
	Drawable until Flutter paints its first frame, then this splash
	screen fades out. A splash screen is useful to avoid any visual
	gap between the end of Android's launch screen and the painting of
	Flutter's first frame.
-->
<!-- <meta-data android:name="io.flutter.embedding.android.SplashScreenDrawable" android:resource="@drawable/launch_background" /> -->

<intent-filter>
	<action android:name="android.intent.action.MAIN" />

	<category android:name="android.intent.category.LAUNCHER" />
</intent-filter>

<!-- Deep Links -->
<intent-filter>
	<action android:name="android.intent.action.VIEW" />

	<category android:name="android.intent.category.DEFAULT" />
	<category android:name="android.intent.category.BROWSABLE" />

	<data android:scheme="praja" />
</intent-filter>
<intent-filter>
	<action android:name="android.intent.action.VIEW" />

	<category android:name="android.intent.category.DEFAULT" />
	<category android:name="android.intent.category.BROWSABLE" />

	<data
		android:host="open"
		android:scheme="praja-app" />
	<data
		android:host="buzz.praja.app"
		android:scheme="praja-app" />
	<data
		android:host="open"
		android:scheme="praja" />
	<data
		android:host="buzz.praja.app"
		android:scheme="praja" />
	<data
		android:host="open"
		android:scheme="praja-fAk8" />
	<data
		android:host="buzz.praja.app"
		android:scheme="praja-fAk8" />
</intent-filter>

<!-- App Links -->
<intent-filter android:autoVerify="true">
	<action android:name="android.intent.action.VIEW" />

	<category android:name="android.intent.category.DEFAULT" />
	<category android:name="android.intent.category.BROWSABLE" />

	<data
		android:host="m.praja.buzz"
		android:scheme="https" />
	<data
		android:host="m.praja.app"
		android:scheme="https" />
	<data
		android:host="praja.app.link"
		android:scheme="https" />
	<!-- example-alternate domain is required for App Links when the Journeys/Web SDK and Deepviews are used inside your website. -->
	<data
		android:host="praja-alternate.app.link"
		android:scheme="https" />
</intent-filter>
<intent-filter>
	<action android:name="FLUTTER_NOTIFICATION_CLICK" />

	<category android:name="android.intent.category.DEFAULT" />
</intent-filter>

<!-- Receive text share intent -->
<intent-filter>
	<action android:name="android.intent.action.SEND" />

	<category android:name="android.intent.category.DEFAULT" />

	<data android:mimeType="text/plain" />
</intent-filter>

<!-- Receive images share intent -->
<intent-filter>
	<action android:name="android.intent.action.SEND" />

	<category android:name="android.intent.category.DEFAULT" />

	<data android:mimeType="image/*" />
</intent-filter>
<intent-filter>
	<action android:name="android.intent.action.SEND_MULTIPLE" />

	<category android:name="android.intent.category.DEFAULT" />

	<data android:mimeType="image/*" />
</intent-filter>

<!-- Receive video share intent -->
<intent-filter>
	<action android:name="android.intent.action.SEND" />

	<category android:name="android.intent.category.DEFAULT" />

	<data android:mimeType="video/*" />
</intent-filter>
				</activity>

				<!--
					Don't delete the meta-data below.
					This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
				-->
				<meta-data
					android:name="flutterEmbedding"
					android:value="2" />

				<!--
				  MoEngage was removed after version 2502.20.xx, and notifications had already stopped long before this release.
					FCM Notifications worked all through out these changes
				-->
				<!-- Remove FlutterFirebaseMessagingService and register our FCMService -->
				<service
					android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
					tools:node="remove"
					/>
				<service android:name="buzz.praja.app.PrajaFCMService"
					android:exported="false">
				<intent-filter>
					<action android:name="com.google.firebase.MESSAGING_EVENT"/>
				</intent-filter>
			</service>

			<!-- Trigger Google Play services to install the backported photo picker module. -->
			<service android:name="com.google.android.gms.metadata.ModuleDependencies"
				android:enabled="false"
				android:exported="false"
				tools:ignore="MissingClass">
			<intent-filter>
				<action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
			</intent-filter>
			<meta-data android:name="photopicker_activity:0:required" android:value="" />
		</service>

		<!-- Remove FlutterFirebaseMessagingService and register our FCMService -->
		<receiver
			android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
			tools:node="remove" />
		<receiver
			android:name="buzz.praja.app.PrajaFCMBroadcastReceiver"
			android:exported="true"
			android:permission="com.google.android.c2dm.permission.SEND">
		<intent-filter>
			<action android:name="com.google.android.c2dm.intent.RECEIVE" />
		</intent-filter>
	</receiver>

	<provider
		android:name="androidx.core.content.FileProvider"
		android:authorities="${applicationId}.provider"
		android:exported="false"
		android:grantUriPermissions="true">
	<meta-data
		android:name="android.support.FILE_PROVIDER_PATHS"
		android:resource="@xml/file_paths" />
</provider>
<provider
    android:name="vn.hunghd.flutterdownloader.DownloadedFileProvider"
    android:authorities="${applicationId}.flutter_downloader.provider"
    android:exported="false"
    android:grantUriPermissions="true">
    <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/provider_paths"/>
</provider>
		</application>

	</manifest>
