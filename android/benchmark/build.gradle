plugins {
    id 'com.android.test'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'com.praja.benchmark'
    compileSdk 32

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }

    defaultConfig {
        minSdk 24
        targetSdk 32

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        // This benchmark buildType is used for benchmarking, and should function like your
        // release build (for example, with minification on). It's signed with a debug key
        // for easy local/CI testing.
        benchmark {
            debuggable = true
            signingConfig = debug.signingConfig
            matchingFallbacks = ["release"]
        }
    }

    targetProjectPath = ":app"
    experimentalProperties["android.experimental.self-instrumenting"] = true
}

dependencies {
    implementation 'androidx.test.ext:junit:1.1.3'
    implementation 'androidx.test.espresso:espresso-core:3.4.0'
    implementation 'androidx.test.uiautomator:uiautomator:2.2.0'
    implementation 'androidx.benchmark:benchmark-macro-junit4:1.1.0-beta05'
}

androidComponents {
    beforeVariants(selector().all()) {
        enabled = buildType == "benchmark"
    }
}