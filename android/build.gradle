allprojects {
    ext {
      // juspay client id / merchant id
      clientId = "praja"
    }
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    afterEvaluate{
        tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile) {
            if (project.plugins.hasPlugin("com.android.application") || project.plugins.hasPlugin("com.android.library")) {
                kotlinOptions.jvmTarget = android.compileOptions.sourceCompatibility
            } else {
                kotlinOptions.jvmTarget = sourceCompatibility
            }
        }
    }
}
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
    gradle.projectsEvaluated {
        tasks.withType(JavaCompile) {
            options.compilerArgs << "-Xlint:deprecation" << "-Xlint:unchecked"
        }
    }
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
