fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## Android

### android verify

```sh
[bundle exec] fastlane android verify
```

Builds the debug APK. TBA: testing

### android private_link_release

```sh
[bundle exec] fastlane android private_link_release
```

Builds release bundle and uploads it to the playstore for installing via link

### android internal_release

```sh
[bundle exec] fastlane android internal_release
```

Builds release bundle and uploads it to the internal channel in playstore

### android upload_build_to_slack

```sh
[bundle exec] fastlane android upload_build_to_slack
```

Builds and uploads a Debug APK for the environment

----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
