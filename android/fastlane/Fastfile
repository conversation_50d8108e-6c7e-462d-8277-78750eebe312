# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

require_relative '../../scripts/version'

default_platform(:android)

platform :android do
  desc 'Builds the debug APK. TBA: testing'
  lane :verify do |options|
    lint
    build(type: 'debug')
    version_name = extract_version
    slack(
      message: "Android v-#{version_name} Debug build, Lint successful",
      slack_url: ENV['SLACK_WEBHOOK_URL'],
      payload: {
        'Version' => version_name,
        'Pull Request' => "github.com/praja/app/pull/#{options[:pr_number]}",
        'Author' => options[:actor]
      }
    )
  end

  desc 'Builds release bundle and uploads it to the playstore for installing via link'
  lane :private_link_release do |options|
    version_name = extract_version
    build(type: 'release')
    upload_mappings_to_firebase
    install_link = upload_to_play_store_internal_app_sharing(
      aab: '../build/app/outputs/bundle/release/app-release.aab',
      json_key: ENV['PLAY_STORE_JSON_KEY_PATH']
    )
    slack(
      message: "Android v-#{version_name} released for testing via link",
      slack_url: ENV['SLACK_WEBHOOK_URL'],
      payload: {
        'version' => version_name,
        'from' => options[:ref],
        'install_link' => install_link
      }
    )
  end

  desc 'Builds release bundle and uploads it to the internal channel in playstore'
  lane :internal_release do |options|
    version_name = extract_version
    build(type: 'release')
    upload_mappings_to_firebase

    current_internal_version_codes = google_play_track_version_codes(
      track: 'internal',
      json_key: ENV['PLAY_STORE_JSON_KEY_PATH']
    )

    latest_internal_version_code = current_internal_version_codes.max
    version_code_to_upload = version_name.gsub('.', '').to_i

    if version_code_to_upload > latest_internal_version_code
      upload_to_play_store(
        aab: '../build/app/outputs/bundle/release/app-release.aab',
        json_key: ENV['PLAY_STORE_JSON_KEY_PATH'],
        track: 'internal'
      )
    else
      upload_to_play_store(
        aab: '../build/app/outputs/bundle/release/app-release.aab',
        json_key: ENV['PLAY_STORE_JSON_KEY_PATH'],
        track: 'production',
        release_status: 'draft'
      )
    end

    slack(
      message: "Android v-#{version_name} released for Internal Testing",
      slack_url: ENV['SLACK_WEBHOOK_URL'],
      payload: {
        'version' => version_name,
        'from' => options[:ref]
      }
    )
  end

  desc 'Builds and uploads a Debug APK for the environment'
  lane :upload_build_to_slack do |options|
    build(type: 'profile', env: options[:env], local_ip: options[:local_ip], local_ror_ngrok_url: options[:local_ror_ngrok_url], local_messaging_ngrok_url: options[:local_messaging_ngrok_url])
    # PWD $ROOT/android/fastlane
    upload_to_slack(
      title: "Android v-#{extract_version} Profile #{options[:env]} build",
      path: '../../build/app/outputs/flutter-apk/app-profile.apk',
      file_name: 'app-profile.apk',
      actor: options[:actor],
      ref: options[:ref],
      sha: options[:sha],
      env: options[:env],
    )
  end

  private_lane :upload_mappings_to_firebase do
    # PWD $ROOT/android/fastlane
    Dir.chdir('../../') do
      # Needs firebase CLI to be already installed / setup
      sh 'firebase crashlytics:symbols:upload --app=1:922743680415:android:2e9a2b4b32e33812e3e1aa ./build/app/outputs/symbols'
    end
  end

  private_lane :build do |options|
    install_deps
    Dir.chdir('../../') do
      if options[:type] == 'debug'
        command = 'fvm flutter build apk --debug'
      elsif options[:type] == 'profile'
        command = 'fvm flutter build apk --profile'
        command += ' --dart-define INCLUDE_DEVELOPER_OPTIONS=true'
      else
        command = 'fvm flutter build appbundle --release --obfuscate --split-debug-info=build/app/outputs/symbols'
      end

      if %w[debug profile].include? options[:type]
        command += " --dart-define ENVIRONMENT=#{options[:env]}" if options[:env]
        command += " --dart-define LOCAL_IP=#{options[:local_ip]}" if options[:local_ip]
        command += " --dart-define LOCAL_ROR_NGROK_URL=#{options[:local_ror_ngrok_url]}" if options[:local_ror_ngrok_url]
        command += " --dart-define LOCAL_MESSAGING_NGROK_URL=#{options[:local_messaging_ngrok_url]}" if options[:local_messaging_ngrok_url]
      end
      sh command
    end
  end

  private_lane :install_deps do
    Dir.chdir('../../') do
      sh 'fvm flutter pub get'
    end
  end

  private_lane :lint do
    install_deps
    # PWD $ROOT/android/fastlane
    Dir.chdir('../../') do
      sh 'fvm flutter analyze --no-fatal-infos --no-fatal-warnings'
    end
  end

  private_lane :upload_to_slack do |options|
    uri = URI('https://media-service-api.thecircleapp.in/slack/upload-file')
    request = Net::HTTP::Post.new(uri)
    request['Authorization'] = "Bearer #{ENV['CI_TO_MEDIA_SERVICE_TOKEN']}"
    form_data = [
      ['file', File.open(options[:path]), { filename: options[:file_name] }],
      # https://app.slack.com/client/TJSSQBX2M/C04Q6DXMQ8Y - Web link for #app-builds
      # channelId is the last part of the path segment in the web url of the channel
      %w[channelId C04Q6DXMQ8Y],
      ['comment', "Triggered by #{options[:actor]}\n*#{options[:env].upcase}* • #{options[:ref]} • `#{options[:sha][0..6]}`"],
    ]
    request.set_form form_data, 'multipart/form-data'

    Net::HTTP.start(uri.hostname, uri.port, use_ssl: true) do |http|
      response = http.request(request)
      raise "Upload to Slack failed with #{response.code}" unless response.is_a? Net::HTTPSuccess
    end
  end

  error do |lane, exception, options|
    slack(
      message: "Android Lane - #{lane} failed",
      success: false,
      slack_url: ENV['SLACK_WEBHOOK_URL'],
      payload: {
        'Lane' => lane,
        'Triggered By' => options[:actor],
        'Triggered On' => options[:ref],
        exception => exception
      }
    )
  end
end

def extract_version
  File.open('../../pubspec.yaml', 'r') do |file|
    file.each_line do |line|
      version = Line.new(line).parse
      return version.version_name if version
    end
  end
end
