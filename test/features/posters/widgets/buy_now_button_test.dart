// import 'package:flutter/material.dart';
// import 'package:flutter_test/flutter_test.dart';
//TODO: Remove This Test
//
// void main() {
//   group("Widget Testing", () {
//     testWidgets("Expect Buy Now Button", (widgetTester) async {
//       await widgetTester.pumpWidget(
//         MaterialApp(
//           home: BuyNowButton(
//             onNavigateToFreeLayout: () {},
//             analyticsParams: const {},
//           ),
//         ),
//       );
//
//       expect(find.text("కొనుగోలు చేయండి"), findsOneWidget);
//     });
//
//     testWidgets("Expect Button has Border Radius", (widgetTester) async {
//       await widgetTester.pumpWidget(
//         MaterialApp(
//           home: BuyNowButton(
//             onNavigateToFreeLayout: () {},
//             analyticsParams: const {},
//           ),
//         ),
//       );
//
//       await widgetTester.pumpAndSettle();
//       final button = find.byKey(const Key("buy_now_button"));
//       final buttonWidget = widgetTester.widget<ElevatedButton>(button);
//       var shape = buttonWidget.style?.shape?.resolve(<MaterialState>{});
//       expect(shape, isA<RoundedRectangleBorder>());
//       expect((shape as RoundedRectangleBorder).borderRadius,
//           BorderRadius.circular(4));
//     });
//
//     testWidgets("No Navigation on Subscription Screen Null",
//         (widgetTester) async {
//       await widgetTester.pumpWidget(
//         MaterialApp(
//           home: BuyNowButton(
//             onNavigateToFreeLayout: () {},
//             analyticsParams: const {},
//           ),
//         ),
//       );
//
//       final button = find.byKey(const Key("buy_now_button"));
//       await widgetTester.tap(button);
//       await widgetTester.pumpAndSettle();
//       expect(find.text("కొనుగోలు చేయండి"), findsOneWidget);
//     });
//   });
// }
