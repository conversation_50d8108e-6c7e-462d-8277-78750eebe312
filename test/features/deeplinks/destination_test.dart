import 'package:flutter_test/flutter_test.dart';
import 'package:praja/features/deeplinks/destination.dart';
import 'package:praja/features/deeplinks/deeplinks.dart';
import 'package:praja/models/circle.dart';

void main() {
  test('create post deeplink', () {
    const route = '/posts/create';

    assert(DeeplinkDestination.fromRoute(route) is CreatePostDeeplink);
  });

  group('post deeplink', () {
    test('with id', () {
      const route = '/posts/26780';

      final deeplink = DeeplinkDestination.fromRoute(route);

      expect(deeplink is PostDeeplink, true);
      expect((deeplink as PostDeeplink).id, 26780);
    });

    test('with hashId', () {
      const routeWithHashId = '/posts/abc-123-xyz';

      final deeplinkWithHashId = DeeplinkDestination.fromRoute(routeWithHashId);

      expect(deeplinkWithHashId is PostDeeplink, true);
      expect((deeplinkWithHashId as PostDeeplink).hashId, 'abc-123-xyz');
    });
  });

  group('post detail deeplink', () {
    test('with id', () {
      const route = '/posts/26780/detail';

      final deeplink = DeeplinkDestination.fromRoute(route);

      expect(deeplink is PostDetailDeeplink, true);
      expect((deeplink as PostDetailDeeplink).id, 26780);
    });

    test('with hashId', () {
      const routeWithHashId = '/posts/abc-123-xyz/detail';

      final deeplinkWithHashId = DeeplinkDestination.fromRoute(routeWithHashId);

      expect(deeplinkWithHashId is PostDetailDeeplink, true);
      expect((deeplinkWithHashId as PostDetailDeeplink).hashId, 'abc-123-xyz');
    });
  });

  test('login deeplink', () {
    const route = '/login';
    const alternateRoute = '/login/';

    assert(DeeplinkDestination.fromRoute(route) is LoginPageDeeplink);
    assert(DeeplinkDestination.fromRoute(alternateRoute) is LoginPageDeeplink);
  });

  test('contacts deeplink', () {
    const route = '/contacts';

    assert(DeeplinkDestination.fromRoute(route) is FollowContactsDeeplink);
  });

  test('my circles deeplink', () {
    const route = '/circles/mycircles';

    assert(DeeplinkDestination.fromRoute(route) is MyCirclesDeeplink);
  });

  test('suggested circles deeplink', () {
    const route = '/circles/suggested';

    assert(DeeplinkDestination.fromRoute(route) is SuggestedCirclesDeeplink);
  });

  test('notifications deeplink', () {
    const route = '/notifications';

    assert(DeeplinkDestination.fromRoute(route) is NotificationsTabDeeplink);
  });

  test('my profile deeplink', () {
    const route = '/my-profile';

    assert(DeeplinkDestination.fromRoute(route) is MyProfileDeeplink);
  });

  group('circle deeplinks', () {
    test('id, default tab', () {
      const route = '/circles/123';

      final deeplink = DeeplinkDestination.fromRoute(route);

      assert(deeplink is CircleDeeplink);
      assert((deeplink as CircleDeeplink).id == 123);
      expect((deeplink as CircleDeeplink).initialTab, null);
    });

    test('id, members tab', () {
      const route = '/circles/123/members';

      final deeplink = DeeplinkDestination.fromRoute(route);

      assert(deeplink is CircleDeeplink);
      assert((deeplink as CircleDeeplink).id == 123);
      assert((deeplink as CircleDeeplink).initialTab == CircleTabs.members);
    });

    test('id, info tab', () {
      const route = '/circles/123/info';

      final deeplink = DeeplinkDestination.fromRoute(route);

      assert(deeplink is CircleDeeplink);
      assert((deeplink as CircleDeeplink).id == 123);
      assert((deeplink as CircleDeeplink).initialTab == CircleTabs.info);
    });

    test('id, posts tab', () {
      const route = '/circles/123/posts';

      final deeplink = DeeplinkDestination.fromRoute(route);

      assert(deeplink is CircleDeeplink);
      assert((deeplink as CircleDeeplink).id == 123);
      assert((deeplink as CircleDeeplink).initialTab == CircleTabs.posts);
    });

    test('hash id, default tab', () {
      const route = '/circles/abc-123-xyz';

      final deeplink = DeeplinkDestination.fromRoute(route);

      assert(deeplink is CircleDeeplink);
      assert((deeplink as CircleDeeplink).hashId == 'abc-123-xyz');
      expect((deeplink as CircleDeeplink).initialTab, null);
    });

    test('hash id, members tab', () {
      const route = '/circles/abc-123-xyz/members';

      final deeplink = DeeplinkDestination.fromRoute(route);

      assert(deeplink is CircleDeeplink);
      assert((deeplink as CircleDeeplink).hashId == 'abc-123-xyz');
      assert((deeplink as CircleDeeplink).initialTab == CircleTabs.members);
    });

    test('hash id, info tab', () {
      const route = '/circles/abc-123-xyz/info';

      final deeplink = DeeplinkDestination.fromRoute(route);

      assert(deeplink is CircleDeeplink);
      assert((deeplink as CircleDeeplink).hashId == 'abc-123-xyz');
      assert((deeplink as CircleDeeplink).initialTab == CircleTabs.info);
    });

    test('hash id, posts tab', () {
      const route = '/circles/abc-123-xyz/posts';

      final deeplink = DeeplinkDestination.fromRoute(route);

      assert(deeplink is CircleDeeplink);
      assert((deeplink as CircleDeeplink).hashId == 'abc-123-xyz');
      assert((deeplink as CircleDeeplink).initialTab == CircleTabs.posts);
    });
  });

  group('hashtag deeplink', () {
    test('hashtag deeplink', () {
      const route = '/hashtags/123';

      final deeplink = DeeplinkDestination.fromRoute(route);

      assert(deeplink is HashtagDeeplink);
      assert((deeplink as HashtagDeeplink).id == 123);
    });

    test('with hashId', () {
      const hashIdRoute = '/hashtags/abc-123-xyz';

      final hashIdDeeplink = DeeplinkDestination.fromRoute(hashIdRoute);

      assert(hashIdDeeplink is HashtagDeeplink);
      assert((hashIdDeeplink as HashtagDeeplink).hashId == 'abc-123-xyz');
    });
  });

  group('users deeplink', () {
    test('with id', () {
      const route = '/users/123';

      final deeplink = DeeplinkDestination.fromRoute(route);

      assert(deeplink is UserDeeplink);
      assert((deeplink as UserDeeplink).id == 123);
    });

    test('with hashId', () {
      const hashRoute = '/users/abc-123-xyz';

      final hashDeeplink = DeeplinkDestination.fromRoute(hashRoute);

      assert(hashDeeplink is UserDeeplink);
      assert((hashDeeplink as UserDeeplink).hashId == 'abc-123-xyz');
    });
  });

  test('chat deeplink', () {
    const route = '/conversations/abc-123-xyz';

    final deeplink = DeeplinkDestination.fromRoute(route);

    assert(deeplink is ChatDeeplink);
    assert((deeplink as ChatDeeplink).id == 'abc-123-xyz');
  });

  test('posters payment deeplink', () {
    const successRoute = '/posters/payment_success';
    const failureRoute = '/posters/payment_failure';

    assert(
        DeeplinkDestination.fromRoute(successRoute) is PostersPaymentDeeplink);
    assert(
        DeeplinkDestination.fromRoute(failureRoute) is PostersPaymentDeeplink);
  });

  test('posters tab deeplink', () {
    const route = '/posters';

    assert(DeeplinkDestination.fromRoute(route) is PostersTabDeeplink);
  });

  group('posters layout deeplink', () {
    test('with title', () {
      const route = '/posters/layout?title=Test&description=Test&price=1.0';

      assert(DeeplinkDestination.fromRoute(route) is PostersFeedViewDeeplink);
      expect(
          (DeeplinkDestination.fromRoute(route) as PostersFeedViewDeeplink)
              .params,
          {"title": "Test", "description": "Test", "price": "1.0"});
    });

    test('without title', () {
      const routeWithoutTitle = '/posters/layout?description=Test&price=1.0';

      assert(DeeplinkDestination.fromRoute(routeWithoutTitle)
          is PostersFeedViewDeeplink);
      expect(
          (DeeplinkDestination.fromRoute(routeWithoutTitle)
                  as PostersFeedViewDeeplink)
              .params,
          {"description": "Test", "price": "1.0"});
    });
  });
}
