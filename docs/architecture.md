# Architecture

## Components
```mermaid
flowchart LR
  Widgets[Widgets]
  ViewModel[ViewModel]
  Service[Service]
  Store[Store]
  API[API]

  Widgets --> |User Inputs| ViewModel
  ViewModel --> |State| Widgets
  ViewModel --> |Request| Service
  Service --> |Response| ViewModel
  Service --> |Device Data Request| Store
  Store --> |Device Data Response| Service
  Service --> |API Request| API
  API --> |API Response| Service

  %% Colors based on Scopes
  style Widgets stroke:#d77;
  style ViewModel stroke:#eaa;
```

### Widgets
**Scope**: UI
* All our widgets are expected to be `StatelessWidget`s by default.
* State is expected to be contained and managed in `ViewModel`s

### ViewModel
The only layer that UI talks to. Allows the UI to be as dumb as possible while allowing us to unit test our UI State logic using `ViewModel`s.

**Scope**: UI (Page Widget)
**Responsibility**: Manage the state of it's corresponding UI

* A `ViewModel` is only referenced by UI. No other layer knows about any `ViewModel`
* A `ViewModel` should never be referenced by other `ViewModel`s
* No inheritance in `ViewModel`s. Use composition to share logic across `ViewModel`. (Services)

### Service
`Service`s abstract away various operations within the app. In essence `Service`s are the **backend** to the UI(frontend) of the app. `ViewModel`s are the bridges between the frontend and the backend.

**Scope**: `@Singleton` if has state

* `Service`s are typically stateless
* No inheritance across `Service`s. Use composition to share common code / state.

Ex usage: `userService.requestOtp()`, `notificationService.markAllAsRead()`

### Store (Singleton)
Abstracts away persistence of data. `Service`s and `ViewModel`s don't need to know about where and how a particular data is stored on the device - shared preferences, sqlite, hive or anything else.

**Scope**: Always `@singleton`

* Prefer supporting `listener`s to data or expose `Stream<>`s

Ex usage: `userStore.stream()`, `userStore.update(user)`, `userStore.clear()`

## Understanding Scope
Requires [understanding Garbage Collection](https://www.youtube.com/watch?v=Mlbyft_MFYM)

When working on an Object Oriented Program, the lifecycle of all the objects that we are controlling and interacting with, are very key aspects to keep in mind. i.e when is an object created, and when is it dead (no longer referenced by any living objects of the program).

If an object A, outlives the object B, then we should be extra careful in setting/clearing references of B in A.

There are two kinds of scopes for objects in a Flutter application.
```mermaid
flowchart LR

  UI[UI]
  Other[Other]

  UI -.- Other

  %% Colors based on Scopes
  style UI stroke:#d77;
```
### UI Scope: Widgets
**Birth**: In their parent widget's `build` function
**Death**: In their parent widget's or their parent widget's `build` function

Consider this widget hierarchy in an App. When Page1 was in a particular state, it shows widget - A
```mermaid
flowchart TD

  Root[Root]
  Page1[Page1]
  A[A]

  Root --> Page1
  Page1 --> A
```

and when the state changes, it shows widget - B
```mermaid
flowchart TD

  Root[Root]
  Page1[Page1]
  A[A]

  Root --> Page1
  Page1 --> B
```
And just with that state change, an instance widget - A just died (no references).

A page change via `Navigator.of(context).push(Page2)` is just a state change at the root of the app. If a page is no longer in the stack, the entire page is ready to be collected by the garbage.

Who invokes `build` function? Flutter Framework. We can request the framework to re-build in a `StatefulWidget` by calling `setState` but it is totally up to the Flutter Framework to decide if and when to invoke the `build` methods of any Widget.

Since the lifecycle of a `Widget` is **not in our control**, we should be extremely careful, passing the references of `Widget`s or their `BuildContext`s to any other object that could potentially outlive the `Widget`.

**How to be careful?**
1. Avoid passing references of `Widget` or `BuildContext` to any other class unless otherwise necessary
  This typically happens via `.addListener()`, `Stream.subscribe()` etc.
2. In cases that you have to pass the reference of a UI Scope object, make sure you are inside a `State<StatefulWidget>` class and use the `dispose()` method to cleanup
  `.removeListener()` / `_subscription?.cancel()`

### `ViewModel`

Technically `ViewModel`s are also just `UI` Scope, but at an individual `Page` level. Within each `Page` widget we have
```dart
Widget build(BuildContext context) {
  return ViewModelScope(builder: (ctx) => pageContent(ctx));
}
```

**Birth**: In the root widget's `build` function.
When `context.getViewModel<MyViewModel>()` is invoked for the first time within a page
**Death**: In the root widget's `build` function.
When the page is removed from the stack via back button press or `Navigator.of(context).pop()` or `.replace()`

Why is it safe to interact with `Other` scope inside a `ViewModel` and not `Widget`? It isn't any safer, the same rules to be *careful* and cleanup apply for `ViewModel`s as well.

`ViewModel`s don't have as complicated a lifecycle as `StatefulWidget`s. They get created on the first invocation of `context.getViewModel<MyViewModel>()` within a page and subsequent invocations of the same just return the same instance again and again, until the Page is popped. This simple lifecycle makes them better candidates to be the orchestrator of application. Holding all the state and it's associated logic to correctly modify the state.

`ViewModel`s don't talk to the `UI` directly. They just update their state in `LiveData`. If there is any `Widget` listening to that `LiveData` via `LiveDataBuilder` then it will get the updates, otherwise it doesn't. Whether a widget is alive listening to the `LiveData` or not doesn't make any difference to the `ViewModel` and that relieves a lot of complexity.

```mermaid
sequenceDiagram

  participant Widget
  participant LiveData
  participant ViewModel

  Widget->>ViewModel: User Action
  ViewModel->>LiveData: State A
  LiveData->>Widget: State A
  Widget->>ViewModel: User Action
  activate ViewModel
  note over ViewModel: Timetaking work
  note over Widget: Removed from Widget tree
  Widget-->LiveData: Stops observing
  ViewModel->>LiveData: State B
  deactivate ViewModel
  note over LiveData: Nothing happens
  note over Widget: Attached to Widget tree
  Widget-->LiveData: Starts observing again
  LiveData->>Widget: State B
```

## Models
Always immutable.

**Why?**  
Avoids a whole class of bugs due to race conditions etc. The resultant code is a lot simpler to reason about.

A response received from server will always represent the response that was received from server no matter what operations were performed afterwards.

Value classes like `String` are immutable in almost all of the languages for the same reason.

Always have Immutable Data Classes with mutable references when required.

[The Value of Values by Rich Hickey](https://www.youtube.com/watch?v=-6BsiVyC1kM)

**Why should we add required for all fields in the constructor?**

Let’s say we have a class with a `copyWith` method. Normally, when using copyWith, we pass the values we want to change, and the rest of the fields should keep their existing values.

But if any of the fields in the constructor are not marked as required, they become optional. This means:
•	In the copyWith method, if you don’t pass a value for that field, it could turn null instead of keeping its existing value.
•	This can cause unexpected behavior because the field becomes null even though you expected it to stay the same.

For example, if you’re using copyWith in your UI and passing only some fields, you expect the other fields to keep their current value. But if they aren’t required, they can accidentally become null.

To avoid this problem, it’s best to mark all fields as required in the constructor. This ensures that every time you create or copy the object, all fields must be provided, and you won’t accidentally lose values.

```dart
class Circle {
  final String id;
  final String name;
  final Photo? photo;

  Circle({
    required this.id,
    required this.name,
    required this.photo,
  });
}
```

### Create different models for full, partial domain objects
Ex: `User`, `UserDetails`, `PersistedUser`, `Circle`, `CircleDetails`, `Conversation`, `DBConversation`

## Full Example
```dart
class Circle {
  final String id;
  final String name;
  final Photo photo;
}

class CircleDetails {
  final String id;
  final String name;
  final Photo photo;
  final String englishName;
  final int membersCount;
  //...
}

@injectable
class CircleDetailsViewModel extends ViewModel {
  final CircleService circleService;

  CircleDetailsViewModel(this.circleService);

  MutableLiveData<Circle?> _circle = MutableLiveData(null);
  MutableLiveData<CircleDetails?> _circleDetails = MutableLiveData(null);

  LiveData<Circle?> get circle = _circle;
  LiveData<CircleDetail?> get circleDetails = _circleDetails;

  void _initialize(Circle circle) {
    if (circle.value == null) {
      circle.value = circle;

      _fetchDetails(circle.id);
    }
  }

  Future<void> _fetchDetails(String id) async {
    final var circleDetails = await circleService.getDetails(id);
    _circleDetails.value = circleDetails;
    _circle.value = circleDetails.toCircle();
  }
}

extension CircleDetailsVMFactory on BuildContext {
  CircleDetailsViewModel circleDetailsViewModel(Circle circle) {
    CircleDetailsViewModel viewModel = getViewModel();
    viewModel._intialize(circle);
    return viewModel;
  }
}

class CircleDetailsPage extends Page {
  Widget buildPage(BuildContext context) {
    var viewModel = context.circleDetailsViewModel();

    return Scaffold(
        header: LiveDataBuilder(
          liveData: viewModel.circle,
          builder: (_, value) => CircleHeader(value)
          ),
        body: LiveDataBuilder(
          liveData: viewModel.circleDetails,
          builder: (_, value) => CircleDetailsUI(value)
          ),
        );
  }
}
```

## Analytics
* Inject `AnalyticsService` into `*ViewModel` as dependency and fire events from `ViewModel`s
* Event names, property names should strictly be inside `AnalyticsService` and not exposed
* Send typed objects, enums as function parameters from `ViewModel` to `AnalyticsService`

## Migration
The above architecture is the **expected** architecture to be followed. But we have a lot of old code which uses `bloc`, `StatefulWidgets`, `static` methods and fields etc.

We expect everyone to migrate any old code that is being significantly modified to the above expected architecture.

### Expectations
* Existing code should continue to work
* All new code should adhere to above arch as much as possible basis effort

### Model Migration
* Rename `Circle` -> `OldCircle`
* Create `Circle` with immutability, (optionally `@JsonSerializable`)
* Add `Circle.toOldCircle()`, `extension CircleMapping on OldCirlce { Circle toCricle(){} }`

### ViewModel Migration from StatefulWidgets
* Create `*ViewModel`, move necessary code from `State<>` to `ViewModel`
* `extends StatefulWidget` -> `extends StatelessWidget`
* Connect UI in `build` method to `ViewModel` using callbacks, `LiveDataBuilder`, `LiveDataListener`, `EventListener`

Since the public API of the widget is not expected to change, so any usage need not change. If public API is affected, then update all usages.

### Service Migration from `static` methods
* Create `*Service` and annotate with `@Injectable` / `@Singleton` accordingly
* Move `static` methods as member functions into the `Service`
* In the old `static` method, refer to this Service's method using `GetIt`
```dart
// lib/models/user.dart
// old code pointing to the new service code
static Future<void> requestOtp(params) async {
  await GetIt.I.get<UserService>.requestOtp(params);
}

//----
// lib/services/user_service.dart

@Injectable
class UserService {
  UserService(/* dependencies */)

    Future<void> requestOtp(params) async {
      // implementation
    }
}
```
