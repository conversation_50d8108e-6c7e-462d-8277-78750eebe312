# Font Icons

We use fluttericon.com to generate the `.ttf` font and the corresponding `.dart` file

## How to add a new icon or update an existing icon
### Prerequisites
- [Install FontForge](https://fontforge.org/en-US/downloads/mac-dl/)

### Step 1
Open Font Forge
1. File -> Open -> Select the `.ttf` file in our codebase
2. File -> Generate Fonts -> Select SVG Font -> Save the `.svg` file somewhere in your downloads folder / desktop folder

![fontforge-ttf-to-svg](https://github.com/user-attachments/assets/08bf88c9-8852-47cb-a6d2-410f8877cc68)

### Step 2
Open fluttericon.com 
1. Drag the `.svg` file saved, to the website (Custom icons section)
2. Select all by long clicking on one icon and dragging

https://github.com/user-attachments/assets/ac70333f-9ced-4a6e-b4d9-dd74ccd12973

### Step 3
Add or update any icon as needed

### Step 4
Update the name beside the download button to `PrajaIcons` and Click on the download button

### Step 5
Update the `.ttf` file and the `.dart` file in the codebase using the ones in the `.zip` file that was downloaded
