PODS:
  - app_settings (5.1.1):
    - Flutter
  - app_tracking_transparency (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - contacts_service (0.2.2):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - facebook_app_events (0.0.1):
    - FBAudienceNetwork (~> 6.15)
    - FBSDKCoreKit (~> 17.0)
    - Flutter
  - FBAEMKit (17.4.0):
    - FBSDKCoreKit_Basics (= 17.4.0)
  - FBAudienceNetwork (6.15.2)
  - FBSDKCoreKit (17.4.0):
    - FBAEMKit (= 17.4.0)
    - FBSDKCoreKit_Basics (= 17.4.0)
  - FBSDKCoreKit_Basics (17.4.0)
  - Firebase/Analytics (10.22.0):
    - Firebase/Core
  - Firebase/Core (10.22.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.22.0)
  - Firebase/CoreOnly (10.22.0):
    - FirebaseCore (= 10.22.0)
  - Firebase/Crashlytics (10.22.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.22.0)
  - Firebase/Messaging (10.22.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.22.0)
  - Firebase/Performance (10.22.0):
    - Firebase/CoreOnly
    - FirebasePerformance (~> 10.22.0)
  - Firebase/RemoteConfig (10.22.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 10.22.0)
  - firebase_analytics (10.10.1):
    - Firebase/Analytics (= 10.22.0)
    - firebase_core
    - Flutter
  - firebase_core (2.29.0):
    - Firebase/CoreOnly (= 10.22.0)
    - Flutter
  - firebase_crashlytics (3.5.1):
    - Firebase/Crashlytics (= 10.22.0)
    - firebase_core
    - Flutter
  - firebase_messaging (14.8.1):
    - Firebase/Messaging (= 10.22.0)
    - firebase_core
    - Flutter
  - firebase_performance (0.9.4-1):
    - Firebase/Performance (= 10.22.0)
    - firebase_core
    - Flutter
  - firebase_remote_config (4.4.1):
    - Firebase/RemoteConfig (= 10.22.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseAnalytics (10.22.0):
    - FirebaseAnalytics/AdIdSupport (= 10.22.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.22.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.22.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseCore (10.22.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.22.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.22.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebasePerformance (10.22.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfig (~> 10.0)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/ISASwizzler (~> 7.8)
    - GoogleUtilities/MethodSwizzler (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseRemoteConfig (10.22.0):
    - FirebaseABTesting (~> 10.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - FirebaseSharedSwift (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseSessions (10.29.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (10.29.0)
  - Flutter (1.0.0)
  - flutter_downloader (0.0.1):
    - Flutter
  - flutter_facebook_app_links (3.0.0):
    - FBSDKCoreKit (~> 17.0)
    - Flutter
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_sound (9.4.6):
    - Flutter
    - flutter_sound_core (= 9.4.6)
  - flutter_sound_core (9.4.6)
  - flutter_vibrate (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - GoogleAppMeasurement (10.22.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.22.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.22.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.22.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.22.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - heif_converter (1.0.0):
    - Flutter
  - HyperSDK (2.1.43):
    - JuspaySafeBrowser (= 0.1.83)
    - Salvator (= 1.0.6)
  - hypersdkflutter (4.0.20):
    - Flutter
    - HyperSDK (= 2.1.43)
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.7.4)
  - image_gallery_saver (2.0.2):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_review (0.2.0):
    - Flutter
  - integration_test (0.0.1):
    - Flutter
  - JuspaySafeBrowser (0.1.83)
  - KeychainSwift (22.0.0)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - light_compressor (1.0.0):
    - Flutter
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - Mixpanel-swift (4.2.5):
    - Mixpanel-swift/Complete (= 4.2.5)
  - Mixpanel-swift/Complete (4.2.5)
  - mixpanel_flutter (2.3.1):
    - Flutter
    - Mixpanel-swift (= 4.2.5)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.1.1):
    - Flutter
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - quick_actions_ios (0.0.1):
    - Flutter
  - receive_sharing_intent (0.0.1):
    - Flutter
  - Salvator (1.0.6)
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - Singular-SDK (12.4.4):
    - Singular-SDK/Main (= 12.4.4)
  - Singular-SDK/Main (12.4.4)
  - singular_flutter_sdk (1.4.1):
    - Flutter
    - Singular-SDK (= 12.4.4)
  - smart_auth (0.0.1):
    - Flutter
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - store_checker (0.0.1):
    - Flutter
  - Toast (4.1.1)
  - TOCropViewController (2.7.4)
  - ULID.swift (1.2.0)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - video_thumbnail (0.0.1):
    - Flutter
    - libwebp
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_settings (from `.symlinks/plugins/app_settings/ios`)
  - app_tracking_transparency (from `.symlinks/plugins/app_tracking_transparency/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/darwin`)
  - contacts_service (from `.symlinks/plugins/contacts_service/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - facebook_app_events (from `.symlinks/plugins/facebook_app_events/ios`)
  - FBAudienceNetwork (~> 6.15.0)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_performance (from `.symlinks/plugins/firebase_performance/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - Flutter (from `Flutter`)
  - flutter_downloader (from `.symlinks/plugins/flutter_downloader/ios`)
  - flutter_facebook_app_links (from `.symlinks/plugins/flutter_facebook_app_links/ios`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_sound (from `.symlinks/plugins/flutter_sound/ios`)
  - flutter_vibrate (from `.symlinks/plugins/flutter_vibrate/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - heif_converter (from `.symlinks/plugins/heif_converter/ios`)
  - hypersdkflutter (from `.symlinks/plugins/hypersdkflutter/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_review (from `.symlinks/plugins/in_app_review/ios`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - KeychainSwift (~> 22.0.0)
  - light_compressor (from `.symlinks/plugins/light_compressor/ios`)
  - mixpanel_flutter (from `.symlinks/plugins/mixpanel_flutter/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - quick_actions_ios (from `.symlinks/plugins/quick_actions_ios/ios`)
  - receive_sharing_intent (from `.symlinks/plugins/receive_sharing_intent/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - singular_flutter_sdk (from `.symlinks/plugins/singular_flutter_sdk/ios`)
  - smart_auth (from `.symlinks/plugins/smart_auth/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - store_checker (from `.symlinks/plugins/store_checker/ios`)
  - ULID.swift (~> 1.2.0)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - video_thumbnail (from `.symlinks/plugins/video_thumbnail/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  https://github.com/CocoaPods/Specs.git:
    - FBAEMKit
    - FBAudienceNetwork
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebasePerformance
    - FirebaseRemoteConfig
    - FirebaseSessions
    - FirebaseSharedSwift
    - flutter_sound_core
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - HyperSDK
    - JuspaySafeBrowser
    - KeychainSwift
    - libwebp
    - Mantle
    - Mixpanel-swift
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - Salvator
    - SDWebImage
    - SDWebImageWebPCoder
    - Singular-SDK
    - Toast
    - TOCropViewController
    - ULID.swift

EXTERNAL SOURCES:
  app_settings:
    :path: ".symlinks/plugins/app_settings/ios"
  app_tracking_transparency:
    :path: ".symlinks/plugins/app_tracking_transparency/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/darwin"
  contacts_service:
    :path: ".symlinks/plugins/contacts_service/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  facebook_app_events:
    :path: ".symlinks/plugins/facebook_app_events/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_performance:
    :path: ".symlinks/plugins/firebase_performance/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  Flutter:
    :path: Flutter
  flutter_downloader:
    :path: ".symlinks/plugins/flutter_downloader/ios"
  flutter_facebook_app_links:
    :path: ".symlinks/plugins/flutter_facebook_app_links/ios"
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_sound:
    :path: ".symlinks/plugins/flutter_sound/ios"
  flutter_vibrate:
    :path: ".symlinks/plugins/flutter_vibrate/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  heif_converter:
    :path: ".symlinks/plugins/heif_converter/ios"
  hypersdkflutter:
    :path: ".symlinks/plugins/hypersdkflutter/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_review:
    :path: ".symlinks/plugins/in_app_review/ios"
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  light_compressor:
    :path: ".symlinks/plugins/light_compressor/ios"
  mixpanel_flutter:
    :path: ".symlinks/plugins/mixpanel_flutter/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  quick_actions_ios:
    :path: ".symlinks/plugins/quick_actions_ios/ios"
  receive_sharing_intent:
    :path: ".symlinks/plugins/receive_sharing_intent/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  singular_flutter_sdk:
    :path: ".symlinks/plugins/singular_flutter_sdk/ios"
  smart_auth:
    :path: ".symlinks/plugins/smart_auth/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  store_checker:
    :path: ".symlinks/plugins/store_checker/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  video_thumbnail:
    :path: ".symlinks/plugins/video_thumbnail/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  app_settings: 017320c6a680cdc94c799949d95b84cb69389ebc
  app_tracking_transparency: 5b1745ef9ade815f7455cb6a0848349589afb7c5
  connectivity_plus: ddd7f30999e1faaef5967c23d5b6d503d10434db
  contacts_service: 849e1f84281804c8bfbec1b4c3eedcb23c5d3eca
  device_info_plus: 97af1d7e84681a90d0693e63169a5d50e0839a0d
  facebook_app_events: acb7c266406d3e3592bcf846d7184172ecfd6492
  FBAEMKit: 58cb5f302cdd715a56d4c1d0dfdd2e423ac1421a
  FBAudienceNetwork: 2457a89b45e2596241fd81b0c2fd8cb6f6053869
  FBSDKCoreKit: 94d7461d0cecf441b1ba7c41acfff41daa8ccd41
  FBSDKCoreKit_Basics: 151b43db8b834d3f0e02f95d36a44ffd36265e45
  Firebase: 797fd7297b7e1be954432743a0b3f90038e45a71
  firebase_analytics: 8e6cecb9b9541db49ac40a46eec7c0f519de7252
  firebase_core: aaadbddb3cb2ee3792b9804f9dbb63e5f6f7b55c
  firebase_crashlytics: 4271b5bb77f6169ac7c2a9d62ad0e6aa5f84c2fe
  firebase_messaging: 860e28c6ad8b3281276eba922024644b9909c903
  firebase_performance: 0a7767812b911db8daa8c542d64f4cdc0c0d7cc1
  firebase_remote_config: 13cdfde7862e4cee3b9e97cf3f9e2f1ea2b98c8e
  FirebaseABTesting: d87f56707159bae64e269757a6e963d490f2eebe
  FirebaseAnalytics: 8d0ff929c63b7f72260f332b86ccf569776b75d3
  FirebaseCore: 0326ec9b05fbed8f8716cddbf0e36894a13837f7
  FirebaseCoreExtension: 705ca5b14bf71d2564a0ddc677df1fc86ffa600f
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseCrashlytics: e568d68ce89117c80cddb04073ab9018725fbb8c
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 9f71037fd9db3376a4caa54e5a3949d1027b4b6e
  FirebasePerformance: 095debad1fc8d7d73148a835fcaec9e528946166
  FirebaseRemoteConfig: e1b992a94d3674dddbcaf5d0d31a0312156ceb1c
  FirebaseSessions: dbd14adac65ce996228652c1fc3a3f576bdf3ecc
  FirebaseSharedSwift: 20530f495084b8d840f78a100d8c5ee613375f6e
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_downloader: b7301ae057deadd4b1650dc7c05375f10ff12c39
  flutter_facebook_app_links: f272e5476d6df8353fbd6459e0eea31fc133360c
  flutter_image_compress_common: ec1d45c362c9d30a3f6a0426c297f47c52007e3e
  flutter_keyboard_visibility: 0339d06371254c3eb25eeb90ba8d17dca8f9c069
  flutter_local_notifications: 4cde75091f6327eb8517fa068a0a5950212d2086
  flutter_sound: d1ed7b8bac3c4513ac5ef8b90c15f3a6b860291b
  flutter_sound_core: 46f6f56752a209dcc72be9f09b71c8b626f70d49
  flutter_vibrate: 9f4c2ab57008965f78969472367c329dd77eb801
  fluttertoast: 9f2f8e81bb5ce18facb9748d7855bf5a756fe3db
  GoogleAppMeasurement: ccefe3eac9b0aa27f96066809fb1a7fe4b462626
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  heif_converter: e3802659e4104e27e28c4d7bff07903da2f69318
  HyperSDK: fdcd5a244b85e296d02f3b13057015ef2a66e3f8
  hypersdkflutter: aeaa629e51cf19d5eab39e41b83eabce0ad6b1e2
  image_cropper: 37d40f62177c101ff4c164906d259ea2c3aa70cf
  image_gallery_saver: cb43cc43141711190510e92c460eb1655cd343cb
  image_picker_ios: 4a8aadfbb6dc30ad5141a2ce3832af9214a705b5
  in_app_review: 318597b3a06c22bb46dc454d56828c85f444f99d
  integration_test: 13825b8a9334a850581300559b8839134b124670
  JuspaySafeBrowser: 91be796164a40317d5a9153ac6d433a4928ef549
  KeychainSwift: 72f81155e1cb01c1bc4da296e4ce2cfcff556a12
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  light_compressor: 3f6f2c961a84fdf4b46b0c58488985469ee3317c
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  Mixpanel-swift: 1fb0322037fa4db64042ab8eb4430848bc90ea43
  mixpanel_flutter: 36d73b7a2502b5782cd8f1d1f6b1b4aa4f2cfd07
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  package_info_plus: 58f0028419748fad15bf008b270aaa8e54380b1c
  path_provider_foundation: 3784922295ac71e43754bd15e0653ccfd36a147c
  permission_handler_apple: e76247795d700c14ea09e3a2d8855d41ee80a2e6
  photo_manager: 4f6810b7dfc4feb03b461ac1a70dacf91fba7604
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  quick_actions_ios: 9e80dcfadfbc5d47d9cf8f47bcf428b11cf383d4
  receive_sharing_intent: c0d87310754e74c0f9542947e7cbdf3a0335a3b1
  Salvator: 1de69620ac17123ba3491e27a9a8b35bc5b97eaa
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  share_plus: 8875f4f2500512ea181eef553c3e27dba5135aad
  shared_preferences_foundation: b4c3b4cddf1c21f02770737f147a3f5da9d39695
  Singular-SDK: f0ef57c0d7a8356021280b5537a362db43af9904
  singular_flutter_sdk: 2e750bf16e57240ff7b09edf83909821a1d3703e
  smart_auth: 4bedbc118723912d0e45a07e8ab34039c19e04f2
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  store_checker: a7be624ac44a4ba88e7917e71afe8433f197161e
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  ULID.swift: 7dddbacf51b74c60742d0ce49b81cb11491f803f
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  video_player_avfoundation: 2b4384f3b157206b5e150a0083cdc0c905d260d3
  video_thumbnail: c4e2a3c539e247d4de13cd545344fd2d26ffafd1
  wakelock_plus: 78ec7c5b202cab7761af8e2b2b3d0671be6c4ae1
  webview_flutter_wkwebview: be0f0d33777f1bfd0c9fdcb594786704dbf65f36

PODFILE CHECKSUM: 3f6fb978b1e6d64f7bcd31394ac4e6baeda5b1d5

COCOAPODS: 1.15.2
